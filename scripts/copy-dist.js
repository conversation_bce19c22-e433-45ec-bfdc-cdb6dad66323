const shell = require("shelljs");
const path = require("path");
// 打包单个模块
const buildModule = (moduleName = "") => {
  if (!moduleName) {
    throw new Error("请指定打包模块");
  }

  // 输出打包模块
  console.log(`拷贝模块 ${moduleName}`);

  // 构建模块路径
  const modulePath = path.resolve(__dirname, `../packages/${moduleName}`);

  // 检查模块目录是否存在
  if (!shell.test("-e", modulePath)) {
    throw new Error(`模块 ${moduleName} 不存在`);
  }

  // 进入模块目录
  shell.cd(modulePath);

  // 如果根目录没有 dist 目录，则创建
  const distPath = path.resolve(__dirname, "../dist");
  if (!shell.test("-e", distPath)) {
    shell.mkdir(distPath);
  }

  // 如果模块目录没有 dist 目录，则抛出异常
  if (!shell.test("-e", path.resolve(modulePath, "dist"))) {
    // throw new Error(`模块 ${moduleName} 打包失败`);
    console.log(`模块 ${moduleName} 未找到dist，跳过`);
    return
  }

  // 打包完成后，复制 dist 到根目录的 dist
  shell.cp("-R", "dist", path.resolve(distPath, moduleName));

  // 清除模块目录的 dist
  shell.rm("-rf", "dist");
};

// 清除根目录的 dist
shell.rm("-rf", path.resolve(__dirname, "../dist"));

// 获取packages目录下的所有一级目录
const packagesPath = path.resolve(__dirname, "../packages");
const packages = shell.ls(packagesPath).filter(dir =>
  shell.test("-d", path.resolve(packagesPath, dir))
);

// 执行构建
const filterPackages = ['app-common'];
for (const packageName of packages) {
  !filterPackages.includes(packageName) && buildModule(packageName);
}
