const fs = require("fs");
const path = require("path");

/** 写入 .npmrc 配置 */
function writeNpmrc() {
  const npmrc = [
    "home=https://artifact.srdcloud.cn/artifactory/api/npm/public-npm-virtual/",
    "registry=https://artifact.srdcloud.cn/artifactory/api/npm/public-npm-virtual/",
  ].join("\n");

  fs.writeFileSync(".npmrc", npmrc);
}

/**
 * 写入 vscode settings.json
 */
function writeVSCodeSettings() {
  // 读取 packages 目录下的所有文件夹
  const packages = fs
    .readdirSync(path.resolve(__dirname, "../packages"))
    .filter((dir) => ![".DS_Store"].includes(dir))
    .map((dir) => `packages/${dir}`);

  const settings = {
    "eslint.useFlatConfig": false,
    "eslint.workingDirectories": packages,
  };

  if (!fs.existsSync(".vscode")) {
    fs.mkdirSync(".vscode");
  }
  fs.writeFileSync(".vscode/settings.json", JSON.stringify(settings, null, 2));
}

writeNpmrc()
writeVSCodeSettings();
