# 研发一体化平台-使用说明

**研发一体化平台**是一个构建在**统一技术底座**之上的可视化研发平台，借助可视化的全栈应用设计，和高质量的源码导出，可以帮助开发者在开发效率和代码质量上取得提升。



为了帮助用户快速上手，本文将从零开始构建一个“个人管理系统”，提供了一个我的待办模块，以讲解研发一体化平台（后面简称：平台）的使用过程。



更新时间：2025-03-14 11:45



## 1 创建项目

点击首页-新建项目：

![image-20250313162509962](./assets/image-20250313162509962.png)



然后需要配置一些表单项：

![image-20250313164542058](./assets/image-20250313164542058.png)



注意：**项目编码**要尽可能“简短”，因为它们会在许多地方使用，包括后端包名，比如：

```txt
app, school, todo, crm, hr ... # good
school_management, xueshengguanli_pingtai ... # bad
```



## 2 新建模型

项目创建好后，需要在 代码生成 - 后端开发 - 数据模型 页面中**新建模型**：

![image-20250313164806019](./assets/image-20250313164806019.png)



数据模型是前后端项目的核心，前端通过数据模型生成增删改查、导入导出完备的页面，后端通过数据模型生成相应的模块。

![image-20250313165602426](./assets/image-20250313165602426.png)

将左侧的字段”拖拽“到右侧区域，然后点击右上角的**保存**，即可生成一个数据模型。



## 3 页面设计

数据模型生成好后，会默认生成一个前端页面，我们点击**设计**按钮，即可进入前端页面设计器：

![image-20250313165714533](./assets/image-20250313165714533.png)

![image-20250313165746381](./assets/image-20250313165746381.png)



这是一个低代码设计器，我们可以对生成的页面进行二次修改，比如：我想为这个页面增加一个标题：

![image-20250313170404144](./assets/image-20250313170404144.png)



完成后，我们需要点击右上角”保存”。



## 4 源码导出

页面设计完成后，我们就需要前往“源码导出”页面，右侧导出源码按钮分成了“前端”和“后端”源码导出：

![image-20250313170809458](./assets/image-20250313170809458.png)



点击“导出”之后，如果一切顺利，列表中将会有两项，我们分别点击“下载”，到此，我们在平台中的操作就暂告一段落了。

![image-20250313171954191](./assets/image-20250313171954191.png)



## 5 本地后端开发

下载到本地的代码包含两个部分：`-ui` 结尾的是前端项目，另一个是后端项目：

```txt
me
 - me # 后端项目
 - me-ui # 前端项目
```



我们需要先启动后端项目。



### 5.1 环境搭建

想要成功运行后端服务，需要安装：JDK@17、Maven、Redis、Mysql，安装和配置方法，请自行解决。

然后登录研发云账号，按下图中的位置，打开“使用指引”。（搜索：*ctgansu-oshare-maven-mc*）

![image-20250313173456077](./assets/image-20250313173456077.png)



然后根据页面中的指引，配置好本地电脑中的 settings.xml，该文件在 Windows 中在 `/conf/settings.xml` , 在 Mac 和 Linux 中是 `~/.m2/settings.xml`。

![image-20250313174220480](./assets/image-20250313174220480.png)



### 5.2 配置

需要改一下 `/me/me-platform/me-platform-backend/src/main/resources/application-alone.yaml` 配置文件，开发时走配置文件，生产环境走环境变量。

![image-20250313175544273](./assets/image-20250313175544273.png)



### 5.3 数据库初始化

项目默认的数据库名是：`zion-alone`，运行前需要创建数据库：

```sh
mysql -u root -p

mysql> create database `zion-alone`;
```



然后导入项目中的这些 sql 脚本：

![image-20250314101508186](./assets/image-20250314101508186.png)



### 5.4 启动

以 VS Code 为例，安装 Extension Pack for Java，然后重新打开编辑器，就会自动执行 `mvn compile`。然后找到：`/me/me-platform/me-platform-backend/src/main/java/com/ctgs/me/platform/ZionPlatformApplication.java` 文件，右键执行 Java Run 或 点击`run` 按钮启动项目：

![image-20250313180605649](./assets/image-20250313180605649.png)



如果一切正常，你将得到这样的输出：

![image-20250314091310491](./assets/image-20250314091310491.png)



然后在浏览器中访问：http://localhost:8081/doc.html, 能够正常访问 swagger，就表明启动成功！👏🏻👏🏻👏🏻

![image-20250314091355883](./assets/image-20250314091355883.png)



## 6 本地前端开发

后端服务启好后，就需要启动前端项目，请自行安装 [Node](https://nodejs.org/en), 要求版本不低于 18.0.0.



### 6.1 安装依赖

安装依赖之前，需要将 npm 镜像源切换到 taobao:

```sh
$ npm config set registry https://registry.npmmirror.com/
```



然后**必须**使用 npm 进行安装，不能用 yarn 或 pnpm：

```sh
$ cd ./me-ui
$ npm i
```



注意，项目采用 npm-monorepo 架构，只需要在最外层 npm install, 内部的 packages 中的包不用单独安装。如果一切正常，你将得到这样的输出：

![image-20250314092144161](./assets/image-20250314092144161.png)



注意：项目内部规范了npm源的地址，将会在第一次 npm i 时将“研发云”的镜像源写入 .npmrc 文件中，所以，请确保你的网络能正常访问研发云，否则，请删除 .npmrc 文件后再安装依赖：

![image-20250314092242200](./assets/image-20250314092242200.png)



### 6.2 启动

依赖安装成功后，可以在 ./zion-ui 下运行命令： `npm run serve-alone`，如果一切顺利，你将得到这样的输出：

![image-20250314094208343](./assets/image-20250314094208343.png)



访问的入口地址是：http://localhost:3000/, 默认账号是：zionAdmin, 密码记录在后端项目中 `application-alone.yaml` 文件中的 `default-pwd` 字段上。

![image-20250314104602676](./assets/image-20250314104602676.png)

![image-20250314104801189](./assets/image-20250314104801189.png)



### 6.3 页面配置

接下来，需要配置一下前端的路由，以便用户可以在左侧菜单栏中访问：”我的待办“：

![image-20250314104959788](./assets/image-20250314104959788.png)



首先要在系统管理-资源管理中，新增一个页面：

![image-20250314112048156](./assets/image-20250314112048156.png)



然后在系统管理-角色管理中，在角色上勾选这个页面资源：

![image-20250314105933137](./assets/image-20250314105933137.png)



然后刷新页面，即可看到左侧已经出现了“我的待办”：

![image-20250314110028477](./assets/image-20250314110028477.png)



但现在点击它还是 404，因为我们还没有在前端项目中进行配置：



### 6.4 前端微服务配置

我们先在 micro-app 目录下新增一个 `me-center.vue` 页面文件：

![image-20250314110515441](./assets/image-20250314110515441.png)



然后在 app-main 文件夹下的 .env 文件中加入 VITE_MICRO_APP_ME_CENTER_URL，这里的3333端口，指的是 app-me 微应用的开发服务器端口。

![image-20250314112133154](./assets/image-20250314112133154.png)



然后配置路由，注意：path 的前缀需要跟上面资源管理中配置的路由地址一致：

![image-20250314112401349](./assets/image-20250314112401349.png)



完成后，如果一切顺利，点击“我的待办”时就会出现这个页面，表明我们的项目配置已经完成：

![image-20250314114256149](./assets/image-20250314114256149.png)



到现在为止，您就可以开始在本地继续项目的开发了！👏🏻👏🏻👏🏻



## 7 增量更新

Developing....