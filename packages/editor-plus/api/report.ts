// import { request } from '../utils/service'
import request, {
  asyncGetSSOLoginUrl,
} from '../../app-main/src/apis/requests/request';
import { to } from 'await-to-js';

// 报告保存
export const createReportApi = (data: any) => {
  return request({
    url: "/web/vi/doc/generate/upload",
    method: "post",
    data,
  });
};

// 获取报告详情
export const getReportDetailApi = (params: { programmeId: string | number; itemId: string | number }) => {
  return request({
    url: '/web/vi/doc/generate/content',
    method: "get",
    params: params
  });
};

// 获取推荐问题
export const getRecommendQuestionsApi = () => {
  return request({
    url: "/docWriter/docQuestion/all",
    method: "get",
  });
};

// 纠错
export const getCorrectionApi = (data: { request: string, query: string, responseMode: string }): Promise<any> => {
  return request({
    url: "/docWriter/generate/v1/item/correction",
    method: "post",
    data,
  });
};

// 文本对话
export const sendReplyMessage = (data: any): Promise<any> => {
  return request({
    url: "/docWriter/generate/v1/item/text",
    method: "post",
    data,
    headers: {
      'accept': '*/*',
    },
  });
};

export const handleLoseSession = async () => {
  // 否则走微服务的跳转登录逻辑
  const [getSSOLoginUrlErr, ssoLoginUrl] = await to(asyncGetSSOLoginUrl());

  if (getSSOLoginUrlErr) return Promise.reject(getSSOLoginUrlErr);

  if (!ssoLoginUrl) {
    console.error('enableZionAxiosInterceptor: 获取单点登录页地址失败');
    return Promise.reject('获取单点登录页地址失败');
  }

  await new Promise((resolve) => setTimeout(resolve, 500));
  const isEncryptInfoError = localStorage.getItem('zion-encrypt-info-error') === 'true';
  const __ssoLoginUrl = isEncryptInfoError
    ? `${ssoLoginUrl}${ssoLoginUrl.includes('?') ? '&' : '?'}clear=true`
    : ssoLoginUrl;
  window.location.href = __ssoLoginUrl;
  return Promise.reject('登录已过期，请重新登录');
}
