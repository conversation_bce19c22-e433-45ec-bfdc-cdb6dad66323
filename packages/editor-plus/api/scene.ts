import { request } from '../utils/service'

/**
 * 获取场景类型列表数据
 * @param type 类型：scene_category=简易模式，scene_mode=专业模式
 * @returns 
 */
export const getSceneTypeList = (type: string = 'scene_category') => {
  return request({
    url: `/docWriter/dict/list?type=${type}`,
    method: "get",
  })
}

/**
 * 获取场景列表
 * @param params {
 *    name: '',
 *    desc: '',
 *    category: 场景类型
 *    mode: 模式（1：通用，2：简易，3：专业）
 * }
 * @returns 
 */
export const getSceneListApi = (params: any) => {
  return request({
    url: `/docWriter/scene/list`,
    method: "get",
    params: params
  })
}

