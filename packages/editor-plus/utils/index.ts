/**
 * 去除think标签内容并确保Markdown标题格式正确
 * @param {string} content - 原始Markdown内容
 * @returns {string} 处理后的Markdown内容
 */
const removeThinkContent = (content: string): string => {
  // 去除think标签内容
  const thinkRegex = /<think>[\s\S]*?<\/think>/g;
  let processedContent = content.replace(thinkRegex, '');
  
  // 确保标题标记前有换行符
  // 匹配不是行首的标题标记（包括 #、##、###、#### 等）
  processedContent = processedContent.replace(/([^\n])\s*(#{1,4}\s)/g, '$1\n$2');
  
  // 移除只包含 # 符号的行（无论是单个还是多个#）
  processedContent = processedContent.replace(/^\s*#{1,4}\s*$/gm, '');
  // 移除以 # 符号结尾的行（无论是单个还是多个#）

  return processedContent;
}

export {
	removeThinkContent
}