{"name": "editor-plus", "version": "0.0.1", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "scripts": {"build": "vue-tsc && vite build", "build-only-release": "echo 'editor-plus build-only-release'"}, "peerDependencies": {"vue": "^3.0.0"}, "dependencies": {"aieditor": "^1.3.8", "animejs": "^4.0.2", "ant-design-vue": "4.x", "axios": "^1.9.0", "lodash-es": "^4.17.21", "markdown-it": "^14.1.0", "marked": "^15.0.12", "screenfull": "^6.0.2", "vue": "^3.0.0"}, "devDependencies": {"@types/lodash-es": "^4.17.12", "@types/markdown-it": "^14.1.2", "@types/node": "^22.15.21", "@vitejs/plugin-vue": "^4.5.0", "typescript": "5.6.2", "vite": "^5.0.0", "vite-plugin-dts": "^4.5.4", "vue-tsc": "2.0.29"}}