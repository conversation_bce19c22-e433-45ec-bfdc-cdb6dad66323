<template>
  <div class="bg-blue-50 rounded-md relative px-7px">
    <!-- 大模型审校 -->
    <div class="my-4">
      <div class="flex items-center mb-2">
        <div class="pl-2 flex-1 text-[#fff] text-base font-medium rounded-full bg-gradient-to-r from-[#3180FB] from-indigo-0">大模型审校</div>
        <div class="ml-auto">
          <a-checkbox v-model:checked="allSelected.model" @change="handleModelAllChange">全选</a-checkbox>
        </div>
      </div>

      <!-- 语法性差错 -->
      <div class="mb-3 pl-7px">
        <div class="flex items-center mb-2">
          <a-checkbox v-model:checked="allSelected.syntax" @change="handleSyntaxAllChange"></a-checkbox>
          <div class="text-base font-medium ml-2">语法性差错</div>
        </div>
        <div class="grid grid-cols-3 gap-x-1 gap-y-2">
          <a-checkbox v-for="item in syntaxOptions" :key="item.value" v-model:checked="item.checked" @change="() => handleItemChange(item, 'syntax')">
            {{ item.label }}
          </a-checkbox>
        </div>
      </div>

      <!-- 逻辑性差错 -->
      <div class="pl-7px">
        <div class="flex items-center mb-2">
          <a-checkbox v-model:checked="allSelected.logic" @change="handleLogicAllChange"></a-checkbox>
          <div class="text-base font-medium ml-2">逻辑性差错</div>
        </div>
        <div class="grid grid-cols-3 gap-x-1 gap-y-2">
          <a-checkbox v-for="item in logicOptions" :key="item.value" v-model:checked="item.checked" @change="() => handleItemChange(item, 'logic')">
            {{ item.label }}
          </a-checkbox>
        </div>
      </div>
    </div>

    <!-- 错别字 -->
    <div class="mb-4">
      <div class="flex items-center mb-2">
        <div class="pl-2 flex-1 text-[#fff] text-base font-medium rounded-full bg-gradient-to-r from-[#3180FB] from-indigo-0">错别字</div>
        <div class="ml-auto">
          <a-checkbox v-model:checked="allSelected.typo" @change="handleTypoAllChange">全选</a-checkbox>
        </div>
      </div>
      <div class="grid grid-cols-3 gap-x-1 gap-y-2 pl-7px">
        <a-checkbox v-for="item in typoOptions" :key="item.value" v-model:checked="item.checked" @change="() => handleItemChange(item, 'typo')">
          {{ item.label }}
        </a-checkbox>
      </div>
    </div>

    <!-- 使用方案按钮 -->
    <div class="mt-4 px-3px">
      <a-button type="primary" :loading="loading" block size='small' @click="handleSubmit" class="bg-blue-500 hover:bg-blue-600 !w-[calc(100%-10px)] h-[40px] mx-auto">
        <template #icon>
          <span class="anticon">
            <svg viewBox="64 64 896 896" focusable="false" data-icon="file-text" width="1em" height="1em" fill="currentColor" aria-hidden="true">
              <path
                d="M854.6 288.6L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM790.2 326H602V137.8L790.2 326zm1.8 562H232V136h302v216a42 42 0 0042 42h216v494z">
              </path>
              <path d="M504 618H320c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h184c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8zM312 490v48c0 4.4 3.6 8 8 8h384c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H320c-4.4 0-8 3.6-8 8z"></path>
            </svg>
          </span>
        </template>
        使用该方案
      </a-button>
    </div>

    <!-- 错误列表 -->
    <transition name="slide-fade">
      <div class="h-[100%] w-[90%] bg-[#ECF3FE] text-[14px] overflow-y-auto absolute top-0 left-0  p-16px rounded-md shadow-lg z-10" v-if="showErrorList">
        <div class="flex items-center text-[18px]">
          <img src="../assets/img/error-list-logo.svg" alt="close" class="w-5 h-5 cursor-pointer" @click="showErrorList = !showErrorList">
          <span class="ml-2 text-[#333]">文稿纠错</span>
        </div>
        <div v-if="errorList.length === 0" class="text-center py-8 text-gray-500">
          暂无审校结果
        </div>
        <div v-else class="space-y-4">
          <div v-for="(item, index) in errorList" :key="index" class="mt-4 pt-4 border-t-2 border-t-solid border-white rounded-md hover:bg-blue-50">
            <div class="font-medium mb-1" v-html="item.text"></div>
            <!-- <div class="text-gray-700 mb-1">{{ item.result }}</div> -->
            <div class="flex justify-between items-center border-t border-t-dashed border-gray-300 py-2 mt-4">
              <div class="text-green-600">{{ item.update }}</div>

            </div>
            <div class="flex justify-end">
              <a-button type="link" size="small" @click="applyCorrection(item)">
                应用修改
              </a-button>
            </div>
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { getCorrectionApi } from '../api/report';

const props = defineProps({
  selectedContent: {
    type: String,
    default: ''
  }
});

// 定义事件
const emit = defineEmits<{
  (e: 'submit', values: string[]): void
  (e: 'apply-correction', correction: { original: string; correction: string }): void
}>();

interface CheckboxOption {
  label: string;
  value: string;
  checked: boolean;
}

const errorList = ref<any[]>([]);
const showErrorList = ref(false);
const loading = ref(false);

// 定义选项
const syntaxOptions = ref<CheckboxOption[]>([
  { label: '语法性差错', value: '语法性差错', checked: true },
  { label: '多字漏字', value: '多字漏字', checked: true },
  { label: '语序错误', value: '语序错误', checked: false },
  { label: '语句不通', value: '语句不通', checked: false },
  { label: '人名错误', value: '人名错误', checked: false },
  { label: '表意不明', value: '表意不明', checked: false },
]);

const logicOptions = ref<CheckboxOption[]>([
  { label: '逻辑性差错', value: '逻辑性差错', checked: true },
  { label: '成分多余', value: '成分多余', checked: true },
  { label: '不合事理', value: '不合事理', checked: false },
  { label: '前后矛盾', value: '前后矛盾', checked: false },
  { label: '句式杂糅', value: '句式杂糅', checked: false },
]);

const typoOptions = ref<CheckboxOption[]>([
  { label: '常见错误', value: '常见错误', checked: true },
  { label: '的地得错误', value: '的地得错误', checked: true },
  { label: '音/形相似错误', value: '音/形相似错误', checked: false },
  { label: '标点符号错误', value: '标点符号错误', checked: false },
  { label: '搭配错误', value: '搭配错误', checked: false },
  { label: '生僻字', value: '生僻字', checked: false },
  { label: '地名错误', value: '地名错误', checked: false },
]);

// 全选状态
const allSelected = ref({
  model: false,
  syntax: false,
  logic: false,
  typo: false,
});

// 选中的值
const selectedValues = ref<string[]>([]);

// 计算全选状态
const updateAllSelectedState = () => {
  allSelected.value.syntax = syntaxOptions.value.every(item => item.checked);
  allSelected.value.logic = logicOptions.value.every(item => item.checked);
  allSelected.value.typo = typoOptions.value.every(item => item.checked);

  // 大模型审校全选状态取决于语法性差错和逻辑性差错是否全部选中
  allSelected.value.model = allSelected.value.syntax && allSelected.value.logic;
};

// 更新选中的值
const updateSelectedValues = () => {
  const values: string[] = [];

  // 收集所有选中的值
  syntaxOptions.value.forEach(item => {
    if (item.checked) values.push(item.value);
  });

  logicOptions.value.forEach(item => {
    if (item.checked) values.push(item.value);
  });

  typoOptions.value.forEach(item => {
    if (item.checked) values.push(item.value);
  });

  selectedValues.value = values;
};

// 处理单个选项变化
const handleItemChange = (item: CheckboxOption, type: 'syntax' | 'logic' | 'typo') => {
  updateAllSelectedState();
  updateSelectedValues();
};

// 处理全选变化
const handleModelAllChange = (e: Event) => {
  const checked = (e.target as HTMLInputElement).checked;

  // 大模型审校全选会同时影响语法性差错和逻辑性差错
  syntaxOptions.value.forEach(item => {
    item.checked = checked;
  });

  logicOptions.value.forEach(item => {
    item.checked = checked;
  });

  // 更新全选状态
  allSelected.value.syntax = checked;
  allSelected.value.logic = checked;

  updateSelectedValues();
};

const handleSyntaxAllChange = (e: Event) => {
  const checked = (e.target as HTMLInputElement).checked;
  syntaxOptions.value.forEach(item => {
    item.checked = checked;
  });
  updateAllSelectedState();
  updateSelectedValues();
};

const handleLogicAllChange = (e: Event) => {
  const checked = (e.target as HTMLInputElement).checked;
  logicOptions.value.forEach(item => {
    item.checked = checked;
  });
  updateAllSelectedState();
  updateSelectedValues();
};

const handleTypoAllChange = (e: Event) => {
  const checked = (e.target as HTMLInputElement).checked;
  typoOptions.value.forEach(item => {
    item.checked = checked;
  });
  updateSelectedValues();
};

// 提交方法
const handleSubmit = () => {
  console.log('选中的值:', selectedValues.value);
  // 触发提交事件
  emit('submit', selectedValues.value);
  loading.value = true;
  getCorrectionApi({
    request: selectedValues.value.join(','),
    query: props.selectedContent,
    responseMode: 'blocking'
  }).then(res => {
    if(!res.data?.success) {
      console.error('获取审校结果失败');
      return;
    }
    res = res.data.data;
    errorList.value = JSON.parse(res.answer).filter((item: any) => item.update !== '' && item.update !== '无错误。' && item.update !== '无错误');
    console.log(errorList.value);

    showErrorList.value = true;
  }).finally(() => {
    loading.value = false;
  });
};

// 应用修改建议
const applyCorrection = (item: any) => {
  // 触发一个新的事件，传递修改建议
  emit('apply-correction', {
    original: item.text,
    correction: item.result
  });
};

// 对外暴露选中的值
defineExpose({
  selectedValues
});

// 初始化
watch(
  [syntaxOptions, logicOptions, typoOptions],
  () => {
    updateAllSelectedState();
    updateSelectedValues();
  },
  { immediate: true, deep: true }
);

watch(
  () => props.selectedContent,
  (newVal) => {
    showErrorList.value = false;
  }
);
</script>

<style scoped>
/* 使用Tailwind CSS类，不需要额外的样式 */
:deep(.ant-checkbox-wrapper) {
  margin-inline-start: 0 !important;
}

:deep(.ant-btn-primary) {
  background-color: rgb(59 130 246);
}

:deep(.ant-btn-primary:hover) {
  background-color: rgb(37 99 235) !important;
}

/* 添加过渡效果 */
.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.3s ease;
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  transform: translateY(-20px);
  opacity: 0;
}
</style>
