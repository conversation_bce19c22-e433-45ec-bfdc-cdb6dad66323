<template>
  <div class="select-action-panel relative">
    <img src="../assets/img/panel-close.png" class="absolute right-[10px] top-[10px] cursor-pointer z-20" @click="closePanel" />
    <div class="h-[46px] px-7px">
      <a-tabs v-model:activeKey="activeTab">
        <a-tab-pane tab="扩写" key="elaborateOn"></a-tab-pane>
        <a-tab-pane tab="缩写" key="abbreviation"></a-tab-pane>
        <a-tab-pane tab="润色" key="polish"></a-tab-pane>
        <a-tab-pane tab="摘要" key="digest"></a-tab-pane>
        <a-tab-pane tab="纠错" key="correction"></a-tab-pane>
      </a-tabs>
    </div>
    <!-- 面板内容 -->
    <div class="h-[calc(100%-46px)] w-full overflow-y-auto text-[12px] scrollbar" v-if="activeTab !== 'correction'">
      <!-- 扩写、缩写、摘要表单 -->
      <div v-if="activeTab !== 'polish'" class="p-2">
        <div class="mb-4">
          <div class="text-[12px] text-[#666] mb-2">原文内容</div>
          <a-textarea class="bg-white w-[calc(100%-20px)]" v-model:value="formData.selectedContent" :auto-size="{ minRows: 4, maxRows: 4 }" placeholder="请输入原文内容" />
        </div>

        <div class="mb-4">
          <div class="text-[12px] text-[#666] mb-2">{{typeName()}}要求 <span class="text-red-500">*</span></div>
          <a-textarea class="bg-white w-[calc(100%-20px)]" v-model:value="formData.request" :maxLength="500" :auto-size="{ minRows: 4, maxRows: 4 }" placeholder="请输入您的要求" />
        </div>

        <div class="mb-4">
          <div class="text-[12px] text-[#666] mb-2">字数要求 <span class="text-red-500">*</span> <span class="text-gray-400">(不能超过2000字)</span></div>
          <a-input-number class="bg-white w-full" :min="1" :max="2000" v-model:value="formData.wordLimit" placeholder="请输入字数要求" />
        </div>
      </div>

      <!-- 润色表单 -->
      <div v-if="activeTab === 'polish'" class="p-2">

        <div class="mb-4">
          <div class="text-[12px] text-[#666] mb-2">原文内容</div>
          <a-textarea class="bg-white w-[calc(100%-20px)]" v-model:value="polishData.selectedContent" :auto-size="{ minRows: 4, maxRows: 6 }" placeholder="请输入原文内容" />
        </div>

        <div class="mb-4">
          <div class="text-[12px] text-[#666] mb-2">优化方向</div>
          <div>
            <a-tag v-for="tag in optimizationDirections" :key="tag.value" class="cursor-pointer mb-1 bg-[#DCE5F3] border-[#DCE5F3]" :class="{ 'bg-white border-blue-500 text-blue-500': polishData.optimization === tag.value }"
              @click="toggleDirection(tag.value)">
              {{ tag.label }}
            </a-tag>
          </div>
        </div>

        <div class="mb-4">
          <div class="text-[12px] text-[#666] mb-2">润色要求 <span class="text-red-500">*</span></div>
          <a-textarea class="bg-white w-[calc(100%-20px)]" v-model:value="polishData.request" :maxLength="500" :auto-size="{ minRows: 4, maxRows: 6 }" placeholder="请输入润色要求" />
        </div>

        <div class="mb-4">
          <div class="text-[12px] text-[#666] mb-2">润色要求 <span class="text-red-500">*</span></div>
          <div>
            <a-tag v-for="tag in polishTags" :key="tag.value" class="cursor-pointer mb-1 bg-[#DCE5F3] border-[#DCE5F3]" :class="{ 'bg-white border-blue-500 text-blue-500': polishData.polish === tag.value }" @click="toggleTag(tag.value)">
              {{ tag.label }}
            </a-tag>
          </div>
        </div>

      </div>

      <div class="flex mb-4 pl-[10px]">
        <a-button type="primary" :loading="generatingStatus" block size='small' @click="handleGenerate" class="bg-blue-500 h-[40px] !w-[calc(100%-20px)]">
          <img src="../assets/img/generate.png" class="h-[16px] w-[16px] mr-1 mb-[2px]" />
          <span class="flex items-center justify-center">
            <span class="mr-1">开始生成</span>
          </span>
        </a-button>
      </div>

      <div class="bg-white w-[calc(100%-32px)] max-h-[400px] p-[10px] overflow-y-auto ml-10px scrollbar rounded-md text-[14px] line-height-24px" ref="contentRef" v-if="outputContent">
        <Markdown :value="outputContent" />
        <div class="flex justify-end text-[#0265FF] gap-2 mt-4" v-if="!generatingStatus">
          <div type="link" size="small" @click="insertContent()" class="cursor-pointer">
            <img src="../assets/img/insert.png" class="h-[16px] w-[16px] mr-1 mb-[2px]" />
            插入
          </div>
          <div type="link" size="small" @click="replaceContent()" class="cursor-pointer">
            <img src="../assets/img/replace.png" class="h-[16px] w-[16px] mr-1 mb-[2px]" />
            替换
          </div>
          <div type="link" size="small" @click="copyContent()" class="cursor-pointer">
            <img src="../assets/img/copy.png" class="h-[16px] w-[16px] mr-1 mb-[2px]" />
            复制
          </div>
        </div>
      </div>
    </div>
    <!-- 纠错面板内容 -->
    <div class="h-[calc(100%-46px)] w-full overflow-y-auto text-[12px] scrollbar" v-if="activeTab === 'correction'">
      <Correction :selectedContent="selectedContent" @apply-correction="(correction) => emit('apply-correction', correction)" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { message } from 'ant-design-vue';
import { marked } from 'marked';
import { nextTick, onMounted, ref, watch } from 'vue';
import { removeThinkContent } from '../utils';
import Correction from './Correction.vue';
import Markdown from "./Markdown.vue";
import { handleLoseSession } from '../api/report';
const props = defineProps({
  actionType: {
    type: String,
    default: ''
  },
  selectedContent: {
    type: String,
    default: ''
  }
});

// 修改emit定义，添加apply-correction事件
const emit = defineEmits(['update:value', 'insert', 'copy-success', 'close', 'replace', 'apply-correction']);

// 当前激活的标签页
const activeTab = ref(props.actionType);

// 表单数据
const formData = ref({
  selectedContent: '',
  request: '',
  wordLimit: null
});

// 润色表单数据
const polishData = ref({
  request: '',
  selectedContent: '',
  optimization: '',  // 优化方向
  polish: ''         // 润色标签
});

// 优化方向
const optimizationDirections = [
  { label: '调整语序', value: '调整语序' },
  { label: '增加细节', value: '增加细节' },
  { label: '提升文采', value: '提升文采' },
  { label: '增加逻辑', value: '增加逻辑' }
];

// 润色标签
const polishTags = [
  { label: '专业', value: '专业' },
  { label: '政府', value: '政府' },
  { label: '幽默', value: '幽默' },
  { label: '正式', value: '正式' },
  { label: '亲切', value: '亲切' }
];

const contentRef = ref<HTMLElement | null>(null);

// 生成中状态
const generatingStatus = ref(false)

const outputContent = ref('')

onMounted(() => {
  initData()
})

watch(props, () => {
  activeTab.value = props.actionType
  initData()
})

function initData() {
  formData.value.selectedContent = props.selectedContent
  polishData.value.selectedContent = props.selectedContent
}

// 切换标签
const toggleTag = (value: string) => {
  polishData.value.polish = value;
};

// 切换优化方向
const toggleDirection = (value: string) => {
  polishData.value.optimization = value;
};

// 处理生成按钮点击
const handleGenerate = () => {
  let inputs = null
  if (activeTab.value !== 'polish') {
    inputs = {
      "request": formData.value.request,
      "wordLimit": formData.value.wordLimit
    }
  } else {
    inputs = {
      "request": polishData.value.request,
      "polish": polishData.value.polish,
      "optimization": polishData.value.optimization
    }
  }
  handleActionContent(formData.value.selectedContent, inputs);
};



const copyContent = () => {
  if (outputContent.value) {
    navigator.clipboard.writeText(removeThinkContent(outputContent.value)).then(() => {
      message.success('复制成功')
    }).catch(err => {
      console.error('复制失败:', err);
    });
  }
};

const insertContent = () => {
    // 先触发插入事件
    emit('insert', removeThinkContent(outputContent.value));
};

const replaceContent = () => {
  if (outputContent.value) {
    emit('replace', removeThinkContent(outputContent.value));
  }
};


async function handleActionContent(content: string, inputs: any) {
  if (!content) return
  generatingStatus.value = true
  try {
    const response = await fetch(`${import.meta.env.VITE_API_PREFIX}/docWriter/generate/v1/item/${activeTab.value}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: localStorage.getItem('zion-auth-value'),
      },
      body: JSON.stringify({
        "query": content,
        ...inputs,
        "responseMode": "streaming",
      }),
    });

    if (response.status === 401) {
      message.warning('登录已过期，请重新登录');
      return handleLoseSession();
    }

    if (!response.ok) {
      throw new Error('网络响应异常');
    }

    const reader = response.body?.getReader();
    const decoder = new TextDecoder('utf-8');

    // 清空输出面板内容
    outputContent.value = '';
    let fullContent = '';
    let done = false;

    while (!done) {
      const { value, done: streamDone } = await reader!.read();

      done = streamDone;
      if (done) break;

      const chunk = decoder.decode(value, { stream: true });

      // 处理每个数据块
      const lines = chunk.split('\n');
      for (const line of lines) {
        const cleanLine = line.replace('data: ', '');
        try {
          const content = JSON.parse(cleanLine).answer || '';
          if (content) {
            fullContent += content;
            // 实时更新输出面板内容
            outputContent.value = marked.parse(fullContent) as string;
            console.log(outputContent.value);

          }
        } catch (e) {
          // console.error('解析错误:', e);
        }
      }
    }
  } catch (error) {
    // @ts-ignore
    if (error.name === 'AbortError') {
      return;
    }
    console.error('请求失败:', error);
  } finally {
    generatingStatus.value = false;
    if (contentRef.value) {
      contentRef.value.scrollTop = contentRef.value.scrollHeight;
    }
  }
}


function typeName () {
  let name = ''
  switch (activeTab.value) {
    case 'elaborateOn':
      name = '扩写'
      break;
    case 'abbreviation':
      name = '缩写'
      break;
    case 'polish':
      name = '润色'
      break;
    case 'digest':
      name = '摘要'
      break;
    case 'correction':
      name = '纠错'
      break;
    default:
      break;
  }
  return name;
}

function handleApply(value: string[]) {
  console.log(value);
}

// 监听内容变化，自动滚动到底部
watch(() => outputContent.value, async () => {
  if (outputContent.value) {
    await nextTick();
    if (contentRef.value) {
      contentRef.value.scrollTop = contentRef.value.scrollHeight;
    }
  }
}, { immediate: true });

const closePanel = () => {
  emit('close');
};
</script>

<style scoped>
  .select-action-panel {
    width: 100%;
    height: 100%;
    background-color: #ECF3FE;
    position: absolute;
    top: 0;
    right: 0;
    z-index: 10;
    padding: 10px;
    padding-top: 0;
    box-sizing: border-box;
    border-radius: var(--ant-borderRadius);
    transition: all 0.3s ease;
  }

  /* 自定义滚动条样式 */
  .scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar::-webkit-scrollbar-track {
    background-color: #f3f4f6;
    border-radius: 9999px;
  }

  .scrollbar::-webkit-scrollbar-thumb {
    background-color: #ccc;
    border-radius: 9999px;
  }

  .scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: #ccc;
    transition: background-color 0.2s;
  }

  .scrollbar {
    scrollbar-width: thin;
    scrollbar-color: #ccc #f3f4f6;
  }
</style>
