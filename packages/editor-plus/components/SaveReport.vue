<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { createReportApi } from '../api/report'
import { message } from 'ant-design-vue'
import { ReportDetail } from '../type/report';
const props = defineProps({
  open: {
    type: Boolean,
    default: false,
  },
  content: {
    type: String,
    default: '',
  },
  reportDetail: {
    type: Object,
    default: () => ({}) as Partial<ReportDetail>,
  },
  isSpecialty: {
    type: Boolean,
    default: false,
  },
  programmeId: {
    type: String,
    default: '',
  },
  itemId: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['update:open', 'updateSeccess'])
const form = ref({
  docName: '',
  reportType: '',
  description: '',
})
const rules = ref({
  docName: [{ required: true, message: '请输入报告名称' }],
  reportType: [{ required: true, message: '请选择报告类型' }],
  description: [{ required: true, message: '请输入报告描述' }],
})

const options = ref([
  { label: '普通文档', value: 'normal' },
  { label: '新闻发布稿', value: 'news' },
  { label: '讲话', value: 'speak' },
  { label: '总结', value: 'final' },
  { label: '通报', value: 'notification' },
])

const formRef = ref()
const loading = ref(false)

const initForm = () => {
  form.value.docName = props.reportDetail?.docName || ''
  form.value.reportType = props.reportDetail?.reportType || ''
  form.value.description = props.reportDetail?.description || ''
}

watch(() => props.reportDetail, (newVal: Partial<ReportDetail>) => {
  initForm()
}, { immediate: true, deep: true })


const close = () => {
  emit('update:open', false)
}
const save = () => {
  formRef.value?.validate().then(() => {
    loading.value = true;
    createReportApi({
      docName: form.value.docName,
      content: props.content,
      size: 0,
      programmeId: props.programmeId || undefined,
      itemId: props.itemId || undefined,
      // reportType: form.value.reportType,
      // description: form.value.description,
      // type: props.isSpecialty ? 2 : 1,
      // id: props.reportDetail.id || undefined,
    }).then((res: any) => {
      if (res.data.success === true) {
        message.success('保存成功')
        console.log(res.data.data)
        emit('updateSeccess', res.data.data)
        close();
        // 返回首页（不使用router）
        // window.location.href = '/'
      }
    }).finally(() => {
      loading.value = false;
    })
  })
}
</script>

<template>
  <a-modal title="保存报告" :open="open" @cancel="close">
    <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
      <a-form-item label="报告名称" name="docName">
        <a-input maxlength="50" showCount  v-model:value="form.docName" />
      </a-form-item>
      <!-- <a-form-item label="报告类型" name="reportType">
        <a-select v-model:value="form.reportType" :options="options" />
      </a-form-item>
      <a-form-item label="报告描述" name="description">
        <a-textarea v-model:value="form.description" :rows="4" :maxlength="100" showCount />
      </a-form-item> -->
    </a-form>
    <template #footer>
      <a-button type="primary" @click="save" :loading="loading">保存</a-button>
      <a-button type="default" @click="close">取消</a-button>
    </template>
  </a-modal>
</template>

<style></style>