<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
const props = defineProps({
  open: {
    type: Boolean,
    default: false,
  },
  tempList: {
    type: Array,
    default: () => []
  },
  commTemp: {
    type: String,
    default: () => ''
  }
})
const emit = defineEmits(['update:open', 'close', 'success'])
const loading = ref(false);
const formRef = ref();
const rules = ref({
  tempId: [{ required: true, message: '请先选择模板' }],
  inputs: [{ required: true, message: '请输入具体要求' }],
})

const stepIndex = ref(1)

// 模板列表
// const tempList = ref([
//   {
//     id: '1',
//     name: '需求规格说明书',
//     content: ''
//   },
//   {
//     id: '2',
//     name: '架构设计内容',
//     content: ''
//   },
// ])

const form = reactive({
  tempId: '',         //  模板id
  tempContent: '',    // 模板内容
  inputs: ''          // 用户需求
})

// 表单内容
watch(
  () => props.open,
  (bool) => {
    if (bool) {
      const searchParams = new URLSearchParams(window.location.search);
      const _commTemp = searchParams.get('commTemp')
      if (_commTemp) {
        let temp = props.tempList.find((o:any) => o.id == _commTemp) as any
        if (temp) {
          form.tempId = temp.id;
          form.tempContent = temp.name
        }
      }
    }
  }
)


/**
 * 模板类型选择
 * @param tempContent 
 */
const tagTypeHandle = (item: any) => {
  form.tempId = item.id;
  form.tempContent = item.name;
}

/**
 * 上一步
 */
const prev = () => {
  stepIndex.value = 1;
}

/**
 * 下一步
 */
const next = () => {
  //formRef.value?.validate().then(() => {
  //  stepIndex.value = 2;
  //})
  formRef.value?.validate().then(() => {
    formRef.value?.validate().then(() => {
      emit('success', {...form})
      close();
    })
  })
}

/**
 * 关闭弹窗
 */
const close = () => {
  emit('update:open', false)

  stepIndex.value = 1;
  form.tempId = '';
  form.tempContent = '';
  form.inputs = '';
}

/**
 * 确认
 */
const confirm = () => {
  formRef.value?.validate().then(() => {
    emit('success', {...form})
    close();
  })
}

defineExpose({
  close
})
</script>

<template>
  <a-modal 
    :open="open" 
    :title="stepIndex === 1 ? '选择模板' : '具体要求'" 
    wrapClassName="com-template-modal-wrap"
    @cancel="close"
  >
    <a-form ref="formRef" :model="form" :rules="rules" >
      <a-form-item label="" name="tempId" v-if="stepIndex === 1">
        <ul class="temp-tag-list">
          <a-button 
            v-for="item in props.tempList"
            :key="(item as any).id"
            class="temp-tag-item" 
            :class="{'active': form.tempId === (item as any).id}" 
            type="primary" ghost @click="tagTypeHandle(item)"
          >{{ (item as any).name }}</a-button>
        </ul>
      </a-form-item>
      <a-form-item label="" name="inputs" v-if="stepIndex === 2">
        <div class="content-wrap">
          <a-input :maxlength="100" showCount v-model:value="form.inputs" placeholder="请输入具体要求"/>
          <a-button type="default" @click="() => form.inputs = ''" >重置</a-button>
        </div>
      </a-form-item>
    </a-form>

    <template #footer>
    <!--
      <a-button type="primary" @click="prev" v-if="stepIndex === 2" ghost>上一步</a-button>
      <a-button type="primary" @click="close" ghost>取消</a-button>
      <a-button type="primary" @click="next" v-if="stepIndex === 1">下一步</a-button>
      <a-button type="primary" @click="confirm" :loading="loading" v-if="stepIndex === 2">确定</a-button>
    -->
      <a-button type="primary" @click="close" ghost>取消</a-button>
      <a-button type="primary" @click="next" v-if="stepIndex === 1">确定</a-button>
    </template>
  </a-modal>
</template>

<style>
.com-template-modal-wrap{
  
}

.com-template-modal-wrap .ant-modal-content{
  padding: 0px;
}

.com-template-modal-wrap .ant-modal-content .ant-modal-header{
  display: flex;
  align-items: center;
  height: 60px;
  padding: 0 20px;
  margin: 0;
  border-bottom: 1px solid #E0E0E0;
}
.com-template-modal-wrap .ant-modal-content .ant-modal-body{
  padding: 20px 20px 0;
}
.com-template-modal-wrap .ant-modal-content .ant-modal-footer{
  padding: 0 20px 20px;
}

.com-template-modal-wrap .ant-modal-header .ant-modal-title{
  font-size: 16px;
  font-weight: normal;
  color: #000;
}
.com-template-modal-wrap .ant-modal-header .ant-modal-title::before{
  content: '';
  display: inline-block;
  vertical-align: middle;
  margin: -2px 8px 0 0;
  width: 2px;
  height: 12px;
  background: #2373f8;
}

.com-template-modal-wrap .ant-modal-footer .ant-btn{
  width: 80px;
  height: 32px;
  border-radius: 20px;
}

.com-template-modal-wrap .temp-tag-list{
  padding: 0;
  margin: 0;
}
.com-template-modal-wrap .temp-tag-list .temp-tag-item {
  margin: 0 10px 0 0;
}
.com-template-modal-wrap .temp-tag-list .temp-tag-item.active{
  background: linear-gradient(to right, #1a6ff4, #a4bdf8);
  color: #fff;
}

.com-template-modal-wrap .content-wrap{
  display: flex;
}
.com-template-modal-wrap .content-wrap .ant-input{

}
.com-template-modal-wrap .content-wrap .ant-btn{
  margin-left: 10px;
}
</style>