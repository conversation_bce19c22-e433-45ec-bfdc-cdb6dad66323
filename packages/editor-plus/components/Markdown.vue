<template>
  <div class="markdown-content">
    <div v-html="renderedMarkdown"></div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import MarkdownIt from 'markdown-it';

const props = defineProps({
  value: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['update:value']);

const md = new MarkdownIt({
  html: true,
  linkify: true,
  typographer: true
});

const processContent = (content: string) => {
  if (!content) return '';
  
  // 处理多个思考标签
  return content
    .replace(/<think>/g, '<div class="think-content">')
    .replace(/<\/think>/g, '</div>');
};

const renderedMarkdown = computed(() => {
  const contentToRender = props.value ? processContent(props.value) : '';
  return md.render(contentToRender);
});

</script>

<style>
.think-content {
  position: relative;
  padding: 0 0 0 13px;
  margin: 0;
  color: #8b8b8b;
  margin-bottom: 10px;
}

.think-content::before {
  position: absolute;
  top: 5px;
  left: 1px;
  width: 2px;
  height: calc(100% - 10px);
  content: ' ';
  background-color: #e5e5e5;
}
</style> 