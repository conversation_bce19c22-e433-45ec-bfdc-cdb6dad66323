import { ref } from 'vue';
import { removeThinkContent } from '../../utils';
import ScenePanel from './ScenePanel.vue';
import { message } from 'ant-design-vue';
import { handleLoseSession } from '../../api/report';

// 简易模式，场景选择
const showScenePanel = ref(false);
const abortController = ref();

// 调用大模型，输出生成内容
const scenePanel2GenerateReport = (info: any) => {
  if (info.aiEditor) aiEditor = info.aiEditor;
  if (info.docRef) docRef = info.docRef;

  getLLMApi(info)

  scenePanelClose();
}

const scenePanelClose = () => {
  showScenePanel.value = false;
}


const getLLMApi = async (info: any) => {
  console.log(info, 'info')

  abortController.value = new AbortController();

  let response = await fetch(`${import.meta.env.VITE_API_PREFIX}/docWriter/generate/v1/item/text`, {
    // 如果是其他类型功能
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: localStorage.getItem('zion-auth-value'),
    },
    body: JSON.stringify({
      query: info.content,
      inputs: {},
      response_mode: 'streaming',
      conversation_id: '',
      files: [],
    }),
    signal: abortController.value?.signal
  });

  if (response.status === 401) {
    message.warning('登录已过期，请重新登录');
    return handleLoseSession();
  }

  if (!response.ok) {
    throw new Error('网络响应异常');
  }

  const reader = response.body?.getReader();
  const decoder = new TextDecoder('utf-8');

  resetState()

  let done = false;
  while (!done) {
    const { value, done: streamDone } = await reader!.read();

    done = streamDone;
    if (done) break;

    const chunk = decoder.decode(value, { stream: true });

    // 处理每个数据块
    const lines = chunk.split('\n');
    for (const line of lines) {
      const cleanLine = line.replace('data: ', '');
      try {
        const content = JSON.parse(cleanLine).answer || '';
        if (content) {
          pendingContent += content;
          throttledUpdate();
        }
      } catch (e) {
        // console.error('解析错误:', e);
      }
    }
  }
}


let fullContent = '';
let pendingContent = '';
// 上次更新的时间戳
let lastUpdateTime = 0;
// 节流间隔时间（毫秒）
const throttleInterval = 150;
let updateTimer: number | null = null;
let longWaitTimer: number | null = null;
let aiEditor: any;
let docRef: any;

// 使用节流函数减少更新频率，同时保护Markdown语法完整性
const throttledUpdate = () => {
  const now = Date.now();
  // 如果距离上次更新的时间小于节流间隔，则设置定时器等待到达间隔时间
  if (now - lastUpdateTime < throttleInterval) {
    // 如果已经有定时器在等待，则不再设置新的定时器
    if (!updateTimer) {
      updateTimer = window.setTimeout(() => {
        updateTimer = null;
        lastUpdateTime = Date.now();
        performUpdate();
      }, throttleInterval - (now - lastUpdateTime));
    }
  } else {
    // 已经超过节流间隔，立即执行更新
    lastUpdateTime = now;
    performUpdate();
  }
};

// 执行实际的更新操作
const performUpdate = () => {
  if (pendingContent) {
    // 检查pendingContent是否包含未闭合的Markdown语法标记
    const unclosedSyntax = checkUnclosedMarkdownSyntax(pendingContent);

    if (unclosedSyntax) {
      // 如果有未闭合的语法，不立即更新，等待更多内容
      // 但设置一个较长的超时，避免永远等待
      if (!longWaitTimer) {
        longWaitTimer = window.setTimeout(() => {
          // 超时后强制更新，即使语法可能不完整
          fullContent += pendingContent;
          aiEditor?.clear();
          aiEditor?.focus().insertMarkdown(removeThinkContent(fullContent));
          docRef.scrollTop = docRef.scrollHeight;
          pendingContent = '';
          longWaitTimer = null;
        }, 100); // 100ms后强制更新
      }
    } else {
      // 没有未闭合的语法，可以安全更新
      if (longWaitTimer) {
        clearTimeout(longWaitTimer);
        longWaitTimer = null;
      }

      fullContent += pendingContent;
      // 只在积累了一定量的内容后才更新编辑器
      // 避免频繁的clear和insert操作
      aiEditor?.clear();
      aiEditor?.focus().insertMarkdown(removeThinkContent(fullContent));
      // 设置内容的时候将滚动条向下滚动
      docRef.scrollTop = docRef.scrollHeight;
      pendingContent = '';
    }
  }
};

// 检查是否有未闭合的Markdown语法
const checkUnclosedMarkdownSyntax = (text: string) => {
  const openAsterisk = (text.match(/\*/g) || []).length % 2 !== 0;
  const openBacktick = (text.match(/`/g) || []).length % 2 !== 0;
  return openAsterisk || openBacktick;
};

const resetState = () => {
  fullContent = '';
  pendingContent = '';
  lastUpdateTime = 0;
  updateTimer = null;
  longWaitTimer = null;
}

export {
  ScenePanel, scenePanel2GenerateReport,
  scenePanelClose, showScenePanel
};
