<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { getSceneTypeList, getSceneListApi } from '../../api/scene'

const props = defineProps(['aiEditor', 'docRef'])

const emit = defineEmits(['close', 'success'])

const formData = ref({
  category: '',
  scene: '',
  title: null,
  content: '',
})

const formErrors = ref({
  category: '',
  scene: '',
  title: '',
  content: '',
})

const validateCategory = () => {
  if (!formData.value.category) {
    formErrors.value.category = '请选择类型'
    return false
  }
  formErrors.value.category = ''
  return true
}

const validateScene = () => {
  if (!formData.value.scene) {
    formErrors.value.scene = '请选择场景'
    return false
  }
  formErrors.value.scene = ''
  return true
}

const validateTitle = () => {
  if (!formData.value.title) {
    formErrors.value.title = '请输入字数要求'
    return false
  }
  if (formData.value.title > 100) {
    formErrors.value.title = '字数不能超过100'
    return false
  }
  formErrors.value.title = ''
  return true
}

const validateContent = () => {
  if (!formData.value.content) {
    formErrors.value.content = '请输入原文内容'
    return false
  }
  if (formData.value.content.length > 2000) {
    formErrors.value.content = '内容不能超过2000字'
    return false
  }
  formErrors.value.content = ''
  return true
}

const validateForm = () => {
  const isCategoryValid = validateCategory()
  const isSceneValid = validateScene()
  const isTitleValid = validateTitle()
  const isContentValid = validateContent()
  return isCategoryValid && isSceneValid && isTitleValid && isContentValid
}

/**
 * 完成场景表单编辑，调用生成报告函数
 */
const handleGenerate = () => {
  if (!validateForm()) {
    return
  }
  // 继续处理生成逻辑
  emit('success', {
    ...formData.value,
    aiEditor: props.aiEditor,
    docRef: props.docRef
  })
}

// const selectedCategory = ref('')
// const selectedScene = ref('')


interface ITypeItem {
  code: string
  id: number
  name: string
  parentCode: string
  remark: string
}

interface ISceneItem {
  category: string
  createBy?: null
  createId?: null
  createTime?: string
  delFlag?: null
  description?: string
  icon?: null
  id?: number
  name: string
  updateBy?: null
  updateId?: null
  updateTime?: string
}

const menuItems = ref<ITypeItem[]>([])

const handleMenuClick = (item: ITypeItem) => {
  formData.value.category = item.code
  formData.value.scene = undefined
  formErrors.value.category = ''
  formErrors.value.scene = ''
  getSceneList(formData.value.category)
}
const handleSceneClick = (item?: any) => {
  formData.value.scene = item.name
  formErrors.value.scene = ''
}

const scenes = ref<ISceneItem[]>([])

const getTypeList = async () => {
  let res = await getSceneTypeList('scene_category') as any;
  if (res.success === true) {
    menuItems.value = res.data

    // 默认选中第一个
    formData.value.category = menuItems.value[0].code
    getSceneList(formData.value.category)
  }
}

const getSceneList = async (code: string) => {
  let params = {
    name: '',
    desc: '',
    category: code,
    mode: '1,2'
  }
  let res = await getSceneListApi(params) as any;
  if (res.success === true) {
    scenes.value = res.data
  }
}

onMounted(() => {
  getTypeList();
})

/**
 * 关闭面板
 */
const close = () => {
  emit('close')
}



</script>

<template>
  <div class="scene-panel relative overflow-y-auto">
    <div class="panel-title">
      <img src="../../assets/img/icon-ai.png" class="icon" />
      选择场景
      <img src="../../assets/img/panel-close.png" class="close-icon" @click="close" />
    </div>

    <!-- 类型选择 -->
    <div class="section-title">
      类型
    </div>
    <div class="type-grid">
      <div
        v-for="type in menuItems"
        :key="type.id"
        class="type-item"
        :class="{
          'active': formData.category === type.code,
          'error-border': formErrors.category && !formData.category
        }"
        @click="handleMenuClick(type)"
      >
        {{ type.name }}
      </div>
    </div>
    <div v-if="formErrors.category" class="text-red-500 text-[12px] mt-[-12px] mb-[12px]">
      {{ formErrors.category }}
    </div>

    <!-- 场景选择 -->
    <div class="section-title">
      场景
    </div>
    <div class="scene-grid">
      <div
        v-for="scene in scenes"
        class="scene-item"
        :class="{
          'active': formData.scene === scene.name,
          'error-border': formErrors.scene && !formData.scene
        }"
        @click="handleSceneClick(scene)"
      >
        {{ scene.name }}
      </div>
    </div>
    <div v-if="formErrors.scene" class="text-red-500 text-[12px] mt-[-12px] mb-[12px]">
      {{ formErrors.scene }}
    </div>

    <!-- 格式参考 -->
    <div class="format-ref">
      <img src="../../assets/img/icon-yh.png" class="icon" />
      添加格式参考（套用参考文稿得排版及格式规范生成）
    </div>

    <!-- 报告主题 -->
    <div class="w-full text-[12px]">
      <div class="mb-4">
        <div class="text-[12px] text-[#666] mb-2">
          报告主题<span class="text-red-500">*</span>
          <span class="text-gray-400">(不能超过100字)</span>
        </div>
        <a-input
          class="bg-white w-[100%]"
          :min="1"
          :max="100"
          v-model:value="formData.title"
          placeholder="请输入报告主题"
          :status="formErrors.title ? 'error' : ''"
          @blur="validateTitle"
        />
        <div v-if="formErrors.title" class="text-red-500 text-[12px] mt-1">
          {{ formErrors.title }}
        </div>
      </div>
      <div class="mb-4">
        <div class="text-[12px] text-[#666] mb-2">
          报告约束<span class="text-red-500">*</span>
          <span class="text-gray-400">(不能超过2000字)</span>
        </div>
        <a-textarea
          class="bg-white w-[100%]"
          v-model:value="formData.content"
          :auto-size="{ minRows: 4, maxRows: 4 }"
          :min="1"
          :max="2000"
          placeholder="请输入报告约束"
          :status="formErrors.content ? 'error' : ''"
          @blur="validateContent"
        />
        <div v-if="formErrors.content" class="text-red-500 text-[12px] mt-1">
          {{ formErrors.content }}
        </div>
      </div>
    </div>

    <!-- 生成按钮 -->
    <div class="generate-btn  mt-2" @click="handleGenerate">
      <img src="../../assets/img/generate.png" class="icon" />
      开始生成
    </div>
  </div>
</template>

<style>
.scene-panel {
  width: 420px;
  height: calc(100% - 0px);
  background-color: #ECF3FE;
  position: absolute;
  top: 0;
  right: 0;
  z-index: 10;
  padding: 20px 15px;
  font-family: PingFang SC, sans-serif;
  box-sizing: border-box;

  &::-webkit-scrollbar {
    width: 5px;
  }
  &::-webkit-scrollbar-thumb {
    border-radius: 10px;
    -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,.3);
    background-color: #b8b8b8;
  }
}

.scene-panel * {
  box-sizing: border-box;
}

.scene-panel .panel-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 20px;
  position: relative;
}

.scene-panel .panel-title .icon {
  width: 20px;
  height: 20px;
  margin-right: 8px;
}

.scene-panel .panel-title .close-icon {
  width: 16px;
  height: 16px;
  position: absolute;
  right: 0;
  cursor: pointer;
  transition: opacity 0.3s;
}

.scene-panel .panel-title .close-icon:hover {
  opacity: 0.7;
}

.scene-panel .section-title {
  font-size: 14px;
  color: #fff;
  padding: 5px 12px;
  border-radius: 4px;
  margin: 0 0 14px;
  border-radius: 13.5px;
  background: linear-gradient(to right, #3180FB 20%, rgba(241, 247, 255, 0));
}

.scene-panel .type-grid {
  width: 100%;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
  margin-bottom: 20px;
}

.scene-panel .type-item {
  background-color: #fff;
  padding: 8px;
  text-align: center;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  border: 1px solid #fff;
  white-space: nowrap;
}

.scene-panel .type-item.active {
  color: #0265FF;
  border: 1px solid #0265FF;
}

.scene-panel .scene-grid {
  width: 100%;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
  margin-bottom: 20px;
}

.scene-panel .scene-item {
  background-color: #fff;
  padding: 8px;
  text-align: center;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  border: 1px solid #fff;
  white-space: nowrap;
}

.scene-panel .scene-item.active {
  color: #0265FF;
  border: 1px solid #0265FF;
}
/* .scene-panel .type-item {
  background-color: #E8F4FF;
  padding: 8px;
  border-radius: 4px;
  font-size: 14px;
  margin-bottom: 20px;
} */

.scene-panel .format-ref {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #666;
  margin: 15px 0;
}

.scene-panel .format-ref .icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}


.scene-panel .generate-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #297CFC;
  color: #fff;
  padding: 9px 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  margin-top: 20px;
  transition: all .3s linear;
}

.scene-panel .generate-btn:hover{
  opacity: 0.8;
}

.scene-panel .generate-btn .icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}

.error-border {
  border: 1px solid #ff4d4f !important;
}
</style>