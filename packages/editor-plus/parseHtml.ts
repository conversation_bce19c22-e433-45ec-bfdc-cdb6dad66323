/**
 * 处理HTML字符串，进行DOM转换和优化
 * @param htmlString 要处理的HTML字符串
 * @returns 处理后的HTML字符串
 */
export const parseHtml = (htmlString: string): string => {
    if (!htmlString) return "";
    
    // 解析HTML字符串为DOM文档
    const doc = new DOMParser().parseFromString(htmlString, "text/html");
    
    // 处理任务列表(ul元素)
    processTaskLists(doc);
    
    // 确保所有li元素都有内容
    ensureListItemContent(doc);
    
    // 处理图片位置
    adjustImagePositions(doc);
    
    // 处理表格
    processTables(doc);
    
    // 将处理后的DOM转换为字符串
    return convertDomToString(doc);
};

/**
 * 处理任务列表(ul元素)
 */
const processTaskLists = (doc: Document): void => {
    const ulElements = doc.querySelectorAll("ul");
    
    ulElements.forEach(ul => {
        const classValue = ul.getAttribute("class");
        
        if (classValue?.includes("task-list")) {
            // 移除所有属性并设置新的data-type
            ul.getAttributeNames().forEach(attr => ul.removeAttribute(attr));
            ul.setAttribute("data-type", "taskList");
            
            // 处理ul的第一个子元素如果是p标签
            const firstChild = ul.firstElementChild;
            if (firstChild?.tagName === "P") {
                const fragment = document.createDocumentFragment();
                firstChild.childNodes.forEach(node => fragment.appendChild(node.cloneNode(true)));
                firstChild.replaceWith(fragment);
            }
            
            // 处理li元素
            processListItems(ul);
        }
    });
};

/**
 * 处理列表项(li元素)
 */
const processListItems = (ul: HTMLUListElement): void => {
    ul.querySelectorAll("li").forEach(li => {
        // 移除所有属性
        li.getAttributeNames().forEach(attr => li.removeAttribute(attr));
        
        // 处理复选框
        const checkbox = li.querySelector("input[type='checkbox']");
        if (checkbox) {
            li.setAttribute("data-type", "taskItem");
            li.setAttribute("data-checked", checkbox.hasAttribute("checked") ? "true" : "false");
        }
    });
};

/**
 * 确保所有li元素都有内容
 */
const ensureListItemContent = (doc: Document): void => {
    const liElements = doc.querySelectorAll("li");
    liElements.forEach(li => {
        if (!li.innerHTML) {
            li.innerHTML = "<p></p>";
        }
    });
};

/**
 * 调整图片位置
 */
const adjustImagePositions = (doc: Document): void => {
    const images = doc.querySelectorAll("body>p>img");
    const body = doc.querySelector("body");
    
    if (images.length > 0 && body) {
        images.forEach(img => {
            const parent = img.parentNode;
            if (parent) {
                const index = Array.prototype.indexOf.call(body.children, parent);
                body.insertBefore(img, body.children[index]);
            }
        });
    }
};

/**
 * 处理表格
 */
const processTables = (doc: Document): void => {
    const tables = doc.querySelectorAll("table");
    tables.forEach(table => ph(table));
};

/**
 * 将DOM转换为字符串
 */
const convertDomToString = (doc: Document): string => {
    let result = "";
    
    doc.body.childNodes.forEach(node => {
        if (node.nodeType === Node.TEXT_NODE) {
            result += node.textContent;
        } else if (node.nodeType === Node.ELEMENT_NODE) {
            const element = node as Element;
            // 特殊处理包含img但不是a标签的元素
            if (element.querySelector("img") && element.tagName !== "A") {
                result += element.innerHTML;
            } else {
                result += element.outerHTML;
            }
        }
    });
    
    return result;
};

/**
 * 处理表格元素，进行格式转换和样式调整
 * @param table 要处理的表格元素
 */
const ph = (table: HTMLTableElement): void => {
    // 移除表格所有属性
    table.getAttributeNames().forEach(attr => table.removeAttribute(attr));
    
    // 设置表格基本样式
    table.setAttribute('border', '1');
    table.setAttribute('cellspacing', '0');
    table.setAttribute('cellpadding', '5');
    table.setAttribute('style', 'width: 100%; border-collapse: collapse;');
    
    // 处理表头(thead)
    const thead = table.querySelector('thead');
    if (thead) {
        thead.querySelectorAll('th').forEach(th => {
            th.setAttribute('style', 'background-color: #f5f5f5; text-align: left;');
        });
    }
    
    // 处理表体(tbody)
    const tbody = table.querySelector('tbody');
    if (tbody) {
        tbody.querySelectorAll('tr').forEach((tr, rowIndex) => {
            // 交替行颜色
            if (rowIndex % 2 === 0) {
                tr.setAttribute('style', 'background-color: #ffffff;');
            } else {
                tr.setAttribute('style', 'background-color: #f9f9f9;');
            }
            
            // 处理单元格
            tr.querySelectorAll('td').forEach(td => {
                td.setAttribute('style', 'border: 1px solid #ddd; padding: 8px;');
            });
        });
    }
    
    // 处理表尾(tfoot)
    const tfoot = table.querySelector('tfoot');
    if (tfoot) {
        tfoot.setAttribute('style', 'background-color: #f5f5f5; font-weight: bold;');
    }
};