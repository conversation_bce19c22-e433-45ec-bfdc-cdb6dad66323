import type { App } from 'vue'

import EditorPlus from './index.vue'
import Antd from 'ant-design-vue';
//实现按需引入*
export { EditorPlus } 
import 'ant-design-vue/dist/reset.css';
// 批量的引入*
const components = [
  EditorPlus
];

const install = function(App:App) {
	components.forEach((component) => {
		App.component(component.name as string, component);
	});
	App.use(Antd);
};
export default { install } 
