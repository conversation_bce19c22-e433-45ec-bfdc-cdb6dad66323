<template>
  <div>
    <div id="aiEditor" ref="divRef" style="padding: 0; margin: 0">
      <div class="aie-container" style="background-color: #f3f4f6">
        <div class="aie-header-panel">
          <div class="aie-container-header" style="background: #fff">
            <div class="action-btn-box">
              <a-button size="small" style="margin-right: 10px" @click="goHome">返回首页</a-button>
              <a-button type="primary" style="margin-right: 10px" size="small" @click="saveReport">保存报告</a-button>
              <!-- <a-button type="primary" style="margin-right: 10px" size="small" :disabled="isLoading" @click="openCommTemplate">模板选择</a-button>
              <a-button type="primary" size="small" v-if="!isSpecialty" :disabled="isLoading" @click="() => { showScenePanel = true; }">模板场景</a-button> -->
            </div>
          </div>
        </div>
        <div class="aie-main">
          <div class="aie-menu-content">

            <div class="aie-directory relative">
              <h5>文档目录</h5>
              <a-spin v-if="queryDetailLoading" class="absolute left-0 top-60px z-1 w-full h-[calc(100%-130px)] flex flex-col justify-center items-center bg-#fff rounded-md" />
              <div id="outline"></div>
            </div>
          </div>

          <div class="aie-container-panel relative" :class="{ 'aie-container-panel-full': isFullscreen }">
            <div class="aie-container-panel-box relative" ref="docRef">
              <a-spin v-if="queryDetailLoading" class="absolute left-0 top-0 z-1 w-full h-full flex flex-col justify-center items-center bg-#fff rounded-md" />
              <div class="aie-container-main"></div>
            </div>
            <!-- 专业版生成loding -->
            <div class="loading-box w-full h-full absolute left-[0] top-[0] z-1000 flex justify-center items-center" v-if="specialGenerateLoading">
              <div class="loading-tips">正在努力撰写中。。。</div>
              <img src="./assets/img/generate-loading-1.gif" style="width: 100px; height: 100px" alt="loading" class="w-10 h-10" />
              <div class="loading-btn" @click="stopGeneration">停止生成</div>
            </div>
            <!-- 悬浮按钮 -->
            <div class="floatBtn" :class="{ 'floatBtn-active': isChat }" @click.stop="isChat = !isChat">
              <img :src="aiImg" alt="AI" />
            </div>
          </div>

          <div class="aie-ai-content" :class="{ 'aie-ai-content-close': !isChat }">
            <div class="aie-ai-chat">
              <div class="chat-tabar">
                <div :class="`tab ${tabType == 'chat' ? 'active' : ''}`" @click="ontab('chat')">
                  <span class="icon-tab"></span>
                  报告撰写助手
                </div>
                <div class="tab-op" v-if="tabType === 'chat'">
                  <div class="btn" @click="newChat" title="新建对话">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z" fill="#81868F" />
                    </svg>
                  </div>
                </div>
              </div>
              <div class="chat-main">
                <div class="panel" v-if="tabType === 'chat'">
                  <div class="chat-content" :class="{ 'tag': commTemp }">
                    <div v-for="(message, index) in messages" :key="index" :class="`message ${message.role} ${(message.type === 'default' && commTemp) ? 'hide' : ''}`">
                      <div :class="{
                        'message-bubble': true,
                        'default-message': message.type === 'default',
                      }" :style="{
                        cursor: message.type === 'default' ? 'pointer' : '',
                      }">
                        <div class="message-content" @click="sendMessage1(message)">
                          <span v-if="message.type === 'system'" v-html="message.content" :style="{
                            fontWeight:
                              message.type === 'system' ? 'bold' : 'normal',
                          }"></span>
                          <Markdown :value="message.content" v-else />
                        </div>
                      </div>
                      <div class="message-actions assistant" v-if="
                        !isLoading &&
                        message.role === 'assistant' &&
                        message.type === 'generate'
                      ">
                        <div class="action-btn" title="应用" @click="applyMessage(message.content)">
                          <svg t="1747798388245" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2044" width="18" height="18">
                            <path
                              d="M736.72 440a23.44 23.44 0 1 0 0-46.8H325.2l60.48-61.6a23.44 23.44 0 0 0 6.32-16.56 22.8 22.8 0 0 0-6.96-16.48 23.36 23.36 0 0 0-33.12 0L213.68 440zM219.84 617.12a23.44 23.44 0 0 0 23.44 23.44h414.56l-61.44 61.44a23.36 23.36 0 0 0 0 33.12 23.36 23.36 0 0 0 33.12 0l141.44-141.36h-528a23.44 23.44 0 0 0-23.12 23.36z"
                              p-id="2045" fill="#81868F"></path>
                            <path d="M512 72a440 440 0 1 0 440 440A440.48 440.48 0 0 0 512 72z m0 59.92A380.08 380.08 0 1 1 131.92 512 380.48 380.48 0 0 1 512 131.92z" p-id="2046" fill="#81868F"></path>
                          </svg>
                        </div>
                        <div class="action-btn" title="追加" @click="insertMessage(message.content)">
                          <svg t="1747797882478" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4623" width="17" height="17">
                            <path
                              d="M928.88 789.87V548.29a35.35 35.35 0 0 0-70.66 0V790.7a69.13 69.13 0 0 1-69.17 69.07H235.48a69.13 69.13 0 0 1-69.17-69.07V237.4a69.12 69.12 0 0 1 69.17-69.06h242.6a35.16 35.16 0 1 0 0-70.31H237.21c-76.4 0-138.33 61.84-138.33 138.13v553.71c0 76.29 61.93 138.13 138.33 138.13h553.33c76.4 0 138.34-61.84 138.34-138.13z m-242-451.57v140.55a34.45 34.45 0 0 0 68.89 0V338.3h139.32a33.74 33.74 0 1 0 0-67.47H755.82V132.42a34.45 34.45 0 0 0-68.89-0.06v138.47H546.49a33.74 33.74 0 1 0 0 67.47z"
                              p-id="4624" fill="#81868F"></path>
                          </svg>
                        </div>
                        <div class="action-btn" title="复制" @click="copyMessage(message.content)">
                          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" clip-rule="evenodd"
                              d="M9.917 6.375h7.291v9.792h1.042V6.333a1 1 0 0 0-1-1H7.417v1.042h2.5ZM6.375 17.21v-8.75h8.75v8.75h-8.75ZM5.333 8.25c0-.46.373-.833.834-.833h9.166c.46 0 .834.373.834.833v9.167c0 .46-.374.833-.834.833H6.167a.833.833 0 0 1-.834-.833V8.25Z"
                              fill="#81868F"></path>
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="chat-input-contailer">
                    <div class="chat-tip-wrap" v-if="commTemp">
                      已选模板：<a-tag color="blue" :closable="!isLoading" @close="commTempClean">{{ commTemp }}</a-tag>
                    </div>
                    <div class="chat-input">
                      <textarea class="input-text" ref="textareaRef" placeholder="请输入您的需求..." :disabled="isLoading" @input="adjustTextareaHeight" @keydown.enter.prevent="handleEnterKey"></textarea>
                    </div>
                    <div class="btn-box">
                      <div class="btn-group left">
                        <div class="clear-btn" :class="{ 'clear-btn-count': chatWordCount }" @click="clearInput">清除</div>
                      </div>
                      <div class="btn-group right">
                        <template v-if="!isLoading">
                          <div class="send-btn" :class="{ 'send-btn-count': chatWordCount }" @click="sendMessage"></div>
                        </template>
                        <template v-else>
                          <div class="stop-btn" @click="stopGeneration"></div>
                        </template>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!-- 操作已选文字面板 -->
            <SelectPanel v-if="showSelectedActionPanel" :selectedContent="currentSelectContent" :actionType="currentActionType" @insert="insertOutputContent" @replace="replaceContent" @close="closeSelectedActionPanel"
              @apply-correction="handleApplyCorrection" />

            <!-- 场景选择 -->
            <ScenePanel v-if="showScenePanel" @close="scenePanelClose" @success="scenePanel2GenerateReport" :aiEditor="aiEditor" :docRef="docRef" />
          </div>
        </div>
        <div class="aie-container-footer"></div>
      </div>
    </div>

    <SaveReport v-model:open="showSaveReport" @close="closeSaveReport" @updateSeccess="updateSeccess" :isSpecialty="isSpecialty" :content="saveContent" :reportDetail="reportDetail" :programmeId="programmeId" :itemId="itemId" />

    <ComTemplate v-model:open="showCommTemplate" :tempList="tempList" @close="closeCommTemplate" @success="successCommTemplate" />

  </div>
</template>

<script setup lang="ts">
  import { AiEditor } from 'aieditor';
  import 'aieditor/dist/style.css';
  import { onMounted, onUnmounted, ref } from 'vue';
  import SelectPanel from './components/SelectPanel.vue';

  import { message, Textarea } from 'ant-design-vue';
  import { getRecommendQuestionsApi, getReportDetailApi, sendReplyMessage, handleLoseSession } from './api/report';
  import ComTemplate from './components/ComTemplate.vue';
  import Markdown from './components/Markdown.vue';
  import SaveReport from './components/SaveReport.vue';
  import {
    ScenePanel,
    scenePanel2GenerateReport, scenePanelClose,
    showScenePanel
  } from './components/scenePanel/scenePanel2Parent';
  import { parseHtml } from './parseHtml';
  import { ReportDetail } from './type/report';
  import { removeThinkContent } from './utils';

  import { DownloadOutlined } from '@ant-design/icons-vue';
  import screenfull from 'screenfull';
  import { createDraggable, createSpring } from 'animejs';
  import aiImg from './assets/img/ai.gif';

  const { llm_url, llm_key, llm_model, tempList } = defineProps({
    llm_url: {
      required: true,
      type: String,
      default: () => '',
    },
    llm_key: {
      required: true,
      type: String,
      default: () => '',
    },
    llm_model: {
      required: true,
      type: String,
      default: () => '',
    },
    tempList: {
      required: true,
      type: Array,
      default: () => [],
    },
  });

  /** 变量区域 */

  const divRef = ref();
  const textareaRef = ref();
  let aiEditor: AiEditor | null = null;
  const tabType = ref('chat');
  const textareaHeight = ref('auto');
  const messages = ref(
    [] as Array<{ role: string; content: any; type?: string }>
  );

  /** loading 数据加载变量 */
  const isLoading = ref(false);
  const queryDetailLoading = ref(false);

  const abortController = ref<AbortController | null>(null);
  const abortControllers = ref<any[]>([]);
  const isStopAllFetch = ref(false);

  /** 保存报告相关变量 */
  const showSaveReport = ref(false);
  const saveContent = ref('');
  const reportDetail = ref<Partial<ReportDetail>>({
    id: '',
    docName: '',
    reportType: '',
    originalFilename: '',
    docContent: '',
    description: '',
    coverPhoto: '',
    createBy: '',
  });
  const greyBtnActive = ref(0);

  const isFullscreen = ref(false);
  const isChat = ref(true);
  const programmeId = ref(''); // 传入的项目 ID
  const itemId = ref(''); // 传入的项目 Item ID
  const scene = ref(''); // 传入的场景标识
  const topic = ref(''); // 传入的主题标识
  // 是否专业版
  const isSpecialty = ref(false);
  // 添加一个标志，防止重复生成报告
  const hasGeneratedReport = ref(false);
  onMounted(() => {
    aiEditor = new AiEditor({
      element: divRef.value as Element,
      placeholder: '点击输入内容...',
      content: '',
      textSelectionBubbleMenu: {
        items: [
          {
            id: 'kx',
            title: '扩写',
            icon: '<div style="display:flex;align-items:center;"><svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="14px" height="13px" viewBox="0 0 14 13" enable-background="new 0 0 14 13" xml:space="preserve"><image id="image0" width="14" height="13" x="0" y="0" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAANBAMAAABr8kJMAAAAIGNIUk0AAHomAACAhAAA+gAAAIDoAAB1MAAA6mAAADqYAAAXcJy6UTwAAAAeUExURQAAAAJl/wBg/wBg/wBk/wBj/wBm/wFl/wJl/////0fm11cAAAAIdFJOUwD7GCAcHxS/hx/mUgAAAAFiS0dECfHZpewAAAAHdElNRQfpBgYBDztg5dVKAAAAJ0lEQVQI12OQ6ACBRgZlYxAwYkAHHRDAgAvgkncNDQ1NgMoXMDAAAGOfEDq1OEplAAAAJXRFWHRkYXRlOmNyZWF0ZQAyMDI1LTA2LTA2VDAxOjE1OjU5KzAwOjAwrhF1cwAAACV0RVh0ZGF0ZTptb2RpZnkAMjAyNS0wNi0wNlQwMToxNTo1OSswMDowMN9Mzc8AAAAodEVYdGRhdGU6dGltZXN0YW1wADIwMjUtMDYtMDZUMDE6MTU6NTkrMDA6MDCIWewQAAAAAElFTkSuQmCC" /></svg><span style="margin-left: 10px">扩写</span></div>',
            onClick: (editor) => {
              handleActionContent(editor, 'elaborateOn');
            },
          },
          {
            id: 'sx',
            title: '缩写',
            icon: '<div style="display:flex;align-items:center;"><svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="14px" height="5px" viewBox="0 0 14 5" enable-background="new 0 0 14 5" xml:space="preserve"><image id="image0" width="14" height="5" x="0" y="0" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAFBAMAAACHocAhAAAAIGNIUk0AAHomAACAhAAA+gAAAIDoAAB1MAAA6mAAADqYAAAXcJy6UTwAAAAPUExURQAAAAJl/wFl/wJl/////7wWHQUAAAADdFJOUwD7v1bgUrUAAAABYktHRASPaNlRAAAAB3RJTUUH6QYGAQ05vN3W5AAAABVJREFUCNdjEDYGAUMGXAAkq8DAAAAx/AIwD6PLsAAAACV0RVh0ZGF0ZTpjcmVhdGUAMjAyNS0wNi0wNlQwMToxMzo1NyswMDowMPMwfmkAAAAldEVYdGRhdGU6bW9kaWZ5ADIwMjUtMDYtMDZUMDE6MTM6NTcrMDA6MDCCbcbVAAAAKHRFWHRkYXRlOnRpbWVzdGFtcAAyMDI1LTA2LTA2VDAxOjEzOjU3KzAwOjAw1XjnCgAAAABJRU5ErkJggg==" /></svg><span style="margin-left: 10px">缩写</span></div>',
            onClick: (editor) => {
              handleActionContent(editor, 'abbreviation');
            },
          },
          {
            id: 'rs',
            title: '润色',
            icon: '<div style="display:flex;align-items:center;"><svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="14px" height="14px" viewBox="0 0 14 14" enable-background="new 0 0 14 14" xml:space="preserve"><image id="image0" width="14" height="14" x="0" y="0" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAMAAAAolt3jAAAAIGNIUk0AAHomAACAhAAA+gAAAIDoAAB1MAAA6mAAADqYAAAXcJy6UTwAAACWUExURQAAAAJm/wBq/wJk/wJl/wFl/wBm/wBg/wBg/wBl/wJl/wJl/wBg/wBg/wJl/wJl/wJl/wJk/wJm/wBk/wJk/wJl/wBm/wBA/wJk/wJl/wJl/wJl/wJl/wBj/wBj/wBl/wBj/wJk/wJl/wNk/wJm/wFl/wFl/wFk/wJl/wFl/wJl/wFl/wJl/wBk/wJk/wBi/wJl/////+ljcEQAAAAwdFJOUwCbDHjvsygIIETjfBAYz990cNNU5/MUBKjXkJxoJCwwUICXy4+7w8Tbz5/HpECENF7dYYEAAAABYktHRDHZ2x1yAAAAB3RJTUUH6QYGAQkGbtc+3QAAAIlJREFUCNdNz+sSgiAQBeCTJRRGXhILy2y72T3f/+laSGY6P5j9GGaXBVxGEf4ynsQCcho4U8lcL9IsOJe6WIoy51I6swBhKqzWLGFZqJXExmwhlFcp+EyCbOP77AZpr9bEg/bugg7HE+qCy7MbRKTtpYvQXPue39Ht/ujcn9v0WQEvouy3wfuDL3p4CKZ9in0pAAAAJXRFWHRkYXRlOmNyZWF0ZQAyMDI1LTA2LTA2VDAxOjA5OjA2KzAwOjAwyws0sAAAACV0RVh0ZGF0ZTptb2RpZnkAMjAyNS0wNi0wNlQwMTowOTowNiswMDowMLpWjAwAAAAodEVYdGRhdGU6dGltZXN0YW1wADIwMjUtMDYtMDZUMDE6MDk6MDYrMDA6MDDtQ63TAAAAAElFTkSuQmCC" /></svg><span style="margin-left: 10px">润色</span></div>',
            onClick: (editor) => {
              handleActionContent(editor, 'polish');
            },
          },
          {
            id: 'zy',
            title: '摘要',
            icon: '<div style="display:flex;align-items:center;"><svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="14px" height="14px" viewBox="0 0 14 14" enable-background="new 0 0 14 14" xml:space="preserve"><image id="image0" width="14" height="14" x="0" y="0" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAMAAAAolt3jAAAAIGNIUk0AAHomAACAhAAA+gAAAIDoAAB1MAAA6mAAADqYAAAXcJy6UTwAAABdUExURQAAAAJl/wBl/wBk/wJl/wJk/wJl/wJl/wJl/wNl/wFm/wJl/wFl/wFl/wJl/wJk/wJl/wBq/wBj/wBg/wFl/wBg/wBg/wJm/wJl/wBn/wBl/wBo/wNl/wJl/////xHwGmIAAAAddFJOUwDjMEB3gGj732C3p7O/n3BvDB8IuxAgo89DPyDDVRuvogAAAAFiS0dEHnIKICsAAAAHdElNRQfpBgUJDzNymKMuAAAAW0lEQVQI11XPSRKAIAxE0RZUVAQcQMHh/te0xBTg2/1NqoPqLlRgvMAAXjek5kArukT0GCQSOULpnFq9aXQ0/dNQzku0Utpvg6V0W+Qoy8u7DwcJXuO88gfX+QA/TQmwOxAFOQAAACV0RVh0ZGF0ZTpjcmVhdGUAMjAyNS0wNi0wNVQwOToxNTo1MSswMDowMO41XIsAAAAldEVYdGRhdGU6bW9kaWZ5ADIwMjUtMDYtMDVUMDk6MTU6NTErMDA6MDCfaOQ3AAAAKHRFWHRkYXRlOnRpbWVzdGFtcAAyMDI1LTA2LTA1VDA5OjE1OjUxKzAwOjAwyH3F6AAAAABJRU5ErkJggg==" /></svg><span style="margin-left: 10px">摘要</span></div>',
            onClick: (editor) => {
              handleActionContent(editor, 'digest');
            },
          },
          {
            id: 'jc',
            title: '纠错',
            icon: '<div style="display:flex;align-items:center;"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none" version="1.1" width="16" height="16" viewBox="0 0 16 16"><g><g><path d="M6.651229117889404,8.845963018035889C6.348329117889405,8.54037301803589,5.858929117889405,8.54037301803589,5.556029117889405,8.845963018035889L4.181300117889404,10.24026301803589C3.8801786178894044,10.545503018035888,3.8801786178894044,11.045653018035889,4.181300117889404,11.351223018035888L4.181460117889404,11.351223018035888C4.484360117889405,11.656813018035889,4.973769117889404,11.656813018035889,5.276669117889404,11.351223018035888L6.651389117889405,9.956923018035889C6.952539117889405,9.649583018035889,6.952539117889405,9.153303018035889,6.651389117889405,8.845963018035889L6.651229117889404,8.845963018035889ZM11.235739117889405,4.196188018035889C10.932709117889404,3.8907548180358886,10.443399117889404,3.8907548180358886,10.140369117889405,4.196188018035889L8.729799117889403,5.627163018035889C8.428499117889405,5.934373018035888,8.428499117889405,6.430743018035889,8.729799117889403,6.737963018035888L8.730119117889404,6.737963018035888C9.031079117889405,7.043533018035889,9.523889117889404,7.043533018035889,9.825169117889406,6.737963018035888L11.236059117889404,5.307143018035889C11.537359117889405,4.999933018035889,11.537359117889405,4.503562018035889,11.236059117889404,4.196350018035889L11.235739117889405,4.196188018035889ZM5.276829117889404,4.240814018035889C4.975539117889404,3.9354059180358885,4.482742117889404,3.9354059180358885,4.181620117889405,4.240814018035889L4.181460117889404,4.240977018035888C3.8803388178894043,4.546547018035889,3.8803388178894043,5.046363018035889,4.181460117889404,5.351773018035889L10.096689117889404,11.351053018035888C10.397969117889405,11.656633018035889,10.890769117889405,11.656633018035889,11.191739117889405,11.351053018035888L11.192059117889404,11.351053018035888C11.493339117889406,11.045483018035888,11.493339117889406,10.545343018035888,11.192059117889404,10.24009301803589L5.276829117889404,4.240814018035889Z" fill="#0265FF" fill-opacity="1" style="mix-blend-mode:passthrough"/></g><g><path d="M15.7742,14.6598L13.7401,12.597C14.7623,11.2735,15.3732,9.60755,15.3732,7.79603C15.3732,3.49045,11.9316,0,7.68644,0C3.4413,0,0,3.49029,0,7.79603C0,12.1014,3.4413,15.5921,7.6866,15.5921C9.58725,15.5921,11.3258,14.8907,12.6676,13.7309L14.6788,15.7711C14.9819,16.0763,15.4711,16.0763,15.7742,15.7711L15.7742,15.7707C16.0754,15.4655,16.0751,14.965,15.7742,14.6598ZM7.6866,14.0206C4.30242,14.0206,1.54913,11.2282,1.54913,7.79603C1.54913,4.36368,4.30242,1.57118,7.6866,1.57118C11.0708,1.57118,13.8242,4.36368,13.8242,7.79603C13.8241,11.2282,11.0706,14.0206,7.6866,14.0206Z" fill="#0265FF" fill-opacity="1" style="mix-blend-mode:passthrough"/></g></g></svg><span style="margin-left: 10px">纠错</span></div>',
            onClick: (editor) => {
              handleActionContent(editor, 'correction');
            },
          },
        ],
      },
      toolbarExcludeKeys: ['ai'],
      draggable: false,
      contentRetention: true,
      ai: {
        models: {
          custom: {
            url: llm_url,
            headers: () => {
              return {
                'Content-Type': 'application/json',
                Authorization: localStorage.getItem('zion-auth-value'),
              };
            },
            wrapPayload: (message: string) => {
              const prompt = {
                model: llm_model,
                messages: [
                  {
                    content: message,
                    role: 'user',
                  },
                ],
                stream: true,
              };
              return JSON.stringify(prompt);
            },
            parseMessage: (message: string) => {
              if (message === '[DONE]') return { role: 'assistant', content: '' };

              try {
                const data = JSON.parse(message);
                const content = data.choices?.[0]?.delta?.content || '';
                return { role: 'assistant', content };
              } catch {
                return { role: 'assistant', content: '模型返回错误' };
              }
            },
            //protocol: "sse"
          },
        },
        commands: [],
      },
      toolbarKeys: [
        'undo',
        'redo',
        'brush',
        'eraser',
        '|',
        'heading',
        'font-family',
        'font-size',
        '|',
        'bold',
        'italic',
        'underline',
        'strike',
        'link',
        'code',
        'subscript',
        'superscript',
        'hr',
        'todo',
        'emoji',
        '|',
        'highlight',
        'font-color',
        '|',
        'align',
        'line-height',
        '|',
        'bullet-list',
        'ordered-list',
        'indent-decrease',
        'indent-increase',
        'break',
        '|',
        'image',
        'video',
        'attachment',
        'quote',
        'code-block',
        'table',
        '|',
        'source-code',
        // 'printer',
        'fullscreen',
        // '|',
        // {
        //   icon: '<svg t="1749699643593" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="19289" width="256" height="256"><path d="M915.2 224c0-19.2-12.8-38.4-38.4-32H582.4V115.2h-51.2L108.8 192v640c140.8 25.6 281.6 44.8 422.4 70.4h57.6V832h275.2c12.8 0 32 0 44.8-6.4 12.8-19.2 6.4-38.4 6.4-57.6V224zM416 633.6c-12.8 6.4-38.4 0-57.6 0-12.8-57.6-25.6-115.2-38.4-179.2-12.8 57.6-25.6 121.6-38.4 179.2-19.2 0-38.4 0-51.2-6.4-12.8-76.8-32-160-44.8-243.2h44.8c6.4 57.6 19.2 115.2 25.6 179.2 12.8-57.6 25.6-121.6 38.4-179.2h51.2c12.8 64 25.6 121.6 38.4 185.6 12.8-64 19.2-128 32-192h57.6c-19.2 83.2-38.4 172.8-57.6 256z m473.6 172.8H582.4v-70.4h236.8v-38.4H582.4v-44.8h236.8v-38.4H582.4v-44.8h236.8v-38.4H582.4v-44.8h236.8V448H582.4v-44.8h236.8v-38.4H582.4V320h236.8v-38.4H582.4v-64h300.8v588.8z m0 0" p-id="19290"></path></svg>',
        //   onClick: () => {
        //     importDocument();
        //   },
        //   tip: "导入Word"
        // },
        // {
        //   icon: '<svg t="1748915744021" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="8398" width="256" height="256"><path d="M517.333333 280.8h-6.506666v45.92H517.333333a27.146667 27.146667 0 0 0 11.573334-1.813333 12.266667 12.266667 0 0 0 5.333333-6.293334 40.48 40.48 0 0 0 1.92-14.613333 28.586667 28.586667 0 0 0-4.32-18.293333 18.613333 18.613333 0 0 0-14.506667-4.906667zM431.04 278.72h-6.08a5.333333 5.333333 0 0 0-5.333333 5.333333v6.453334a5.333333 5.333333 0 0 0 5.333333 5.333333h5.653333a42.666667 42.666667 0 0 0 6.72-1.173333 6.986667 6.986667 0 0 0 4.213334-2.613334 7.946667 7.946667 0 0 0 1.6-4.906666 7.733333 7.733333 0 0 0-2.56-6.24 15.68 15.68 0 0 0-9.546667-2.186667z" p-id="8399"></path><path d="M625.226667 386.88V220.64a5.333333 5.333333 0 0 0-5.333334-5.333333h-506.133333a5.333333 5.333333 0 0 0-5.333333 5.333333v166.24a5.333333 5.333333 0 0 0 5.333333 5.333333h506.133333a5.333333 5.333333 0 0 0 5.333334-5.333333zM287.626667 268.266667l-16 73.333333a5.333333 5.333333 0 0 1-5.333334 4.16h-17.173333a5.333333 5.333333 0 0 1-5.333333-3.893333l-12.16-43.52a1.6 1.6 0 0 0-3.04 0l-11.84 43.52a5.333333 5.333333 0 0 1-5.333334 3.893333h-16.906666a5.333333 5.333333 0 0 1-5.333334-4.16L172.96 268.266667a5.333333 5.333333 0 0 1 5.333333-6.506667H192a5.333333 5.333333 0 0 1 5.333333 4.373333l6.773334 36a1.6 1.6 0 0 0 3.093333 0l10.08-36.533333a5.333333 5.333333 0 0 1 5.333333-3.946667h16.48a5.333333 5.333333 0 0 1 5.333334 3.946667l10.133333 36.48a1.6 1.6 0 0 0 3.093333 0l6.773334-35.946667a5.333333 5.333333 0 0 1 5.333333-4.373333H282.666667a5.333333 5.333333 0 0 1 4.96 6.506667z m86.666666 59.04a35.413333 35.413333 0 0 1-14.293333 14.666666 47.573333 47.573333 0 0 1-23.36 5.333334 53.333333 53.333333 0 0 1-23.52-4.533334 35.306667 35.306667 0 0 1-15.093333-14.293333A48 48 0 0 1 292.213333 304a45.706667 45.706667 0 0 1 75.733334-32 42.666667 42.666667 0 0 1 11.306666 31.52 51.733333 51.733333 0 0 1-4.96 23.786667z m93.173334 18.453333h-17.386667a5.333333 5.333333 0 0 1-4.693333-2.773333L433.013333 320a19.146667 19.146667 0 0 0-4.693333-6.453333 10.666667 10.666667 0 0 0-6.346667-1.973334h-2.293333v28.746667a5.333333 5.333333 0 0 1-5.333333 5.333333H398.933333a5.333333 5.333333 0 0 1-5.333333-5.333333V267.093333a5.333333 5.333333 0 0 1 5.333333-5.333333h37.92a62.613333 62.613333 0 0 1 18.4 2.08 19.786667 19.786667 0 0 1 10.24 7.68 22.88 22.88 0 0 1 3.84 13.6 23.626667 23.626667 0 0 1-2.986666 12.053333 22.88 22.88 0 0 1-8.213334 8.213334 30.346667 30.346667 0 0 1-9.066666 3.306666 27.84 27.84 0 0 1 6.72 3.093334 26.933333 26.933333 0 0 1 4.16 4.426666 30.4 30.4 0 0 1 3.626666 5.333334l8.586667 16.586666a5.333333 5.333333 0 0 1-4.693333 7.626667z m91.626666-21.333333a35.146667 35.146667 0 0 1-8.533333 12.426666A27.093333 27.093333 0 0 1 538.666667 343.466667a59.36 59.36 0 0 1-15.466667 2.293333h-33.013333a5.333333 5.333333 0 0 1-5.333334-5.333333V267.093333a5.333333 5.333333 0 0 1 5.333334-5.333333h33.226666a46.666667 46.666667 0 0 1 18.4 3.093333 30.186667 30.186667 0 0 1 11.626667 8.906667 36.053333 36.053333 0 0 1 6.56 13.44 62.08 62.08 0 0 1 2.08 16 56.96 56.96 0 0 1-2.986667 21.173333z" p-id="8400"></path><path d="M335.573333 280a16 16 0 0 0-12.64 5.333333 28.533333 28.533333 0 0 0-4.746666 18.666667 28.266667 28.266667 0 0 0 4.746666 18.293333 17.92 17.92 0 0 0 25.813334 0 31.146667 31.146667 0 0 0 4.586666-19.52 25.813333 25.813333 0 0 0-4.8-17.333333 16.266667 16.266667 0 0 0-12.96-5.44z" p-id="8401"></path><path d="M912 106.666667H201.173333a5.333333 5.333333 0 0 0-5.333333 5.333333v64a5.333333 5.333333 0 0 0 5.333333 5.333333H837.333333a5.333333 5.333333 0 0 1 5.333334 5.333334v510.666666a5.333333 5.333333 0 0 1-1.546667 3.733334l-88.213333 140.053333a5.333333 5.333333 0 0 1-3.733334 1.546667H275.84a5.333333 5.333333 0 0 1-5.333333-5.333334V434.08a5.333333 5.333333 0 0 0-5.333334-5.333333h-64a5.333333 5.333333 0 0 0-5.333333 5.333333V912a5.333333 5.333333 0 0 0 5.333333 5.333333h578.88a5.333333 5.333333 0 0 0 3.786667-1.546666l131.946667-183.786667a5.333333 5.333333 0 0 0 1.546666-3.786667V112a5.333333 5.333333 0 0 0-5.333333-5.333333z" p-id="8402"></path><path d="M575.52 512h-36.16a5.333333 5.333333 0 0 0-5.333333 3.946667c-16 64-43.413333 161.28-52.053334 202.986666l-42.666666-202.666666a5.333333 5.333333 0 0 0-5.333334-4.213334h-40a5.333333 5.333333 0 0 0-5.333333 6.613334l64 247.573333a5.333333 5.333333 0 0 0 5.333333 4h42.346667a5.333333 5.333333 0 0 0 5.333333-3.893333c14.933333-55.093333 40.586667-148.373333 49.226667-190.773334h0.373333c7.946667 38.826667 33.28 132.373333 48 190.666667a5.333333 5.333333 0 0 0 5.333334 4h42.666666a5.333333 5.333333 0 0 0 5.333334-3.893333l68.853333-247.573334a5.333333 5.333333 0 0 0-5.333333-6.72h-36.053334a5.333333 5.333333 0 0 0-5.333333 4c-11.52 46.72-39.146667 149.6-47.36 200.693334h-0.373333c-7.36-40.586667-35.36-147.04-49.76-200.8a5.333333 5.333333 0 0 0-5.706667-3.946667z" p-id="8403"></path></svg>',
        //   onClick: () => {
        //     exportDocument('word');
        //   },
        //   tip: '导出Word',
        // },
        // {
        //   icon: '<svg t="1748916378385" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="10277" width="256" height="256"><path d="M625.226667 390.293333V224a5.333333 5.333333 0 0 0-5.333334-5.333333h-506.133333a5.333333 5.333333 0 0 0-5.333333 5.333333v166.293333a5.333333 5.333333 0 0 0 5.333333 5.333334h506.133333a5.333333 5.333333 0 0 0 5.333334-5.333334z m-314.986667-79.466666a32.853333 32.853333 0 0 1-23.306667 7.146666h-14.24v25.866667a5.333333 5.333333 0 0 1-5.333333 5.333333h-15.413333a5.333333 5.333333 0 0 1-5.333334-5.333333V270.506667a5.333333 5.333333 0 0 1 5.333334-5.333334h37.813333a29.546667 29.546667 0 0 1 21.333333 6.72 25.013333 25.013333 0 0 1 7.04 19.04 25.813333 25.813333 0 0 1-7.893333 19.893334z m96 16.906666a34.56 34.56 0 0 1-8.48 12.48A28.213333 28.213333 0 0 1 385.866667 346.666667a62.08 62.08 0 0 1-15.466667 2.293333h-33.226667a5.333333 5.333333 0 0 1-5.333333-5.333333V270.506667a5.333333 5.333333 0 0 1 5.333333-5.333334h33.226667a45.92 45.92 0 0 1 18.4 3.093334 30.506667 30.506667 0 0 1 11.626667 9.066666 37.333333 37.333333 0 0 1 6.613333 13.493334 62.08 62.08 0 0 1 2.08 16 55.413333 55.413333 0 0 1-3.093333 20.906666z m80.8-49.866666a5.333333 5.333333 0 0 1-5.333333 5.333333h-27.466667a5.333333 5.333333 0 0 0-5.333333 5.333333v4a5.333333 5.333333 0 0 0 5.333333 5.333334h21.92a5.333333 5.333333 0 0 1 5.333333 5.333333V309.333333a5.333333 5.333333 0 0 1-5.333333 5.333334h-21.92a5.333333 5.333333 0 0 0-5.333333 5.333333v23.68a5.333333 5.333333 0 0 1-5.333334 5.333333h-15.36a5.333333 5.333333 0 0 1-5.333333-5.333333V270.506667a5.333333 5.333333 0 0 1 5.333333-5.333334h53.333334a5.333333 5.333333 0 0 1 5.333333 5.333334z" p-id="10278"></path><path d="M280.106667 282.24h-7.413334v18.72h6.346667a16.266667 16.266667 0 0 0 10.666667-2.613333 8.32 8.32 0 0 0 3.04-6.666667 9.226667 9.226667 0 0 0-2.613334-6.666667 13.546667 13.546667 0 0 0-10.026666-2.773333zM364.266667 284.213333h-6.506667v45.866667h6.4a26.666667 26.666667 0 0 0 11.573333-1.813333 12.266667 12.266667 0 0 0 5.333334-6.293334 39.946667 39.946667 0 0 0 1.92-14.56 28.213333 28.213333 0 0 0-4.32-18.293333 18.24 18.24 0 0 0-14.4-4.906667z" p-id="10279"></path><path d="M912 106.666667H201.173333a5.333333 5.333333 0 0 0-5.333333 5.333333v64a5.333333 5.333333 0 0 0 5.333333 5.333333H837.333333a5.333333 5.333333 0 0 1 5.333334 5.333334v510.666666a5.333333 5.333333 0 0 1-1.546667 3.733334l-88.213333 140.053333a5.333333 5.333333 0 0 1-3.733334 1.546667H275.84a5.333333 5.333333 0 0 1-5.333333-5.333334V434.08a5.333333 5.333333 0 0 0-5.333334-5.333333h-64a5.333333 5.333333 0 0 0-5.333333 5.333333V912a5.333333 5.333333 0 0 0 5.333333 5.333333h578.88a5.333333 5.333333 0 0 0 3.786667-1.546666l131.946667-183.786667a5.333333 5.333333 0 0 0 1.546666-3.786667V112a5.333333 5.333333 0 0 0-5.333333-5.333333z" p-id="10280"></path><path d="M378.666667 771.306667a28.693333 28.693333 0 0 0 17.28 27.146666 24.586667 24.586667 0 0 0 14.826666 4.32c24.693333 0 53.706667-27.786667 84-81.493333a665.066667 665.066667 0 0 1 117.333334-37.706667 118.56 118.56 0 0 0 72.906666 30.293334c16 0 49.386667 0 49.386667-35.84 0-12.373333-6.186667-33.973333-52.48-35.2a297.333333 297.333333 0 0 0-62.4 6.186666A283.2 283.2 0 0 1 558.453333 570.666667c17.28-55.573333 18.506667-92.053333 5.333334-110.56a32.426667 32.426667 0 0 0-25.92-12.96 30.826667 30.826667 0 0 0-30.293334 16c-18.506667 30.24 7.413333 88.32 19.146667 112A672 672 0 0 1 474.666667 697.173333c-94.133333 40.746667-96 65.493333-96 74.133334z m33.333333 2.453333l-3.093333 1.226667a89.333333 89.333333 0 0 1 35.2-29.013334 61.76 61.76 0 0 1-32.053334 27.786667z m268.693333-98.826667c19.146667 0 23.466667 4.32 22.24 7.413334a39.2 39.2 0 0 1-19.146666 1.866666 72.373333 72.373333 0 0 1-29.066667-7.413333 121.76 121.76 0 0 1 26.026667-1.866667zM532.48 481.013333a5.706667 5.706667 0 0 1 4.32-1.28h3.093333a75.413333 75.413333 0 0 1-1.226666 51.893334 75.68 75.68 0 0 1-6.186667-50.613334z m12.373333 142.026667l2.453334-0.586667a289.76 289.76 0 0 0 38.933333 47.52l-1.866667 1.28a588.16 588.16 0 0 0-66.72 19.146667l-1.813333-0.64c10.453333-21.6 20.373333-43.84 29.013333-66.72z" p-id="10281"></path></svg>',
        //   onClick: () => {
        //     exportDocument('pdf');
        //   },
        //   tip: '导出PDF',
        // },
      ],
      onCreated: (editor) => {
        updateOutLine(editor);
        countWords();
        draggableInit();
      },
      onChange: (editor) => {
        updateOutLine(editor);
        countWords();
      },
      onFullscreen: (bool) => {
        isFullscreen.value = bool;
        try {
          screenfull.toggle();
        } catch (error) {
          console.error(error);
        }
      },
    });

    const searchParams = new URLSearchParams(window.location.search);
    programmeId.value = searchParams.get('programmeId') || '';
    itemId.value = searchParams.get('itemId') || '';
    scene.value = searchParams.get('scene') || '';
    topic.value = searchParams.get('topic') || '';

    // 专业模式接收参数
    isSpecialty.value = searchParams.get('isSpecialty') === '2';
    const id = searchParams.get('id');
    if (!isSpecialty.value && !id) {
      aiEditor?.clear();
      localStorage.removeItem('ai-editor-content');
    }

    if (isSpecialty.value && !id && !hasGeneratedReport.value) {
      hasGeneratedReport.value = true; // 设置标志，防止重复生成
      generateReport(scene.value);
    }

    if (programmeId.value && itemId.value) {
      getReportDetail();
    }

    // 判断是否为选择模板参数，如果是则 赋值, 选择了模板参数，就不初始化消息
    const _commTemp = searchParams.get('commTemp')
    if (_commTemp) {
      let temp = tempList.find((o: any) => o.id == _commTemp) as any
      if (temp) {
        commTemp.value = temp.name;
      }
    } else {
      // 初始化消息
      initMessages();
    }
  });

  onUnmounted(() => {
    localStorage.removeItem('ai-editor-content');
    aiEditor && aiEditor.destroy();
  });

  const draggableInit = () => {
    const draggable = createDraggable('.floatBtn', {
      container: '.aie-container-panel',
      containerPadding: [12, 6, 12, 6], // top, right, bottom, left
      containerFriction: 1,
      releaseContainerFriction: 1,
      minVelocity: 0,
      onSettle() {
        // console.log('onSettle', draggable, draggable.x, draggable.y)
      }
    });
    draggable.x = draggable.$container.offsetWidth - 100;
    draggable.refresh();
  }

  // 初始化消息
  const initMessages = () => {
    // getRecommendQuestionsApi().then((res: any) => {
    //   const questions = res?.data?.data?.map((item: any) => {
    //     return {
    //       role: 'assistant',
    //       content: item.question,
    //       type: 'default',
    //       // isRecommend: true
    //     };
    //   }) || [];
    //   messages.value.push(...questions);
    // });
    messages.value = [];
  };

  // 字数统计
  const countWords = () => {
    const footerDom = document.querySelector(
      '.aie-container-footer'
    ) as HTMLElement;
    const spanElement = footerDom.querySelector('span');
    if (spanElement) {
      const textContent = spanElement.textContent;
      const numberPart = textContent?.match(/\d+/);
      if (numberPart) {
        const wordCount = parseInt(numberPart[0], 10);
        spanElement.textContent = `字数：${wordCount}`;
      } else {
        spanElement.textContent = `字数：0`;
      }
    }
  };

  // 更新目录
  const updateOutLine = (editor: AiEditor) => {
    const outlineContainer = document.querySelector('#outline');
    while (outlineContainer?.firstChild) {
      outlineContainer.removeChild(outlineContainer.firstChild);
    }

    const outlines = editor.getOutline();
    for (let outline of outlines) {
      const child = document.createElement('div');
      child.classList.add(`aie-title${outline.level}`);
      child.style.marginLeft = `${14 * (outline.level - 1)}px`;
      child.innerHTML = `<a href="#${outline.id}">${outline.text}</a>`;
      child.addEventListener('click', (e) => {
        e.preventDefault();
        const el = editor.innerEditor.view.dom.querySelector(
          `#${outline.id}`
        ) as HTMLElement;
        el.scrollIntoView({
          behavior: 'smooth',
          block: 'center',
          inline: 'nearest',
        });
        setTimeout(() => {
          editor.focusPos(outline.pos + outline.size - 1);
        }, 1000);
      });
      outlineContainer?.appendChild(child);
    }
  };

  // 切换标签
  const ontab = (type: string) => {
    tabType.value = type;
  };

  // 输入框自动增高
  const chatWordCount = ref(0)
  const adjustTextareaHeight = () => {
    const textarea = textareaRef.value as HTMLTextAreaElement;
    if (textarea) {
      textarea.style.height = 'auto';
      const newHeight = Math.min(textarea.scrollHeight, 200);
      textarea.style.height = `${newHeight}px`;
      textarea.style.overflow = newHeight >= 200 ? 'auto' : 'hidden';
      chatWordCount.value = textarea.value.length;
    }
  };

  // 发送消息
  const sendMessage = () => {
    const textarea = textareaRef.value as HTMLTextAreaElement;
    if (textarea && textarea.value.trim()) {
      const message = textarea.value.trim();
      // 添加用户消息
      messages.value.push({
        role: 'user',
        content: !commTemp.value ? message : `模板选择：${commTemp.value}<br/>输入需求：${message}`,
      });

      // 清空输入框
      textarea.value = '';
      textarea.style.height = 'auto';
      textarea.focus();

      // 滚动到底部
      scrollBottom();
      isLoading.value = true;
      abortController.value = new AbortController();
      replyMessage({ content: message }).finally(() => {
        isLoading.value = false;
        abortController.value = null;
      });
    }
  };

  // 发送消息
  const sendMessage1 = (message: any) => {
    if (message.type === 'default') {
      // 添加用户消息
      messages.value.push({
        role: 'user',
        content: message.content,
      });
      scrollBottom();
      isLoading.value = true;
      abortController.value = new AbortController();
      const content = message.content// + '，内容如下：' + aiEditor?.getText();
      replyMessage({ content }).finally(() => {
        isLoading.value = false;
        abortController.value = null;
      });
    }
  };

  /**
   * 调用模型回复消息
   * @param param 冗余参数
   */
  const replyMessage = async ({ content = '' }: { content?: string }) => {
    try {
      // 先创建一个loading的消息
      let loadingMessageIndex =
        messages.value.push({
          role: 'assistant',
          content:
            '<div class="loading-dots"><div class="dot"></div><div class="dot"></div><div class="dot"></div></div>',
          type: 'loading',
        }) - 1;

      scrollBottom();

      let response: any;
      if (1 || !commTemp.value) { // 均走直接对话
        response = await fetch(`${import.meta.env.VITE_API_PREFIX}/docWriter/generate/v1/item/text`, {
          // 如果是其他类型功能
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: localStorage.getItem('zion-auth-value'),
          },
          body: JSON.stringify({
            query: content,
            // inputs: {},
            response_mode: 'streaming',
            conversation_id: '',
            // files: [],
            scene: scene.value,
            topic: topic.value,
          }),
          signal: abortController.value?.signal
        });
      } else {
        response = await fetch(`${import.meta.env.VITE_API_PREFIX}/docWriter/generate/v1/item/template`, {
          // 如果是其他类型功能
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: localStorage.getItem('zion-auth-value'),
          },
          body: JSON.stringify({
            query: commTemp.value,
            inputs: {
              content: content
            },
            response_mode: 'streaming',
            conversation_id: '',
            files: [],
          }),
          signal: abortController.value?.signal
        });
      }

      if (response.status === 401) {
        message.warning('登录已过期，请重新登录');
        return handleLoseSession();
      }

      if (!response.ok) {
        throw new Error('网络响应异常');
      }

      const reader = response.body?.getReader();

      if (!reader) {
        throw new Error('无法获取流读取器');
      }

      const decoder = new TextDecoder('utf-8');

      // 移除loading消息，创建一个空的消息
      messages.value.splice(loadingMessageIndex, 1);
      messages.value.push({
        role: 'assistant',
        content: '',
        type: 'generate',
      })
      const messageIndex = messages.value.length - 1;

      let pendingContent = '';
      let done = false;

      // 直接使用 while 循环处理流，不要创建新的 ReadableStream
      while (!done) {
        try {
          const { value, done: streamDone } = await reader.read();
          done = streamDone;

          if (done) {
            break;
          }

          const chunk = decoder.decode(value, { stream: true });
          const lines = chunk.split('\n');

          for (const line of lines) {
            if (!line.trim()) continue; // 跳过空行

            const cleanLine = line.replace(/^data:\s*/, '');
            if (!cleanLine) continue;

            try {
              const parsedData = JSON.parse(cleanLine);
              console.log('解析行:', parsedData);
              const content = parsedData.answer || '';

              if (content) {
                pendingContent += content;
                // 直接更新消息内容
                messages.value[messageIndex].content = pendingContent;
                // 滚动到底部
                scrollBottom();
              }
            } catch (e) {
              // 解析单行失败，跳过该行
              console.error('解析行失败:', cleanLine, e);
            }
          }
        } catch (readError) {
          // 如果是中止错误，直接跳出循环
          if (readError.name === 'AbortError') {
            break;
          }
          console.error('读取流数据失败:', readError);
          throw readError;
        }
      }

    } catch (error) {
      // @ts-ignore
      if (error.name === 'AbortError') {
        // 移除loading消息
        const loadingIndex = messages.value.findIndex(
          (msg) => msg.type === 'loading'
        );
        if (loadingIndex !== -1) {
          messages.value.splice(loadingIndex, 1);
        }
        return;
      }
      console.error('请求失败:', error);
      // 移除loading消息
      const loadingIndex = messages.value.findIndex(
        (msg) => msg.type === 'loading'
      );
      if (loadingIndex !== -1) {
        messages.value.splice(loadingIndex, 1);
      }
      messages.value.push({
        role: 'assistant',
        content: '请求失败，请稍后再试',
        type: 'error',
      });
    } finally {
      isLoading.value = false;
      abortController.value = null;
      // 确保loading消息被移除
      const loadingIndex = messages.value.findIndex(
        (msg) => msg.type === 'loading'
      );
      if (loadingIndex !== -1) {
        messages.value.splice(loadingIndex, 1);
      }
    }
  };

  // 停止回复消息
  const stopGeneration = () => {
    // 简单模式
    if (abortController.value) {
      abortController.value.abort(); // 中止请求
      isLoading.value = false;
      abortController.value = null;
    }
    // 专业模式
    if (abortControllers.value.length) {
      isStopAllFetch.value = true;
      abortControllers.value.forEach(controller => controller.abort());
      specialGenerateLoading.value = false;
      abortControllers.value = []
    }
  }

  const docRef = ref();
  //专业版生成中loading
  const specialGenerateLoading = ref(false);
  // 专业版生成报告
  const generateReport = async (scene: string) => {
    aiEditor?.clear();
    let fullContent = '';
    const planDatas = JSON.parse(localStorage.getItem('planData') || '{}');
    const exploreDatas = JSON.parse(localStorage.getItem('exploreData') || '{}');
    let learnings = '';
    exploreDatas.forEach((i: any) => {
      learnings += i.content;
    });
    // 确保planDatas.chapters存在且是数组
    if (!planDatas.chapters || !Array.isArray(planDatas.chapters)) {
      console.error('章节数据不存在或格式错误');
      return;
    }
    let title = planDatas.title;
    specialGenerateLoading.value = true;
    isStopAllFetch.value = false;

    // 声明在循环外部，但在每次循环开始时重置
    let updateTimer: number | null = null;
    let longWaitTimer: number | null = null;
    let pendingContent = '';
    fullContent += `# ${title}\n\n`;
    //abortController.value = new AbortController();
    for (let i = 0; i < planDatas.chapters.length; i++) {
      if (isStopAllFetch.value) break;
      const controller = new AbortController();
      abortControllers.value.push(controller); // 保存 controller，稍后可以统一取消

      // 拼接md换行符
      if (i !== 0) {
        fullContent += '\n\n';
      }
      try {
        // 在处理新章节前，确保所有计时器被清除，pendingContent被重置
        if (updateTimer) {
          clearTimeout(updateTimer);
          updateTimer = null;
        }
        if (longWaitTimer) {
          clearTimeout(longWaitTimer);
          longWaitTimer = null;
        }
        pendingContent = '';

        // 发送请求并等待响应
        const res = await fetch(`${import.meta.env.VITE_API_PREFIX}/docWriter/generate/v1/item/chapter`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: localStorage.getItem('zion-auth-value'),
          },
          body: JSON.stringify({
            learnings: learnings,
            plan: JSON.stringify(planDatas),
            scene,
            chapter: i + 1,
            responseMode: 'streaming',
          }),
          signal: controller.signal, // 绑定 AbortController
        });

        if (res.status === 401) {
          message.warning('登录已过期，请重新登录');
          return handleLoseSession();
        }

        const reader = res.body?.getReader();
        const decoder = new TextDecoder('utf-8');

        // 处理流式响应
        let done = false;
        // 使用外部声明的变量，不再重新声明
        // updateTimer 和 pendingContent 已在外部声明
        // 上次更新的时间戳
        let lastUpdateTime = 0;
        // 节流间隔时间（毫秒）
        const throttleInterval = 150;

        // 使用节流函数减少更新频率，同时保护Markdown语法完整性
        const throttledUpdate = () => {
          const now = Date.now();
          // 如果距离上次更新的时间小于节流间隔，则设置定时器等待到达间隔时间
          if (now - lastUpdateTime < throttleInterval) {
            // 如果已经有定时器在等待，则不再设置新的定时器
            if (!updateTimer) {
              updateTimer = window.setTimeout(() => {
                updateTimer = null;
                lastUpdateTime = Date.now();
                performUpdate();
              }, throttleInterval - (now - lastUpdateTime));
            }
          } else {
            // 已经超过节流间隔，立即执行更新
            lastUpdateTime = now;
            performUpdate();
          }
        };

        // 执行实际的更新操作
        const performUpdate = () => {
          if (pendingContent) {
            // 检查pendingContent是否包含未闭合的Markdown语法标记
            const unclosedSyntax = checkUnclosedMarkdownSyntax(pendingContent);

            if (unclosedSyntax) {
              // 如果有未闭合的语法，不立即更新，等待更多内容
              // 但设置一个较长的超时，避免永远等待
              if (!longWaitTimer) {
                longWaitTimer = window.setTimeout(() => {
                  // 超时后强制更新，即使语法可能不完整
                  fullContent += pendingContent;
                  aiEditor?.clear();
                  aiEditor?.focus().insertMarkdown(removeThinkContent(fullContent));
                  docRef.value.scrollTop = docRef.value.scrollHeight;
                  pendingContent = '';
                  longWaitTimer = null;
                }, 100); // 100ms后强制更新
              }
            } else {
              // 没有未闭合的语法，可以安全更新
              if (longWaitTimer) {
                clearTimeout(longWaitTimer);
                longWaitTimer = null;
              }

              fullContent += pendingContent;
              // 只在积累了一定量的内容后才更新编辑器
              // 避免频繁的clear和insert操作
              aiEditor?.clear();
              aiEditor?.focus().insertMarkdown(removeThinkContent(fullContent));
              // 设置内容的时候将滚动条向下滚动
              docRef.value.scrollTop = docRef.value.scrollHeight;
              pendingContent = '';
            }
          }
        };

        // 检查是否有未闭合的Markdown语法
        const checkUnclosedMarkdownSyntax = (text: string) => {
          const openAsterisk = (text.match(/\*/g) || []).length % 2 !== 0;
          const openBacktick = (text.match(/`/g) || []).length % 2 !== 0;
          return openAsterisk || openBacktick;
        };

        // longWaitTimer 已在外部声明，不再重新声明

        while (!done) {
          const { value, done: streamDone } = await reader!.read();

          done = streamDone;
          if (done) {
            // 确保最后一次更新被执行
            if (pendingContent) {
              // 流结束时，即使有未闭合的语法也必须更新
              // 清除可能存在的长等待定时器
              if (longWaitTimer) {
                clearTimeout(longWaitTimer);
                longWaitTimer = null;
              }

              fullContent += pendingContent;
              aiEditor?.clear();
              aiEditor?.focus().insertMarkdown(removeThinkContent(fullContent));
              docRef.value.scrollTop = docRef.value.scrollHeight;
              pendingContent = ''; // 确保清空pendingContent，防止内容重复
            }
            break;
          }

          const chunk = decoder.decode(value, { stream: true });

          // 处理每个数据块
          const lines = chunk.split('\n');
          for (const line of lines) {
            const cleanLine = line.replace('data: ', '');
            try {
              const content = JSON.parse(cleanLine).answer || '';

              if (content) {
                // 累加到待处理内容
                pendingContent += content;
                throttledUpdate();
              }
            } catch (e) {
              // console.error('解析错误:', e);
            }
          }
        }

      } catch (error) {
        console.error(`生成第${i + 1}章失败:`, error);
      }
    }
    specialGenerateLoading.value = false;
  };

  let isUserScrolling = false;
  // 消息滚动到底部
  const scrollBottom = () => {
    setTimeout(() => {
      const chatContent = document.querySelector('.chat-content');
      if (chatContent) {
        // const threshold = 100; // 离底部 100px 内才自动滚动
        // const distanceToBottom = chatContent.scrollHeight - chatContent.scrollTop - chatContent.clientHeight;

        // if (distanceToBottom < threshold && !isUserScrolling) {
        //   chatContent.scrollTo({
        //     top: chatContent.scrollHeight,
        //     behavior: 'smooth',
        //   });
        // }
        chatContent.scrollTo({
          top: chatContent.scrollHeight,
          behavior: 'smooth',
        });
      }
    }, 100);
  };
  // 监听用户是否正在手动滚动
  // const chatContent = document.querySelector('.chat-content');
  // if (chatContent) {
  //   chatContent.addEventListener('scroll', () => {
  //     const distanceToBottom = chatContent.scrollHeight - chatContent.scrollTop - chatContent.clientHeight;
  //     // 当用户滚动离底部超过一定距离，说明是手动滚动
  //     isUserScrolling = distanceToBottom > 150;

  //     // 可选：如果用户回到底部，再允许自动滚动
  //     if (distanceToBottom < 50) {
  //       isUserScrolling = false;
  //     }
  //   });
  // }


  // 处理Enter键事件
  const handleEnterKey = (e: KeyboardEvent) => {
    if (e.shiftKey) {
      // 如果按住Shift+Enter，插入换行
      const textarea = textareaRef.value as HTMLTextAreaElement;
      const startPos = textarea.selectionStart;
      const endPos = textarea.selectionEnd;
      textarea.value =
        textarea.value.substring(0, startPos) +
        '\n' +
        textarea.value.substring(endPos);
      adjustTextareaHeight();
    } else {
      // 直接按Enter发送消息
      sendMessage();
    }
  };

  // 复制消息内容
  const copyMessage = (content: string) => {
    // 没有思考标签的内容
    const notThinkContent = removeThinkContent(content);
    // 创建一个临时div来提取纯文本内容
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = notThinkContent;
    const plainText = tempDiv.textContent || tempDiv.innerText || '';
    navigator.clipboard
      .writeText(plainText)
      .then(() => {
        message.success('复制成功');
      })
      .catch((err) => {
        console.error('复制失败:', err);
      });
  };

  // 应用消息
  const applyMessage = (content: string) => {
    const answerContent = removeThinkContent(content);
    aiEditor?.clear();
    aiEditor?.focus().insertMarkdown(answerContent);
  };

  // 追加消息
  const insertMessage = (content: string) => {
    const answerContent = removeThinkContent(content);
    aiEditor?.focus().insertMarkdown(answerContent);
  };

  // 新建对话
  const newChat = () => {
    initMessages();
    scrollBottom();
  };

  // 调用API优化提示词
  const optimizePromptAPI = async (prompt: string) => {
    try {
      const response = await fetch(`${import.meta.env.VITE_API_PREFIX}${llm_url}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: localStorage.getItem('zion-auth-value'),
        },
        body: JSON.stringify({
          model: llm_model,
          messages: [
            {
              role: 'system',
              content:
                '你是一个提示词优化助手，请将用户输入的提示词优化得更清晰、具体和有效，直接返回优化后的提示词内容，不要添加任何解释。',
            },
            {
              content: prompt,
              role: 'user',
            },
          ],
          stream: false,
        }),
      });

      if (response.status === 401) {
        message.warning('登录已过期，请重新登录');
        return handleLoseSession();
      }

      if (!response.ok) {
        throw new Error('网络响应异常');
      }

      const data = await response.json();
      return data.choices[0].message.content;
    } catch (error) {
      console.error('优化提示词失败:', error);
      return prompt; // 失败时返回原提示词
    }
  };

  // 在methods中添加清除输入的方法
  const clearInput = () => {
    const textarea = textareaRef.value as HTMLTextAreaElement;
    if (textarea) {
      textarea.value = '';
      textarea.style.height = 'auto';
      textarea.focus();
    }
  };

  // 获取编辑器的内容
  const getEditorContent = (type: string = 'text') => {
    if (type === 'text') {
      return aiEditor?.getText();
    }
    if (type === 'html') {
      return aiEditor?.getHtml();
    }
    if (type === 'markdown') {
      return aiEditor?.getMarkdown();
    }
  };

  const showSelectedActionPanel = ref(false);
  const currentActionType = ref('');
  const currentSelectContent = ref('');
  // 操作已选文字
  async function handleActionContent(editor: any, actionType: string) {
    currentSelectContent.value = editor?.getSelectedText() || '';
    showSelectedActionPanel.value = true;
    currentActionType.value = actionType;
    editor.blur();
  }

  // 关闭操作已选文字面板
  const closeSelectedActionPanel = () => {
    showSelectedActionPanel.value = false;
  };

  /** 输出面板相关方法 */
  const insertOutputContent = (content: string) => {
    const answerContent = removeThinkContent(content);
    aiEditor?.focus().insertMarkdown(answerContent);

  };

  // 替换内容
  const replaceContent = (content: string) => {
    const selection = aiEditor?.getSelectedText();
    if (!selection) {
      message.warning('请先选择要替换的内容');
      return;
    }
    aiEditor?.commandsChain().focus().deleteSelection().run();
    aiEditor?.focus().insertMarkdown(content);
  };

  // 处理纠错应用修改
  const handleApplyCorrection = (correction: { original: string; correction: string }) => {
    const editorContent = aiEditor?.getHtml() || '';
    if (editorContent.includes(correction.original)) {
      aiEditor?.clear();
      aiEditor?.focus().setMarkdownContent(editorContent.replace(correction.original, correction.correction));
      message.success('修改已应用');
      closeSelectedActionPanel();
    } else {
      message.warning('未找到需要修改的文本');
    }
  };

  const closeSaveReport = () => {
    showSaveReport.value = false;
  };

  function goHome() {
    localStorage.removeItem('ai-editor-content');
    const targetTitle = import.meta.env.VITE_BASIC_PAGE_TITLE;
    const targetUrl = `${window.location.origin}/project/detail/${programmeId.value}/check`;
    // 尝试获取已存在窗口
    const existingWindow = window.open('', targetTitle);
    if (existingWindow && !existingWindow.closed) {
      // 窗口存在时重定向
      existingWindow.location.href = targetUrl;
      // existingWindow.open(targetUrl, targetTitle);
      existingWindow.focus();
    } else {
      // 不存在时新建窗口
      window.open(targetUrl, targetTitle);
    }
  }

  // 导入文档 word
  const importDocument = () => {
    // 创建隐藏的文件输入元素
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.doc,.docx,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document';

    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (!file) return;

      try {
        const formData = new FormData();
        formData.append('file', file);

        const response = await fetch(`${import.meta.env.VITE_API_PREFIX}/docWriter/import/word`, {
          method: 'POST',
          body: formData
        });

        if (response.status === 401) {
          message.warning('登录已过期，请重新登录');
          return handleLoseSession();
        }

        if (!response.ok) {
          throw new Error('上传失败');
        }

        const result = await response.json();
        if (result && result.code == 200) {
          // 清空编辑器并插入解析后的内容
          aiEditor?.clear();
          setTimeout(() => {
            aiEditor?.setContent(parseHtml(result.data));
          }, 1000);
        }
      } catch (error) {
        console.error('导入Word文档失败:', error);
      }
    };

    // 触发文件选择对话框
    input.click();
  }


  // 导出文档 word, pdf
  const exportDocument = (type: string = 'word') => {
    const url = `${import.meta.env.VITE_API_PREFIX}/docWriter/export/to-${type}`;
    let fileName = `export.${type === 'pdf' ? 'pdf' : 'docx'}`;
    const docName = reportDetail.value.docName
    if (docName) {
      fileName = `${docName}.${type === 'pdf' ? 'pdf' : 'docx'}`;
    }

    let content = getEditorContent('html');
    // if(type == 'word'){
    //   content = getEditorContent('markdown')
    // }

    fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: localStorage.getItem('zion-auth-value'),
      },
      body: JSON.stringify({
        html: content,
      }),
    })
      .then((response) => {
        if (response.ok) {
          return response.blob();
        }
      })
      .then((blob) => {
        if (blob) {
          const link = document.createElement('a');
          link.style.display = 'none';
          link.href = URL.createObjectURL(blob);
          link.download = fileName;
          document.body.appendChild(link);
          link.click();
          // 释放的 URL 对象以及移除 a 标签
          URL.revokeObjectURL(link.href);
          document.body.removeChild(link);
        }
      });
  };

  function saveReport() {
    const content = getEditorContent('html');
    saveContent.value = content || '';
    showSaveReport.value = true;
  }

  // 获取详情
  function getReportDetail() {
    queryDetailLoading.value = true;
    getReportDetailApi({
      programmeId: programmeId.value,
      itemId: itemId.value
    }).then((res: any) => {
      if (res.data.success === true) {
        aiEditor?.setContent(res.data?.data?.content || '');
        // 这里处理滚动条初始在顶部
        aiEditor.focusStart();
        aiEditor.commandsChain().scrollIntoView()
        aiEditor.blur()
        reportDetail.value.docName = res.data?.data?.docName || '';
      }
      queryDetailLoading.value = false;
    }).catch((err: any) => {
      console.error('获取报告详情失败:', err);
      queryDetailLoading.value = false;
    });
  }

  // 更新成功
  const updateSeccess = (id: string | number) => {
    // getReportDetail();
  }

  /** 常用模板相关变量 */
  // 是否打开模板
  const showCommTemplate = ref(false);
  const commTemp = ref('')
  // 打开模板弹窗
  const openCommTemplate = () => {
    showCommTemplate.value = true
    //commTemp.value = '';
  }
  // 关闭模板回调
  const closeCommTemplate = () => {

  }
  // 成功后模板回调
  const successCommTemplate = (form: any) => {
    commTemp.value = form.tempContent || ''
  }

  // 删除commTemp的值
  const commTempClean = () => {
    commTemp.value = ''
  }

  defineExpose({
    getEditorContent,
  });
</script>

<style lang="less">

  html,
  body {
    overflow: hidden;
    height: 100vh;
    margin: 0;
    padding: 0;
  }

  .page-header {
    background-color: #fff;
    height: 35px;
    line-height: 35px;
    padding: 0 2rem;
    display: flex;
    position: sticky;
    border-bottom: 1px solid #efefef;
    top: 0;
    z-index: 1;
  }

  .logo a {
    font-size: 20px;
    font-weight: 500;
    color: #000000;
    text-decoration: none;
  }

  .aie-container {
    border: none !important;
    width: 100vw !important;
    height: 100vh !important;
    background: #f7f9ff;
  }

  .aie-header-panel {
    // position: sticky;
    // z-index: 1;
  }

  .aie-header-panel aie-header>div {
    align-items: center;
    justify-content: flex-start;
    padding-left: 14px;
    padding-right: 360px;
    text-align: left;
  }

  .aie-container-header {
    position: relative;
  }

  .action-btn-box {
    position: absolute;
    right: 10px;
    top: 6px;
  }

  .aie-main {
    flex: 1;
    position: relative;
    display: flex;
    justify-content: space-between;
    padding: 15px;
    min-height: 0;
  }

  .aie-main>div {
    height: 100%;
  }

  .floatBtn {
    display: inline-block;

    &.floatBtn-active {
      img {
        width: 38px;
        height: 38px;
        padding: 2px;
        opacity: 1;
        border: dashed 1px var(--aie-content-link-a-color);
      }
    }

    img {
      width: 42px;
      height: 42px;
      padding: 0;
      border-radius: 50%;
      opacity: 0.86;
      transition: all 0.3s ease-in-out;
      animation: realistic-bounce 4.8s infinite ease-in-out;

      &:hover {
        opacity: 1;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.6);
      }

      &:active {
        opacity: 0.76;
      }
    }
  }

  .aie-container-panel {
    flex: 1;
    height: 100%;
    padding: 0 24px;

    &.aie-container-panel-full {
      max-width: initial;
    }
  }

  .aie-container-panel-box {
    overflow-y: auto;
    overflow-x: hidden;
    height: 100%;
    max-width: 1098px;
    margin: 0 auto;

    /* 为所有文本元素设置对比色指针 */
    [class*="text-"],
    p,
    span,
    a,
    h1,
    h2,
    h3,
    h4,
    h5,
    h6,
    [contenteditable="true"] {
      cursor: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="32" height="20" viewBox="0 0 32 32"><path fill="none" stroke="black" stroke-width="1" d="M16 2 L16 30 M12 2 L20 2 M12 30 L20 30"/></svg>') 16 16, text;
    }
  }

  .aie-container-main {
    margin: 0 auto;
    /* max-width:1000px; */
    padding: 15px;
    width: 100%;
    min-height: calc(100%);
    border: 1px solid rgb(229 231 235);
    background-color: #fff;
    box-sizing: border-box;
    border-radius: var(--ant-borderRadius);
  }

  .aie-ai-content {
    position: relative;
    transition: 0.3s all ease-in-out;
    width: 22vw;
    min-width: 330px;
    max-width: 480px;
    opacity: 1;

    &.aie-ai-content-close {
      width: 0;
      min-width: 0;
      opacity: 0;
    }
  }

  .aie-menu-content {
    position: relative;
    width: 18vw;
    transition: 0.3s all ease-in-out;
    max-width: 330px;
  }

  .aie-directory {
    position: absolute;
    /* top: 10px;
	left: 10px;
	width: 200px; */
    width: 100%;
    height: 100%;
    /* margin: 15px 0 0 12px; */
    overflow-y: auto;
    border: 1px solid rgb(229 231 235);
    box-sizing: border-box;
    background: #fff;
    padding: 10px;
    border-radius: var(--ant-borderRadius);
  }

  .aie-directory h5 {
    color: #000000c4;
    font-size: 16px;
    text-indent: 4px;
    line-height: 32px;
    border-bottom: 1px solid rgb(229 231 235);
    margin: 0 0 10px;
    padding: 5px 0 10px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .aie-directory a {
    height: 30px;
    font-size: 14px;
    color: #000000a3;
    text-indent: 4px;
    line-height: 30px;
    text-decoration: none;
    width: 100%;
    display: inline-block;
    margin: 0;
    padding: 0;
    white-space: nowrap;
    overflow: hidden;
    -o-text-overflow: ellipsis;
    text-overflow: ellipsis;
  }

  .aie-directory a:hover {
    cursor: pointer;
    background-color: #334d660f;
    border-radius: 4px;
  }

  .aie-title1 {
    font-size: 14px;
    font-weight: 500;
  }

  .aie-title2,
  .aie-title3,
  .aie-title4,
  .aie-title5,
  .aie-title6 {
    font-size: 12px;
  }

  /* @media screen and (max-width: 1280px) {
	.aie-directory {
		display: none;
	}

	.aie-ai-chat {
		display: none;
	}

} */

  /* @media screen and (max-width: 1400px) {
	.aie-directory {
		width: 200px;
	}

	.aie-ai-chat {
		width: 400px;
	}
} */

  .aie-container-footer {
    background-color: #fff;
    width: 100%;
    /* position: fixed; */
    bottom: 0;
  }

  .aie-container-footer aie-footer>div {
    height: 36px;
    line-height: 36px;
    font-size: 12px;
  }

  .aie-container aie-footer>div span {
    margin-left: 20px;
  }

  .aie-ai-chat {
    height: 100%;
    width: 100%;
    background: #ecf3fe;
    border: 1px solid rgb(229 231 235);
    border-radius: var(--ant-borderRadius);
    box-sizing: border-box;
  }

  .chat-tabar {
    display: flex;
    height: 60px;
    padding: 0 10px;
    align-items: center;
    border-bottom: 1px solid rgb(229 231 235);
  }

  .chat-tabar .tab {
    position: relative;
    display: flex;
    align-items: center;
    height: 100%;
    margin: 0 5px;
    padding: 0px 5px;
    color: rgba(0, 0, 0, 0.64);
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .chat-tabar .tab.active {
    color: #000000;
    font-weight: 500;
  }

  .chat-tabar .tab.active::before {
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    /* border-bottom: 2px solid #000000; */
  }

  .chat-tabar .tab .icon-tab {
    width: 16px;
    height: 16px;
    display: inline-block;
    margin-right: 4px;
    vertical-align: middle;
    background: url(./assets/img/icon-ai.png) no-repeat center;
  }

  .tab-op {
    margin-left: auto;
    /* 新增：将操作按钮推到右侧 */
    display: flex;
    gap: 10px;
  }

  .tab-op .btn {
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    cursor: pointer;
    position: relative;
  }

  .tab-op .btn:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }

  .tab-op .btn:hover::after {
    content: attr(title);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 10;
    margin-top: 5px;
  }

  .chat-main {
    height: calc(100% - 61px);
  }

  .chat-main .panel {
    height: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .chat-input-contailer {
    padding: 0 10px;
    padding-bottom: 10px;
    transform: translateY(7px);
  }

  .chat-input {
    // height: auto;
    padding: 12px;
    background-color: #fff;
    border-bottom-right-radius: 8px;
    border-bottom-left-radius: 8px;
    box-sizing: border-box;
    border-radius: var(--ant-borderRadius);
  }

  /* .chat-input .input {
	position: relative;
} */

  .chat-input .input-text {
    width: calc(100% - 1px);
    border: 1px solid rgb(229 231 235);
    border-radius: 4px;
    padding: 7px 12px;
    padding-right: 7px;
    line-height: 22px;
    resize: none;
    overflow-y: auto;
    outline: none;
    box-sizing: border-box;
    transition: height 0.2s ease;
    min-height: 120px;
  }

  .chat-input .input-text:focus {
    border: 1px solid rgb(229 231 235);
    box-shadow: none;
  }

  .btn-box {
    height: 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 3px 12px;
    transform: translateY(-10px);
    background: #fff;
    border-radius: var(--ant-borderRadius);
    width: calc(100% - 24px);
  }

  .btn-group {
    display: flex;
    gap: 15px;
  }

  .btn-box .btn {
    padding: 3px 6px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s ease;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .send-btn {
    width: 16px;
    height: 15px;
    background: url(./assets/img/icon-send.png) no-repeat center;
    cursor: pointer;
    opacity: 0.6;
    transition: all 0.3s;

    &.send-btn-count {
      opacity: 0.9;

      &:hover {
        opacity: 1;
        transform: scale(1.1);
      }
    }
  }

  .stop-btn {
    width: 16px;
    height: 16px;
    background: url(./assets/img/icon-stop.png) no-repeat center / contain;
    cursor: pointer;
  }

  .stop-btn:hover {
    opacity: 0.8;
  }

  .optimize-btn {
    font-size: 14px;
    color: #12d0b0;
    cursor: pointer;
  }

  .optimize-btn::before {
    content: '';
    display: inline-block;
    width: 16px;
    height: 16px;
    background: url(./assets/img/icon-yh.png) no-repeat center;
    vertical-align: middle;
    margin: -3px 2px 0 0;
  }

  .optimize-btn:hover {
    opacity: 0.8;
  }

  .clear-btn {
    font-size: 12px;
    color: #b4bfd0;
    cursor: pointer;

    &.clear-btn-count {
      color: var(--ant-colorText);

      &:hover {
        color: var(--ant-colorPrimary);
      }
    }
  }

  .clear-btn:hover {
    opacity: 0.8;
  }

  .chat-content {
    height: calc(100% - 180px);
    overflow-x: hidden;
    overflow-y: auto;
    padding: 10px;
    box-sizing: border-box;
    flex: 1;
  }

  .chat-content.tag {
    height: calc(100% - 205px);
  }

  .message {
    display: flex;
    flex-direction: column;
    margin-bottom: 8px;
  }

  .message.hide {
    display: none;
  }

  .message-bubble {
    max-width: 80%;
    padding: 10px 16px;
    border-radius: 8px;
    word-wrap: break-word;
    font-size: 14px;
  }

  .default-message {
    background-color: #fff !important;
    color: #0265ff !important;
  }

  .message.user .message-bubble {
    background-color: #e6f7ff;
    color: #333;
    align-self: flex-end;
  }

  .message.assistant .message-bubble {
    background-color: #f0f0f0;
    color: #333;
    align-self: flex-start;
  }

  .message-content p {
    margin: 0;
    line-height: 22px;
  }

  .message-actions {
    margin-top: 4px;
    display: flex;
    gap: 6px;
  }

  .message-actions.user {
    align-self: flex-end;
  }

  .action-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 24px;
    border-radius: 2px;
    cursor: pointer;
    box-sizing: border-box;
    color: var(--text-medium, rgba(0, 0, 0, 0.48));
    font-family: 'PingFang SC';
    font-size: 10px;
    font-style: normal;
    font-weight: 400;
    line-height: 13px;
    position: relative;
  }

  .action-btn:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }

  .action-btn:hover::after {
    content: attr(title);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 10;
    margin-bottom: 5px;
  }

  .toast-container {
    position: fixed;
    top: 150px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;
  }

  .toast-message {
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 14px;
    animation: fadeInOut 2s ease-in-out;
  }

  @keyframes fadeInOut {
    0% {
      opacity: 0;
    }

    20% {
      opacity: 1;
    }

    80% {
      opacity: 1;
    }

    100% {
      opacity: 0;
    }
  }

  /** loading 样式 */
  .loading-dots {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 20px;
  }

  .loading-dots .dot {
    width: 8px;
    height: 8px;
    margin: 0 4px;
    background-color: #888;
    border-radius: 50%;
    animation: bounce 1.4s infinite ease-in-out both;
  }

  .loading-dots .dot:nth-child(1) {
    animation-delay: -0.32s;
  }

  .loading-dots .dot:nth-child(2) {
    animation-delay: -0.16s;
  }

  @keyframes bounce {

    0%,
    80%,
    100% {
      transform: scale(0);
    }

    40% {
      transform: scale(1);
    }
  }

  .rbtn-wrap {
    margin-top: 20px;
    margin-left: 20px;
    display: flex;
    gap: 10px;
  }

  .rbtn-wrap .grey-btn {
    width: 68px;
    height: 26px;
    line-height: 26px;
    font-size: 14px;
    color: #8c9db9;
    border-radius: 3px;
    background: #dce5f3;
    text-align: center;
  }

  .chat-tip-wrap {
    background: #fff;
    font-size: 14px;
    padding: 10px 10px 0;
    color: #afafaf;
  }


  .loading-box {
    display: flex;
    flex-direction: column;
  }

  .loading-box .loading-tips {
    color: #0265ff;
  }

  .loading-box .loading-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 94px;
    height: 26px;
    background: #297CFC;
    color: #fff;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
  }

  .loading-box .loading-btn::before {
    content: '';
    display: inline-block;
    vertical-align: middle;
    width: 13px;
    height: 13px;
    margin: 2px 5px 0 0;
    background: url(./assets/img/icon-stop-2.png) no-repeat center / contain;
  }

  .loading-box .loading-btn:hover {
    opacity: .7;
  }

  @media print {
    .page-break {
      page-break-before: always;
      break-before: page;
    }

    .aie-container,
    .aie-main,
    .aie-container-panel,
    .aie-container-panel-box,
    .aie-container-main {
      background: #fff !important;
      box-shadow: none !important;
      border: none !important;
    }

    .aie-ai-content,
    .aie-menu-content,
    .aie-header-panel,
    .aie-container-header,
    .aie-container-footer,
    .chat-tabar,
    .chat-main,
    .chat-input-contailer {
      display: none !important;
    }
  }

  @keyframes realistic-bounce {
    0% {
      transform: translateY(0);
      opacity: 1;
      animation-timing-function: cubic-bezier(0.33, 0, 0.67, 1);
    }

    50% {
      transform: translateY(14px);
      opacity: .76;
      animation-timing-function: cubic-bezier(0.33, 0, 0.2, 1);
    }

    100% {
      transform: translateY(0);
      opacity: 1;
      animation-timing-function: cubic-bezier(0.33, 0, 0.67, 1);
    }
  }
</style>
