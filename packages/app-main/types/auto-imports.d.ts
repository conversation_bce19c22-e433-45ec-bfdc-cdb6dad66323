/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// noinspection JSUnusedGlobalSymbols
// Generated by unplugin-auto-import
// biome-ignore lint: disable
export {}
declare global {
  const EffectScope: typeof import('vue')['EffectScope']
  const acceptHMRUpdate: typeof import('pinia')['acceptHMRUpdate']
  const computed: typeof import('vue')['computed']
  const createApp: typeof import('vue')['createApp']
  const createPinia: typeof import('pinia')['createPinia']
  const customRef: typeof import('vue')['customRef']
  const defaultPageSizeOptions: typeof import('../src/hooks/useLoad')['defaultPageSizeOptions']
  const defaultPaginationOptions: typeof import('../src/hooks/usePagination')['defaultPaginationOptions']
  const defaultStep3Form: typeof import('../src/stores/startDevForm')['defaultStep3Form']
  const defineAsyncComponent: typeof import('vue')['defineAsyncComponent']
  const defineComponent: typeof import('vue')['defineComponent']
  const defineStore: typeof import('pinia')['defineStore']
  const download: typeof import('../src/hooks/useDownload')['download']
  const downloadFile: typeof import('../src/hooks/useDownload')['downloadFile']
  const downloadFileFromStream: typeof import('../src/hooks/useDownload')['downloadFileFromStream']
  const effectScope: typeof import('vue')['effectScope']
  const flatListKey: typeof import('../src/stores/menuList')['flatListKey']
  const getActivePinia: typeof import('pinia')['getActivePinia']
  const getCurrentInstance: typeof import('vue')['getCurrentInstance']
  const getCurrentScope: typeof import('vue')['getCurrentScope']
  const handleExceptDown: typeof import('../src/hooks/useDownload')['handleExceptDown']
  const inject: typeof import('vue')['inject']
  const isProxy: typeof import('vue')['isProxy']
  const isReactive: typeof import('vue')['isReactive']
  const isReadonly: typeof import('vue')['isReadonly']
  const isRef: typeof import('vue')['isRef']
  const mapActions: typeof import('pinia')['mapActions']
  const mapGetters: typeof import('pinia')['mapGetters']
  const mapState: typeof import('pinia')['mapState']
  const mapStores: typeof import('pinia')['mapStores']
  const mapWritableState: typeof import('pinia')['mapWritableState']
  const markRaw: typeof import('vue')['markRaw']
  const nextTick: typeof import('vue')['nextTick']
  const onActivated: typeof import('vue')['onActivated']
  const onBeforeMount: typeof import('vue')['onBeforeMount']
  const onBeforeRouteLeave: typeof import('vue-router')['onBeforeRouteLeave']
  const onBeforeRouteUpdate: typeof import('vue-router')['onBeforeRouteUpdate']
  const onBeforeUnmount: typeof import('vue')['onBeforeUnmount']
  const onBeforeUpdate: typeof import('vue')['onBeforeUpdate']
  const onDeactivated: typeof import('vue')['onDeactivated']
  const onErrorCaptured: typeof import('vue')['onErrorCaptured']
  const onMounted: typeof import('vue')['onMounted']
  const onRenderTracked: typeof import('vue')['onRenderTracked']
  const onRenderTriggered: typeof import('vue')['onRenderTriggered']
  const onScopeDispose: typeof import('vue')['onScopeDispose']
  const onServerPrefetch: typeof import('vue')['onServerPrefetch']
  const onUnmounted: typeof import('vue')['onUnmounted']
  const onUpdated: typeof import('vue')['onUpdated']
  const onWatcherCleanup: typeof import('vue')['onWatcherCleanup']
  const paginationConfig: typeof import('../src/hooks/usePagination')['paginationConfig']
  const provide: typeof import('vue')['provide']
  const reactive: typeof import('vue')['reactive']
  const readonly: typeof import('vue')['readonly']
  const ref: typeof import('vue')['ref']
  const resolveComponent: typeof import('vue')['resolveComponent']
  const setActivePinia: typeof import('pinia')['setActivePinia']
  const setMapStoreSuffix: typeof import('pinia')['setMapStoreSuffix']
  const shallowReactive: typeof import('vue')['shallowReactive']
  const shallowReadonly: typeof import('vue')['shallowReadonly']
  const shallowRef: typeof import('vue')['shallowRef']
  const storeKey: typeof import('../src/stores/menuList')['storeKey']
  const storeToRefs: typeof import('pinia')['storeToRefs']
  const stores: typeof import('../src/stores/index')['default']
  const toRaw: typeof import('vue')['toRaw']
  const toRef: typeof import('vue')['toRef']
  const toRefs: typeof import('vue')['toRefs']
  const toValue: typeof import('vue')['toValue']
  const triggerRef: typeof import('vue')['triggerRef']
  const unref: typeof import('vue')['unref']
  const useAntProgressAnimate: typeof import('../src/hooks/useAntProgressAnimate')['useAntProgressAnimate']
  const useAntTableSortable: typeof import('../src/hooks/useAntTableSortable')['useAntTableSortable']
  const useAppidAndTenantid: typeof import('../src/hooks/useAppidAndTenantid')['default']
  const useAttrs: typeof import('vue')['useAttrs']
  const useBreakpoint: typeof import('../src/hooks/useBreakpoint')['useBreakpoint']
  const useCommonParams: typeof import('../src/hooks/useCommonParams')['useCommonParams']
  const useCssModule: typeof import('vue')['useCssModule']
  const useCssVars: typeof import('vue')['useCssVars']
  const useCurrentDark: typeof import('../src/stores/currentDark')['useCurrentDark']
  const useCurrentRouteTab: typeof import('../src/stores/currentRouteTab')['useCurrentRouteTab']
  const useCurrentTenant: typeof import('../src/stores/currentUser')['useCurrentTenant']
  const useCurrentUser: typeof import('../src/stores/currentUser')['useCurrentUser']
  const useCustomUpload: typeof import('../src/hooks/useCustomUpload')['useCustomUpload']
  const useDialog: typeof import('../src/hooks/useDialog')['default']
  const useFullscreen: typeof import('../src/hooks/useFullscreen')['useFullscreen']
  const useGlobal: typeof import('../src/stores/global')['useGlobal']
  const useId: typeof import('vue')['useId']
  const useLink: typeof import('vue-router')['useLink']
  const useLoadMore: typeof import('../src/hooks/useLoad')['useLoadMore']
  const useMenuList: typeof import('../src/stores/menuList')['useMenuList']
  const useModel: typeof import('vue')['useModel']
  const usePagination: typeof import('../src/hooks/usePagination')['default']
  const useQuickRoute: typeof import('../src/hooks/useQuickRoute')['useQuickRoute']
  const useResizable: typeof import('../src/hooks/useResizable')['useResizable']
  const useRoute: typeof import('vue-router')['useRoute']
  const useRouter: typeof import('vue-router')['useRouter']
  const useSlots: typeof import('vue')['useSlots']
  const useStartDevFormStore: typeof import('../src/stores/startDevForm')['useStartDevFormStore']
  const useTable: typeof import('../src/hooks/useLoad')['useTable']
  const useTableScrollH: typeof import('../src/hooks/useTableScrollH')['default']
  const useTemplateRef: typeof import('vue')['useTemplateRef']
  const useUserAppTenant: typeof import('../src/stores/currentUser')['useUserAppTenant']
  const useUserPermission: typeof import('../src/hooks/useUserPermissions')['useUserPermission']
  const useUserPermissionSuperAdmin: typeof import('../src/hooks/useUserPermissions')['useUserPermissionSuperAdmin']
  const useUserPermissionsData: typeof import('../src/hooks/useUserPermissions')['useUserPermissionsData']
  const useUserPermissionsEvery: typeof import('../src/hooks/useUserPermissions')['useUserPermissionsEvery']
  const useUserPermissionsSome: typeof import('../src/hooks/useUserPermissions')['useUserPermissionsSome']
  const useVxeTable: typeof import('../src/hooks/useVxeLoad')['useVxeTable']
  const watch: typeof import('vue')['watch']
  const watchEffect: typeof import('vue')['watchEffect']
  const watchPostEffect: typeof import('vue')['watchPostEffect']
  const watchSyncEffect: typeof import('vue')['watchSyncEffect']
}
// for type re-export
declare global {
  // @ts-ignore
  export type { Component, Slot, Slots, ComponentPublicInstance, ComputedRef, DirectiveBinding, ExtractDefaultPropTypes, ExtractPropTypes, ExtractPublicPropTypes, InjectionKey, PropType, Ref, MaybeRef, MaybeRefOrGetter, VNode, WritableComputedRef } from 'vue'
  import('vue')
  // @ts-ignore
  export type { IAppInfo } from '../src/hooks/useAppidAndTenantid'
  import('../src/hooks/useAppidAndTenantid')
  // @ts-ignore
  export type { Breakpoint, BreakpointResult } from '../src/hooks/useBreakpoint'
  import('../src/hooks/useBreakpoint')
  // @ts-ignore
  export type { PaginationOptions, PaginationResult } from '../src/hooks/usePagination'
  import('../src/hooks/usePagination')
  // @ts-ignore
  export type { Step1Form, Step2Form, Step3Form } from '../src/stores/startDevForm'
  import('../src/stores/startDevForm')
}

// for vue template auto import
import { UnwrapRef } from 'vue'
declare module 'vue' {
  interface GlobalComponents {}
  interface ComponentCustomProperties {
    readonly EffectScope: UnwrapRef<typeof import('vue')['EffectScope']>
    readonly acceptHMRUpdate: UnwrapRef<typeof import('pinia')['acceptHMRUpdate']>
    readonly computed: UnwrapRef<typeof import('vue')['computed']>
    readonly createApp: UnwrapRef<typeof import('vue')['createApp']>
    readonly createPinia: UnwrapRef<typeof import('pinia')['createPinia']>
    readonly customRef: UnwrapRef<typeof import('vue')['customRef']>
    readonly defaultPaginationOptions: UnwrapRef<typeof import('../src/hooks/usePagination')['defaultPaginationOptions']>
    readonly defaultStep3Form: UnwrapRef<typeof import('../src/stores/startDevForm')['defaultStep3Form']>
    readonly defineAsyncComponent: UnwrapRef<typeof import('vue')['defineAsyncComponent']>
    readonly defineComponent: UnwrapRef<typeof import('vue')['defineComponent']>
    readonly defineStore: UnwrapRef<typeof import('pinia')['defineStore']>
    readonly download: UnwrapRef<typeof import('../src/hooks/useDownload')['download']>
    readonly downloadFile: UnwrapRef<typeof import('../src/hooks/useDownload')['downloadFile']>
    readonly downloadFileFromStream: UnwrapRef<typeof import('../src/hooks/useDownload')['downloadFileFromStream']>
    readonly effectScope: UnwrapRef<typeof import('vue')['effectScope']>
    readonly flatListKey: UnwrapRef<typeof import('../src/stores/menuList')['flatListKey']>
    readonly getActivePinia: UnwrapRef<typeof import('pinia')['getActivePinia']>
    readonly getCurrentInstance: UnwrapRef<typeof import('vue')['getCurrentInstance']>
    readonly getCurrentScope: UnwrapRef<typeof import('vue')['getCurrentScope']>
    readonly handleExceptDown: UnwrapRef<typeof import('../src/hooks/useDownload')['handleExceptDown']>
    readonly inject: UnwrapRef<typeof import('vue')['inject']>
    readonly isProxy: UnwrapRef<typeof import('vue')['isProxy']>
    readonly isReactive: UnwrapRef<typeof import('vue')['isReactive']>
    readonly isReadonly: UnwrapRef<typeof import('vue')['isReadonly']>
    readonly isRef: UnwrapRef<typeof import('vue')['isRef']>
    readonly mapActions: UnwrapRef<typeof import('pinia')['mapActions']>
    readonly mapGetters: UnwrapRef<typeof import('pinia')['mapGetters']>
    readonly mapState: UnwrapRef<typeof import('pinia')['mapState']>
    readonly mapStores: UnwrapRef<typeof import('pinia')['mapStores']>
    readonly mapWritableState: UnwrapRef<typeof import('pinia')['mapWritableState']>
    readonly markRaw: UnwrapRef<typeof import('vue')['markRaw']>
    readonly nextTick: UnwrapRef<typeof import('vue')['nextTick']>
    readonly onActivated: UnwrapRef<typeof import('vue')['onActivated']>
    readonly onBeforeMount: UnwrapRef<typeof import('vue')['onBeforeMount']>
    readonly onBeforeRouteLeave: UnwrapRef<typeof import('vue-router')['onBeforeRouteLeave']>
    readonly onBeforeRouteUpdate: UnwrapRef<typeof import('vue-router')['onBeforeRouteUpdate']>
    readonly onBeforeUnmount: UnwrapRef<typeof import('vue')['onBeforeUnmount']>
    readonly onBeforeUpdate: UnwrapRef<typeof import('vue')['onBeforeUpdate']>
    readonly onDeactivated: UnwrapRef<typeof import('vue')['onDeactivated']>
    readonly onErrorCaptured: UnwrapRef<typeof import('vue')['onErrorCaptured']>
    readonly onMounted: UnwrapRef<typeof import('vue')['onMounted']>
    readonly onRenderTracked: UnwrapRef<typeof import('vue')['onRenderTracked']>
    readonly onRenderTriggered: UnwrapRef<typeof import('vue')['onRenderTriggered']>
    readonly onScopeDispose: UnwrapRef<typeof import('vue')['onScopeDispose']>
    readonly onServerPrefetch: UnwrapRef<typeof import('vue')['onServerPrefetch']>
    readonly onUnmounted: UnwrapRef<typeof import('vue')['onUnmounted']>
    readonly onUpdated: UnwrapRef<typeof import('vue')['onUpdated']>
    readonly onWatcherCleanup: UnwrapRef<typeof import('vue')['onWatcherCleanup']>
    readonly paginationConfig: UnwrapRef<typeof import('../src/hooks/usePagination')['paginationConfig']>
    readonly provide: UnwrapRef<typeof import('vue')['provide']>
    readonly reactive: UnwrapRef<typeof import('vue')['reactive']>
    readonly readonly: UnwrapRef<typeof import('vue')['readonly']>
    readonly ref: UnwrapRef<typeof import('vue')['ref']>
    readonly resolveComponent: UnwrapRef<typeof import('vue')['resolveComponent']>
    readonly setActivePinia: UnwrapRef<typeof import('pinia')['setActivePinia']>
    readonly setMapStoreSuffix: UnwrapRef<typeof import('pinia')['setMapStoreSuffix']>
    readonly shallowReactive: UnwrapRef<typeof import('vue')['shallowReactive']>
    readonly shallowReadonly: UnwrapRef<typeof import('vue')['shallowReadonly']>
    readonly shallowRef: UnwrapRef<typeof import('vue')['shallowRef']>
    readonly storeKey: UnwrapRef<typeof import('../src/stores/menuList')['storeKey']>
    readonly storeToRefs: UnwrapRef<typeof import('pinia')['storeToRefs']>
    readonly toRaw: UnwrapRef<typeof import('vue')['toRaw']>
    readonly toRef: UnwrapRef<typeof import('vue')['toRef']>
    readonly toRefs: UnwrapRef<typeof import('vue')['toRefs']>
    readonly toValue: UnwrapRef<typeof import('vue')['toValue']>
    readonly triggerRef: UnwrapRef<typeof import('vue')['triggerRef']>
    readonly unref: UnwrapRef<typeof import('vue')['unref']>
    readonly useAntProgressAnimate: UnwrapRef<typeof import('../src/hooks/useAntProgressAnimate')['useAntProgressAnimate']>
    readonly useAppidAndTenantid: UnwrapRef<typeof import('../src/hooks/useAppidAndTenantid')['default']>
    readonly useAttrs: UnwrapRef<typeof import('vue')['useAttrs']>
    readonly useBreakpoint: UnwrapRef<typeof import('../src/hooks/useBreakpoint')['useBreakpoint']>
    readonly useCssModule: UnwrapRef<typeof import('vue')['useCssModule']>
    readonly useCssVars: UnwrapRef<typeof import('vue')['useCssVars']>
    readonly useCurrentDark: UnwrapRef<typeof import('../src/stores/currentDark')['useCurrentDark']>
    readonly useCurrentRouteTab: UnwrapRef<typeof import('../src/stores/currentRouteTab')['useCurrentRouteTab']>
    readonly useCurrentUser: UnwrapRef<typeof import('../src/stores/currentUser')['useCurrentUser']>
    readonly useDialog: UnwrapRef<typeof import('../src/hooks/useDialog')['default']>
    readonly useGlobal: UnwrapRef<typeof import('../src/stores/global')['useGlobal']>
    readonly useId: UnwrapRef<typeof import('vue')['useId']>
    readonly useLink: UnwrapRef<typeof import('vue-router')['useLink']>
    readonly useMenuList: UnwrapRef<typeof import('../src/stores/menuList')['useMenuList']>
    readonly useModel: UnwrapRef<typeof import('vue')['useModel']>
    readonly usePagination: UnwrapRef<typeof import('../src/hooks/usePagination')['default']>
    readonly useRoute: UnwrapRef<typeof import('vue-router')['useRoute']>
    readonly useRouter: UnwrapRef<typeof import('vue-router')['useRouter']>
    readonly useSlots: UnwrapRef<typeof import('vue')['useSlots']>
    readonly useStartDevFormStore: UnwrapRef<typeof import('../src/stores/startDevForm')['useStartDevFormStore']>
    readonly useTemplateRef: UnwrapRef<typeof import('vue')['useTemplateRef']>
    readonly useUserPermission: UnwrapRef<typeof import('../src/hooks/useUserPermissions')['useUserPermission']>
    readonly useUserPermissionSuperAdmin: UnwrapRef<typeof import('../src/hooks/useUserPermissions')['useUserPermissionSuperAdmin']>
    readonly useUserPermissionsData: UnwrapRef<typeof import('../src/hooks/useUserPermissions')['useUserPermissionsData']>
    readonly useUserPermissionsEvery: UnwrapRef<typeof import('../src/hooks/useUserPermissions')['useUserPermissionsEvery']>
    readonly useUserPermissionsSome: UnwrapRef<typeof import('../src/hooks/useUserPermissions')['useUserPermissionsSome']>
    readonly watch: UnwrapRef<typeof import('vue')['watch']>
    readonly watchEffect: UnwrapRef<typeof import('vue')['watchEffect']>
    readonly watchPostEffect: UnwrapRef<typeof import('vue')['watchPostEffect']>
    readonly watchSyncEffect: UnwrapRef<typeof import('vue')['watchSyncEffect']>
  }
}