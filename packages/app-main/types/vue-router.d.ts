// This can be directly added to any of your `.ts` files like `router.ts`
// It can also be added to a `.d.ts` file. Make sure it's included in
// project's tsconfig.json "files"
import 'vue-router';

// To ensure it is treated as a module, add at least one `export` statement
export {};

declare module 'vue-router' {
  interface RouteMeta {
    /** 菜单树选中状态的标识 */
    menuKey?: string;
    /** 是否隐藏 layout 中的侧边菜单栏 */
    excludesSider?: boolean;
    /** 是否需要 layout 的 padding */
    withoutPadding?: boolean;
    /** 页面级权限参数 */
    pagePermission?: {
      permission?: string;
      permissionsEvery?: string[];
      permissionsSome?: string[];
    };
    // ---------------- Start: vue router tab 支持的参数 ----------------
    closable?: boolean;
    key?: 'path' | 'fullPath' | ((route: RouteLocationNormalizedLoaded) => string | undefined);
    tabKey?: string | ((route: RouteLocationNormalizedLoaded) => string | undefined);
    keepAlive?: boolean;
    title?: string | ((route: RouteLocationNormalizedLoaded) => string | undefined);
    keepAlive?: boolean;
    // ---------------- End: vue router tab 支持的参数 ----------------
  }
}
