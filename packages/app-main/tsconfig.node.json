{
  "extends": "@tsconfig/node18/tsconfig.json",
  "include": [
    "vite.config.*",
    "vitest.config.*",
    "cypress.config.*",
    "nightwatch.conf.*",
    "playwright.config.*",
    "scripts/**/*",
    "../app-common/untyped-modules.d.ts",
    "types/**/*.d.ts",
  ],
  "compilerOptions": {
    "strict": true,
    "verbatimModuleSyntax": true,
    "composite": true,
    "module": "ESNext",
    "moduleResolution": "Bundler",
    "types": [
      "node"
    ]
  }
}
