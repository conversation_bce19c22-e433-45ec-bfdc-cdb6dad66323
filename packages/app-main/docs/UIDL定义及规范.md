## 定义
UIDL（User Interface Definition Language）：用户界面定义语言。主要用来描述UI元素及其关系，以JSON格式表示。各语言实现层可以基于该规范实现自己的解析逻辑而产出不同渲染效果。
如下是基于Vuejs的渲染示例：
```json
{
  "type": "AButton",
  // "title": "按钮",
  // "description": "一个按钮",
  "props": {
    "size": "large",
    "type": "primary",
    "loading": false,
    "disabled": false
  },
  "children": [
    {
      // "componentName": "Text",
      "type": "span",
      "props": {
        "text": "按钮"
      }
    }
  ]
}
```

```html
<AButton size="large" type="primary" :loading="false" :disabled="false">按钮</AButton>
```

## 规范
UIDL需使用标准JSON格式，包含以下字段：

|字段名|类型|是否必填|描述|
|-|-|-|-|
|type|string|是|组件类型|
|title|string|是|组件中文名称|
|description|string|否|组件描述|
|props|object|否|组件属性|
|children|array|否|子组件|

## 物料文档规范
每个物料一个MD文件，文件名和物料type一致，如：AButton.md。文件内容包含以下部分
- type: 组件类型
- title: 组件名称
- description: 组件描述
- props: 组件属性(每个属性一行，包含属性名、类型、默认值，说明，参考Antd文档组件API部分)
- children: 子组件列表，如果是[]表示可以放置子组件，不设置表示不能放置子组件
- 组件UIDL示例

## 物料文档示例
AButton.md
```markdown
## 组件信息
- type：AButton
- title：按钮
- description：按钮用于开始一个即时操作
- props：

|属性名|说明|类型|默认值|
|-|-|-|-|
|block|将按钮宽度调整为其父宽度的选项|boolean|false|
|danger|设置危险按钮|boolean|false|
- children：[]
## 示例
```json
{
  "type": "AButton",
  "title": "按钮",
  "description": "按钮用于开始一个即时操作",
  "props": {
    "block": false,
    "danger": false
  },
  "children":[]
}
```
