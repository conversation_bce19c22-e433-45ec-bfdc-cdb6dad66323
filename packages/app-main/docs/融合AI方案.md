# 一、AI核心能力
## 调用第三方接口
[扣子-AI 智能体开发平台](https://www.coze.cn/home)

[阿里云登录 - 欢迎登录阿里云，安全稳定的云计算服务平台](https://bailian.console.aliyun.com/)

目前调研了扣子和阿里云百炼，两者功能差异如下：

| **核心功能点** | **扣子** | **阿里云百炼** |
| --- | --- | --- |
| 大模型选择 | 支持，6类12个大模型 | 支持，13类207个模型 |
| **模型调优** |  | **支持** |
| **知识库** | 支持 | 支持 |
| 插件 | 支持，官方插件、自定义 | 支持，官方插件、自定义 |
| 工作流 | 支持，节点较丰富 | 支持，目前只有核心节点 |
| 图像流 | 支持 | 不支持，可以通过工作流调图像理解模型实现 |


## <font style="color:#8A8F8D;">本地搭建大模型（暂时不考虑）</font>
[Ollama](https://ollama.com/)

[FastGPT](https://tryfastgpt.ai/)

暂时没有AI人才储备，自建大模型也只能使用现有的通用开源模型，短时间并不具备训练和微调模型能力，暂时先不考虑此方案，作为备用方案。

# 二、融合方向（前端）
## 页面构建
+ **根据需求生成页面**
+ 根据对话调整页面
+ 图片/设计稿转页面
    - screenshot-to-code
    - imgcook
+ 其他功能
    - 智能推荐
    - 文生CSS
    - 文生公式

![画板](https://cdn.nlark.com/yuque/0/2024/jpeg/159460/1731576745458-32ef6621-98b3-4301-8c05-3425b9459e53.jpeg)



## 前端逻辑编排
+ 自然预览生成编排
+ 根据对话调整编排
+ 编排逻辑解读
+ 增加大模型节点

