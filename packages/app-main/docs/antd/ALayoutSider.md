在进行**页面级布局**时，用于包裹“侧边栏”的一个“容器组件”。

## 何时使用

- 在进行页面级布局时使用；
- ALayoutSider 只能放在 `ALayout` 中；

## API

侧边栏。

| 参数 | 说明 | 类型 | 默认值 | 版本 |
| --- | --- | --- | --- | --- |
| breakpoint | 触发响应式布局的[断点](/components/grid#api) | `xs` \| `sm` \| `md` \| `lg` \| `xl` \| `xxl` | - |  |
| class | 容器 class | string | - |  |
| collapsed(v-model) | 当前收起状态 | boolean | - |  |
| collapsedWidth | 收缩宽度，设置为 0 会出现特殊 trigger | number | 80 |  |
| collapsible | 是否可收起 | boolean | false |  |
| defaultCollapsed | 是否默认收起 | boolean | false |  |
| reverseArrow | 翻转折叠提示箭头的方向，当 Sider 在右边时可以使用 | boolean | false |  |
| style | 指定样式 | object\|string | - |  |
| theme | 主题颜色 | `light` \| `dark` | `dark` |  |
| trigger | 自定义 trigger，设置为 null 时隐藏 trigger | string\|slot | - |  |
| width | 宽度 | number\|string | 200 |  |
| zeroWidthTriggerStyle | 指定当 `collapsedWidth` 为 0 时出现的特殊 trigger 的样式 | object | - | 1.5.0 |

## 使用示例
