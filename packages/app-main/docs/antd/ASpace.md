设置组件之间的间距。

## 何时使用

避免组件紧贴在一起，拉开统一的空间。

- 适合行内元素的水平间距。
- 可以设置各种水平对齐方式。
- 需要表单组件之间紧凑连接且合并边框时，使用 Space.Compact（自 `ant-design-vue@4.0.0` 版本开始提供该组件）。

## API

| 参数 | 说明 | 类型 | 默认值 | 版本 |
| --- | --- | --- | --- | --- |
| align | 对齐方式 | `start` \| `end` \|`center` \|`baseline` | - | 1.6.5 |
| direction | 间距方向 | `vertical` \| `horizontal` | `horizontal` | 1.6.5 |
| size | 间距大小 | `small` \| `middle` \| `large` \| `number` | `small` | 1.6.5 |
| split | 设置拆分 | VueNode \| v-slot | - | 2.2.0 |
| wrap | 是否自动换行，仅在 `horizontal` 时有效 | boolean | false | 2.2.0 |

## 使用示例
