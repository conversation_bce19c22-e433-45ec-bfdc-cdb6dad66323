Atabs 中的每一项。

## 何时使用

- 在使用 ATabs 组件时，必须在 ATabs 中嵌套 ATabPane 组件，包裹 tab 栏中的每一项；

## API

### ATabPane Props

| 参数        | 说明                      | 类型         | 默认值 |
| ----------- | ------------------------- | ------------ | ------ |
| forceRender | 被隐藏时是否渲染 DOM 结构 | boolean      | false  |
| key         | 对应 activeKey            | string       | -      |
| tab         | 选项卡头显示文字          | string\|slot | -      |

### ATabPane 插槽

| 插槽名称  | 说明                                            | 参数 |
| --------- | ----------------------------------------------- | ---- |
| closeIcon | 自定义关闭图标，`在 type="editable-card"`时有效 | -    |
| tab       | 选项卡头显示文字                                | -    |

## 使用示例
