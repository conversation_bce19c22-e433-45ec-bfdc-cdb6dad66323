时间范围选择器。

## 何时使用

- 需要选择一个时间“范围”时

## API

属性与 DatePicker 的 [RangePicker](/components/date-picker/#rangepicker) 相同。还包含以下属性：

| 参数         | 说明                 | 类型                                    | 默认值 | 版本  |
| ------------ | -------------------- | --------------------------------------- | ------ | ----- |
| order        | 始末时间是否自动排序 | boolean                                 | true   |       |
| disabledTime | 不可选择的时间       | [RangeDisabledTime](#pangedisabledtime) | -      | 3.3.0 |

### RangeDisabledTime

```typescript
type RangeDisabledTime = (
  now: Dayjs,
  type = 'start' | 'end',
) => {
  disabledHours?: () => number[];
  disabledMinutes?: (selectedHour: number) => number[];
  disabledSeconds?: (selectedHour: number, selectedMinute: number) => number[];
};
```

## 使用示例
