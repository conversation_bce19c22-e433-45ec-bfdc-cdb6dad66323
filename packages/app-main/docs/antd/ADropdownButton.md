带下拉框的按钮。

## 何时使用

左边是按钮，右边是额外的相关功能菜单。可设置 `icon` 属性来修改右边的图标。

## API

| 参数 | 说明 | 类型 | 默认值 | 版本 |
| --- | --- | --- | --- | --- |
| disabled | 菜单是否禁用 | boolean | - |  |
| icon | 右侧的 icon | VNode \| slot | - | 1.5.0 |
| loading | 设置按钮载入状态 | boolean \| { delay: number } | false | 3.0 |
| overlay(v-slot) | 菜单 | [Menu](/components/menu-cn/) | - |  |
| placement | 菜单弹出位置 | `bottomLeft` \| `bottom` \| `bottomRight` \| `topLeft` \| `top` \| `topRight` | `bottomLeft` |  |
| size | 按钮大小，和 [Button](/components/button-cn/) 一致 | string | 'default' |  |
| trigger | 触发下拉的行为 | Array&lt;`click`\|`hover`\|`contextmenu`> | `['hover']` |  |
| type | 按钮类型，和 [Button](/components/button-cn/) 一致 | string | 'default' |  |
| open(v-model) | 菜单是否显示 | boolean | - |  |

## 使用示例
