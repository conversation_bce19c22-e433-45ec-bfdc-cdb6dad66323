单选框。

## 何时使用

- 用于在多个备选项中选中单个状态。
- 和 Select 的区别是，Radio 所有选项默认可见，方便用户在比较中选择，因此选项不宜过多。

## API

| 参数             | 说明                              | 类型    | 默认值 |
| ---------------- | --------------------------------- | ------- | ------ |
| autofocus        | 自动获取焦点                      | boolean | false  |
| checked(v-model) | 指定当前是否选中                  | boolean | false  |
| disabled         | 禁用 Radio                        | boolean | false  |
| value            | 根据 value 进行比较，判断是否选中 | any     | -      |

## 使用示例
