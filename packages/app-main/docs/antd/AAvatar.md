用来代表用户或事物，支持图片、图标或字符展示。

## 何时使用

需要展示头像的地方，比如页面右上角，或者列表的头部，用于标识当前登录或评论的用户的头像。

## API

| 参数 | 说明 | 类型 | 默认值 | 版本 |
| --- | --- | --- | --- | --- |
| alt | 图像无法显示时的替代文本 | string | - |  |
| crossOrigin | cors 属性设置 | `'anonymous'` \| `'use-credentials'` \| `''` | - | 3.0 |
| draggable | 图片是否允许拖动 | boolean \| `'true'` \| `'false'` | - | 2.2.0 |
| gap | 字符类型距离左右两侧边界单位像素 | number | 4 | 2.2.0 |
| icon | 设置头像的图标类型，可设为 Icon 的 `type` 或 VNode | VNode \| slot | - |  |
| loadError | 图片加载失败的事件，返回 false 会关闭组件默认的 fallback 行为 | () => boolean | - |  |
| shape | 指定头像的形状 | `circle` \| `square` | `circle` |  |
| size | 设置头像的大小 | number \| `large` \| `small` \| `default` \| { xs: number, sm: number, ...} | `default` | 2.2.0 |
| src | 图片类头像的资源地址 | string | - |  |
| srcset | 设置图片类头像响应式资源地址 | string | - |  |

## 使用示例
