这是 ASkeleton 中的一个嵌套子组件，用于渲染“按钮”的骨架屏占位。

## 何时使用

- 需要一个“按钮”样式的骨架占位时使用；

## API

| 属性   | 说明                           | 类型                             | 默认值 | 版本 |
| ------ | ------------------------------ | -------------------------------- | ------ | ---- |
| active | 是否展示动画效果               | boolean                          | false  |      |
| block  | 将按钮宽度调整为其父宽度的选项 | boolean                          | false  |      |
| shape  | 指定按钮的形状                 | `circle` \| `round` \| `default` | -      |      |
| size   | 设置按钮的大小                 | `large` \| `small` \| `default`  | -      |      |

## 使用示例
