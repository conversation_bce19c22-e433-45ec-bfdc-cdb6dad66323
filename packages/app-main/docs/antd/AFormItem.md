用于包裹 AForm 组件中的表单项。

## 何时使用

- AFormItem **必须**嵌套在 AFrom 组件中使用；
- AFormItem 中只能存在一个表单域，比如 AInput、ARadio、ASelect 等；
- 凡是用到表单的地方，比如：登录、新增、更新、搜索时，都需要使用 AFrom + AFormItem 包裹表单控件；

## API

| 参数 | 说明 | 类型 | 默认值 | 版本 |
| --- | --- | --- | --- | --- |
| autoLink | 是否自动关联表单域，对于大部分情况都可以使用自动关联，如果不满足自动关联的条件，可以手动关联，参见下方注意事项 | boolean | true |  |
| colon | 配合 label 属性使用，表示是否显示 label 后面的冒号 | boolean | true |  |
| extra | 额外的提示信息，和 help 类似，当需要错误信息和提示文案同时出现时，可以使用这个。 | string\|slot |  |  |
| hasFeedback | 配合 validateStatus 属性使用，展示校验状态图标，建议只配合 Input 组件使用 | boolean | false |  |
| help | 提示信息，如不设置，则会根据校验规则自动生成 | string\|slot |  |  |
| htmlFor | 设置子元素 label `htmlFor` 属性 | string |  |  |
| label | label 标签的文本 | string\|slot |  |  |
| labelAlign | 标签文本对齐方式 | 'left' \| 'right' | 'right' |  |
| labelCol | label 标签布局，同 `<Col>` 组件，设置 `span` `offset` 值，如 `{span: 3, offset: 12}` 或 `sm: {span: 3, offset: 12}` | [object](/components/grid-cn/#col) |  |  |
| name | 表单域 model 字段，在使用 validate、resetFields 方法的情况下，该属性是必填的 | [NamePath](#namepath) |  |  |
| required | 是否必填，如不设置，则会根据校验规则自动生成 | boolean | false |  |
| rules | 表单验证规则 | object \| array |  |  |
| tooltip | 配置提示信息 | string \| slot |  | 4.0.4 |
| validateFirst | 当某一规则校验不通过时，是否停止剩下的规则的校验。 | boolean | false | 2.0.0 |
| validateStatus | 校验状态，如不设置，则会根据校验规则自动生成，可选：'success' 'warning' 'error' 'validating' | string |  |  |
| validateTrigger | 设置字段校验的时机 | string \| string\[] | `change` | 2.0.0 |
| wrapperCol | 需要为输入控件设置布局样式时，使用该属性，用法同 labelCol | [object](/components/grid-cn/#col) |  |  |

## 使用示例
