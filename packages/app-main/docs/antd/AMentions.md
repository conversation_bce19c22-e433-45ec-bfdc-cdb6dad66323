提及组件。

## 何时使用

- 用于在输入中提及某人或某事，常用于发布、聊天或评论功能。

## API

### Mentions

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- | --- |
| autofocus | 自动获得焦点 | boolean | `false` |
| defaultValue | 默认值 | string |  |
| filterOption | 自定义过滤逻辑 | false \| (input: string, option: OptionProps) => boolean |  |
| getPopupContainer | 指定建议框挂载的 HTML 节点 | () => HTMLElement |  |
| notFoundContent | 当下拉列表为空时显示的内容 | string \| slot | 'Not Found' |
| placement | 弹出层展示位置 | `top` \| `bottom` | `bottom` |
| prefix | 设置触发关键字 | string \| string\[] | '@' |
| split | 设置选中项前后分隔符 | string | ' ' |
| status | 设置校验状态 | 'error' \| 'warning' | - | 3.3.0 |
| validateSearch | 自定义触发验证逻辑 | (text: string, props: MentionsProps) => void |  |
| value(v-model) | 设置值 | string |  |
| options | 选项配置 | [Options](#option) | \[] | 4.0 |
| option | 通过 option 插槽，自定义节点 | v-slot:option="option" | - | 4.0 |

### Option

| 参数      | 说明           | 类型                | 默认值 |
| --------- | -------------- | ------------------- | ------ |
| value     | 选择时填充的值 | string | number             | -      |
| label     | 选项的标题     | VueNode | (o: Option)=> VueNode     | -      |
| disabled  | 是否可选       | boolean             | -      |
| class | css 类名       | string              | -      |
| style     | 选项样式       | CSSProperties | -      |
|payload| 其它数据 | object | - |

## 使用示例
