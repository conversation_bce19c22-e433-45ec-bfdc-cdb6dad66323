方便的从数组生成 ACheckbox。

## 何时使用

一个一个设置 ACheckbox，可能显得繁琐，此时就可以用 ACheckboxGroup，使用 `:options` 指定复选框数组。

## API

| 参数 | 说明 | 类型 | 默认值 | 版本 |
| --- | --- | --- | --- | --- |
| disabled | 整组失效 | boolean | false |  |
| name | CheckboxGroup 下所有 `input[type="checkbox"]` 的 `name` 属性 | string | - | 1.5.0 |
| options | 指定可选项，可以通过 slot="label" slot-scope="option" 定制`label` | string\[] \| Array&lt;{ label: string value: string disabled?: boolean, indeterminate?: boolean, onChange?: function }> | \[] |  |
| value(v-model) | 指定选中的选项 | (boolean \| string \| number)\[] | \[] |  |

## 使用示例
