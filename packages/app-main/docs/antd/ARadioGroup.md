单选按钮组，需要在**一组**可选项中**单选**一项时使用。

## 何时使用

- 当单选按钮（ARadioButton）或单选框（ARadio）不止1个时，建议使用 ARadioGroup 包裹，并使用 `:options` 指定单选项；
- 需要在**一组**可选项中**单选**一项时；

## API

单选框组合，用于包裹一组 `Radio`。

| 参数 | 说明 | 类型 | 默认值 | 版本 |
| --- | --- | --- | --- | --- |
| buttonStyle | RadioButton 的风格样式，目前有描边和填色两种风格 | `outline` \| `solid` | `outline` |  |
| disabled | 禁选所有子单选器 | boolean | false |  |
| name | RadioGroup 下所有 `input[type="radio"]` 的 `name` 属性 | string | - |  |
| options | 以配置形式设置子元素 | string\[] \| number[] \| Array&lt;{ label: string value: string disabled?: boolean }> | - |  |
| optionType | 用于设置 Radio `options` 类型 | `default` \| `button` | `default` | 3.0.0 |
| size | 大小，只对按钮样式生效 | `large` \| `default` \| `small` | `default` |  |
| value(v-model) | 用于设置当前选中的值 | any | - |  |

## 使用示例
