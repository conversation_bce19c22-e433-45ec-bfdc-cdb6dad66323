这是 ASkeleton 中的一个嵌套子组件，用于渲染“图片”的骨架屏占位。

## 何时使用

- 需要一个“图片”样式的骨架占位时使用；

## API

| 属性  | 说明                 | 类型                                      | 默认值 |
| ----- | -------------------- | ----------------------------------------- | ------ |
| shape | 指定头像的形状       | `circle` \| `square`                      | -      |
| size  | 设置头像占位图的大小 | number \| `large` \| `small` \| `default` | -      |

## 使用示例
