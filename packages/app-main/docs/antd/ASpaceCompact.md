它是 ASpace 组件的一个“变体”，如果你需要表单组件之间紧凑连接，并且合并边框时，那就可以用上它。

## 何时使用

需要表单组件之间紧凑连接且合并边框时，使用 ASpaceCompact。支持的组件有：

- AButton
- AAutoComplete
- ACascader
- ADatePicker
- AInput/AInputSearch
- ASelect
- ATimePicker
- ATreeSelect

## API

| 参数      | 说明                         | 类型                           | 默认值       | 版本  |
| --------- | ---------------------------- | ------------------------------ | ------------ | ----- |
| block     | 将宽度调整为父元素宽度的选项 | boolean                        | false        | 4.0.0 |
| direction | 指定排列方向                 | `vertical` \| `horizontal`     | `horizontal` | 4.0.0 |
| size      | 子组件大小                   | `large` \| `middle` \| `small` | `middle`     | 4.0.0 |

## 使用示例
