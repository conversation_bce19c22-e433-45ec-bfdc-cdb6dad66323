协助进行页面级整体布局。

## 何时使用

在需要进行**页面级布局**时，配合以下组件使用：
- `ALayout`：布局容器，其下可嵌套 `ALayoutHeader` `ALayoutSider` `ALayoutContent` `ALayoutFooter` 或 `ALayout` 本身，可以放在任何父容器中。
- `ALayoutHeader`：顶部布局，自带默认样式，其下可嵌套任何元素，只能放在 `ALayout` 中。
- `ALayoutSider`：侧边栏，自带默认样式及基本功能，其下可嵌套任何元素，只能放在 `ALayout` 中。
- `ALayoutContent`：内容部分，自带默认样式，其下可嵌套任何元素，只能放在 `ALayout` 中。
- `ALayoutFooter`：底部布局，自带默认样式，其下可嵌套任何元素，只能放在 `ALayout` 中。

## API

布局容器。

| 参数     | 说明                                                               | 类型    | 默认值 |
| -------- | ------------------------------------------------------------------ | ------- | ------ |
| class    | 容器 class                                                         | string  | -      |
| hasSider | 表示子元素里有 Sider，一般不用指定。可用于服务端渲染时避免样式闪动 | boolean | -      |
| style    | 指定样式

## 使用示例
