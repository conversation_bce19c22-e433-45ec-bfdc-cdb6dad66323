在 ACollapse 中使用的折叠面板元素项。

## 何时使用

- 只能在 ACollapse 组件中使用；
- 每一个 ACollapsePane 组件，就代表了一个折叠面板容器；

## API

| 参数        | 说明                           | 类型                   | 默认值 | 版本  |
| ----------- | ------------------------------ | ---------------------- | ------ | ----- |
| collapsible | 是否可折叠或指定可折叠触发区域 | `header` \| `disabled` | -      | 3.0   |
| extra       | 自定义渲染每个面板右上角的内容 | VNode \| slot          | -      | 1.5.0 |
| forceRender | 被隐藏时是否渲染 DOM 结构      | boolean                | false  |       |
| header      | 面板头内容                     | string\|slot           | -      |       |
| key         | 对应 activeKey                 | string \| number       | -      |       |
| showArrow   | 是否展示当前面板上的箭头       | boolean                | `true` |       |

## 使用示例
