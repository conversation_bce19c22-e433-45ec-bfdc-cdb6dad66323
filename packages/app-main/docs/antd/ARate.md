评分组件。

## 何时使用

- 对评价进行展示。
- 对事物进行快速的评级操作。

## API

| 属性           | 说明                   | 类型           | 默认值             |
| -------------- | ---------------------- | -------------- | ------------------ |
| allowClear     | 是否允许再次点击后清除 | boolean        | true               |
| allowHalf      | 是否允许半选           | boolean        | false              |
| autofocus      | 自动获取焦点           | boolean        | false              |
| character      | 自定义字符             | string \| slot | `<StarOutlined />` |
| count          | star 总数              | number         | 5                  |
| disabled       | 只读，无法进行交互     | boolean        | false              |
| tooltips       | 自定义每项的提示信息   | string[]       | -                  |
| value(v-model) | 当前数，受控值         | number         | -                  |

## 使用示例
