{"name": "app-main", "version": "1.0.0", "private": true, "scripts": {"dev-integration": "cross-env ENABLE_DEV_INTEGRATION=true vite", "serve": "vite", "serve-alone": "vite --mode development-alone", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "build-only-development": "vite build --mode development", "build-only-development-alone": "vite build --mode development-alone", "build-only-release": "NODE_OPTIONS=--max_old_space_size=24576 vite build --mode release", "build-only-release-alone": "vite build --mode release-alone", "type-check": "vue-tsc --noEmit -p tsconfig.app.json --composite false", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@fantage9/vue-router-tab": "^0.9.23", "@guolao/vue-monaco-editor": "^1.5.5", "@howiefh/ant-path-matcher": "^0.0.4", "@microsoft/fetch-event-source": "^2.0.1", "@tinyhttp/content-disposition": "^2.2.2", "@types/file-saver": "^2.0.7", "@types/sm-crypto": "^0.3.4", "@vueup/vue-quill": "^1.2.0", "@vueuse/core": "^13.3.0", "@vueuse/integrations": "^13.3.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "ant-design-vue": "^4.2.6", "await-to-js": "^3.0.0", "axios": "^1.10.0", "codemirror": "^5.65.18", "cron-validator": "^1.3.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "echarts-liquidfill": "^3.1.0", "editor-plus": "file:../editor-plus", "file-saver": "^2.0.5", "futil": "^1.76.4", "highlight.js": "^11.11.1", "html2canvas": "^1.4.1", "js-base64": "^3.7.7", "js-cookie": "^3.0.5", "jsencrypt": "^3.3.2", "jszip": "^3.10.1", "lodash-es": "^4.17.21", "md-editor-v3": "^5.7.0", "mime-db": "^1.54.0", "mitt": "^3.0.1", "monaco-editor": "0.51.0", "nprogress": "^0.2.0", "pako": "^2.1.0", "path-browserify": "^1.0.1", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.3.0", "remeda": "^1.29.0", "sm-crypto": "^0.3.13", "sortablejs": "^1.15.6", "speed-components-ui": "^0.1.15", "split.js": "^1.6.5", "string-width": "^7.2.0", "ufo": "^1.6.1", "vue": "^3.5.16", "vue-clipboard3": "^2.0.0", "vue-collapsed": "^1.3.4", "vue-countup-v3": "^1.4.2", "vue-draggable-plus": "^0.6.0", "vue-echarts": "^7.0.3", "vue-highlight-words": "^3.0.1", "vue-hooks-plus": "^2.4.0", "vue-router": "^4.5.1", "vue3-colorpicker": "^2.3.0", "vue3-cron-antd": "^1.1.1", "vuedraggable": "^4.1.0", "wujie-polyfill": "^1.1.3", "wujie-vue3": "^1.0.28", "xss": "^1.0.15", "zod": "^3.25.67"}, "devDependencies": {"@rushstack/eslint-patch": "^1.11.0", "@tsconfig/node18": "^18.2.4", "@types/crypto-js": "^4.2.2", "@types/js-cookie": "^3.0.6", "@types/lodash-es": "^4.17.12", "@types/mime-db": "^1.43.6", "@types/node": "^24.0.3", "@types/nprogress": "^0.2.3", "@types/pako": "^2.0.3", "@types/path-browserify": "^1.0.3", "@types/sortablejs": "^1.15.8", "@unocss/eslint-config": "^0.57.7", "@vitejs/plugin-vue": "^5.2.4", "@vitejs/plugin-vue-jsx": "^4.2.0", "@vue-hooks-plus/resolvers": "^1.3.0", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.7.0", "eslint": "^8.54.0", "eslint-import-resolver-typescript": "^3.9.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-simple-import-sort": "^10.0.0", "eslint-plugin-vue": "^9.24.0", "eslint-plugin-vue-scoped-css": "^2.10.0", "less": "^4.3.0", "npm-run-all2": "^8.0.4", "postcss": "^8.5.6", "prettier": "^3.5.3", "typescript": "~5.2.2", "unocss": "^0.57.7", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.7.0", "vite": "6.0.13", "vite-plugin-bundle-obfuscator": "^1.5.0", "vite-plugin-html": "^3.2.2", "vite-plugin-iconfont": "^1.5.3", "vite-plugin-monaco-editor": "^1.1.0", "vite-plugin-obfuscator": "^1.0.5", "vue-component-type-helpers": "^2.2.10", "vue-eslint-parser": "^9.4.3", "vue-tsc": "^2.2.10"}}