import { fileURLToPath, URL } from 'node:url';

import vue from '@vitejs/plugin-vue';
import vueJsx from '@vitejs/plugin-vue-jsx';
import { VueHooksPlusResolver } from '@vue-hooks-plus/resolvers';
import UnoCSS from 'unocss/vite';
import autoImport from 'unplugin-auto-import/vite';
import { AntDesignVueResolver } from 'unplugin-vue-components/resolvers';
import unpluginComponents from 'unplugin-vue-components/vite';
import { type ConfigEnv, defineConfig, loadEnv, type ProxyOptions } from 'vite';
import vitePluginBundleObfuscator from 'vite-plugin-bundle-obfuscator';

// import { compression } from 'vite-plugin-compression2';
// import devIntegration from 'vite-plugin-dev-integration';
import { createHtmlPlugin } from 'vite-plugin-html';
import iconfont from 'vite-plugin-iconfont';
import monacoEditorPlugin from 'vite-plugin-monaco-editor';

// https://vitejs.dev/config/
export default ({ mode, command }: ConfigEnv) => {
  const env = loadEnv(mode, process.cwd());
  // const IS_PRODUCTION = command === 'build';
  const proxy: Record<string, string | ProxyOptions> = {
    [env.VITE_API_PREFIX]: {
      target: env.VITE_API_HOST,
      ws: true,
      changeOrigin: false, // 将Origin的来源更改为目标URL
      rewrite: (path) => path.replace(new RegExp(`^${env.VITE_API_PREFIX}`), ''),
      configure: (proxy) => {
        proxy.on('proxyRes', (proxyRes) => {
          proxyRes.headers['Access-Control-Expose-Headers'] = '*';
        });
      },
    },
    // [env.VITE_API_ATTACHMENT_BASE_URL]: {
    //   target: env.VITE_API_PROXY_ATTACHMENT_URL,
    //   changeOrigin: true, // 将Origin的来源更改为目标URL
    //   rewrite: (path) => path.replace(new RegExp(`^${env.VITE_API_ATTACHMENT_BASE_URL}`), ''),
    //   configure: (proxy) => {
    //     proxy.on('proxyRes', (proxyRes) => {
    //       proxyRes.headers['Access-Control-Expose-Headers'] = '*';
    //     });
    //   },
    // },
  };

  return defineConfig({
    base: env.VITE_BASIC_PATH,
    server: {
      host: true,
      port: 4000,
      strictPort: true,
      proxy,
      // hmr: true,
      // watch: process.env['ENABLE_DEV_INTEGRATION'] === 'true' ? null : undefined,
    },
    preview: {
      host: true,
      port: 5000,
      strictPort: true,
      proxy,
    },
    plugins: [
      // !IS_PRODUCTION &&
      //   devIntegration({
      //     rootPath: fileURLToPath(new URL('../../', import.meta.url)),
      //   }),
      monacoEditorPlugin({}),
      createHtmlPlugin({
        minify: true,
        inject: {
          data: {
            VITE_BASIC_TITLE: env.VITE_BASIC_PAGE_TITLE,
            VITE_AUTH_TYPE: env.VITE_AUTH_TYPE,
            VITE_BUILD_TIMESTAMP: new Date().toLocaleString().replaceAll(/[/\s:]/g, '_'),
          },
        },
      }),
      UnoCSS(),
      vue(),
      vueJsx(),
      env.VITE_BUILD_JAVASCRIPT_OBFUSCATOR === 'true' &&
        vitePluginBundleObfuscator({
          threadPool: true,
          autoExcludeNodeModules: false,
          options: {
            controlFlowFlattening: false,
            deadCodeInjection: false,
            disableConsoleOutput: false,
            log: false,
            numbersToExpressions: false,
            renameGlobals: false,
            selfDefending: false,
            simplify: false,
            splitStrings: false,
            unicodeEscapeSequence: false,
            transformObjectKeys: false,

            compact: true,
            controlFlowFlatteningThreshold: 1,
            deadCodeInjectionThreshold: 1,
            stringArrayCallsTransformThreshold: 1,
            stringArrayThreshold: 1,
            reservedStrings: ['.js$', '^_'],
            stringArray: true,
            stringArrayEncoding: [],
            debugProtection: true,
            debugProtectionInterval: 2000,
          },
        }),
      // env.VITE_BUILD_JAVASCRIPT_OBFUSCATOR === 'true' &&
      //   viteObfuscateFile({
      //     controlFlowFlattening: false,
      //     deadCodeInjection: false,
      //     disableConsoleOutput: false,
      //     log: false,
      //     numbersToExpressions: false,
      //     renameGlobals: false,
      //     selfDefending: false,
      //     simplify: false,
      //     splitStrings: false,
      //     unicodeEscapeSequence: false,
      //     transformObjectKeys: false,

      //     compact: true,
      //     controlFlowFlatteningThreshold: 1,
      //     deadCodeInjectionThreshold: 1,
      //     stringArrayCallsTransformThreshold: 1,
      //     stringArrayThreshold: 1,
      //     reservedStrings: ['.js$', '^_'],
      //     stringArray: true,
      //     stringArrayEncoding: [],
      //     debugProtection: true,
      //     debugProtectionInterval: 2000,
      //   }),
      autoImport({
        imports: ['vue', 'vue-router', 'pinia'],
        dts: 'types/auto-imports.d.ts',
        // resolvers: [AntDesignVueResolver()],
        dirs: ['src/hooks', 'src/stores'], // 需要自动导入的文件目录
        vueTemplate: true,
        // 可能会导致一些打包后的组件报错，参考：https://juejin.cn/post/7360283967830818850
        ignore: ['h'],
        resolvers: [VueHooksPlusResolver()],
        eslintrc: {
          enabled: true,
          filepath: './.eslintrc-auto-import.json',
          globalsPropValue: true,
        },
      }),
      // 自动按需加载组件 https://github.com/antfu/unplugin-vue-components
      unpluginComponents({
        dts: 'types/components.d.ts',
        dirs: ['src/components'],
        extensions: ['vue', 'tsx'],
        directoryAsNamespace: false,
        resolvers: [
          AntDesignVueResolver({
            resolveIcons: true,
            importStyle: false, // ant-design-vue采用cssinjs，不需要引入样式文件
          }),
        ],
      }),
      env.VITE_ENABLE_ICONFONT_PLUGIN !== 'false' &&
        command !== 'build' &&
        iconfont({
          url: env.VITE_ICONFONT_URL,
          distUrl: './public/iconfont/iconfont.js',
          // iconJson: './src/components/IconPicker/data.json',
          inject: false, // 如果不开启，需要自己引入iconfont.js
          dts: './types/iconfont.d.ts',
        }),
      // compression({
      //   threshold: 1025, // 阈值，大于此值才会被压缩
      //   algorithm: 'gzip', // 压缩算法
      // }),
    ],
    build: {
      sourcemap: false,
      minify: 'terser',
      terserOptions: {
        format: {
          comments: false,
        },
        // compress: {
        //   evaluate: false,
        //   drop_console: true,
        //   drop_debugger: true,
        // },
      },
    },
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url)),
      },
    },
  });
};
