apiVersion: v1
kind: Secret
type: kubernetes.io/dockerconfigjson
metadata:
  # 可自定义
  name: dis-docker-creds1
  # 填写分配的的命名空间
  namespace: zion-b3bws3cg # 如果不是默认命名空间，请更改这个值
data:
   # 创建.dockerconfigjson类型的Secret的示范样例
   # docker login artifact.srdcloud.cn 或 docker login gz01-srdart.srdcloud.cn 根据项目制品库的docker制品库使用指引登录对应的制品库域名
   # 登录后，会自动生成~/.docker/config.json文件
   # cd ~
   # cat .docker/config.json | base64
   # 生成base64密文后粘贴到下面属性上
  .dockerconfigjson: {{.dockerconfigjson}}
---
kind: Deployment
apiVersion: apps/v1
metadata:
  name: dev-integration-ui-develop
  namespace: zion-b3bws3cg
  labels:
    app: dev-integration-ui-develop
spec:
  replicas: 1
  selector:
    matchLabels:
      app: dev-integration-ui-develop
  template:
    metadata:
      name: dev-integration-ui-develop
      labels:
        app: dev-integration-ui-develop
    spec:
      imagePullSecrets:
        - name: dis-docker-creds1
      containers:
        - name: dev-integration-ui-develop
          # 研发云的镜像地址
          image: gz01-srdart.srdcloud.cn/dev-integration/dev_integration-snapshot-docker-local/dev-integration-ui-develop:{{.commitId}}
          imagePullPolicy: Always
          # envFrom:
          #   - configMapRef:
          #       name: dis-env
          #   - secretRef:
          #       name: dis-secret
          env:
            - name: platform_server_url
              value: {{.platformServerUrl}}
            - name: api_server_url
              value: {{.apiServerUrl}}

          ports:
            # 应用端口
            - containerPort: 80
          #readinessProbe:  #用于判断容器是否准备好接收流量 失败将摘除pod
            #httpGet:
              #path: /doc.html # 设置健康检查的路径
              #port: 8082
            #initialDelaySeconds: 20 # 容器启动后延迟多久开始检查
            #periodSeconds: 10 # 检查周期
          #livenessProbe:  #用于检测容器是否仍然存活 失败将重启
            #httpGet:
              #path: /doc.html # 设置健康检查的路径
              #port: 8082
            #initialDelaySeconds: 60 # 容器启动后延迟多久开始检查
            #periodSeconds: 15 # 检查周期

---
kind: Service
apiVersion: v1
metadata:
  name: dev-integration-ui-develop
  namespace: zion-b3bws3cg
spec:
  ports:
    - protocol: TCP
      # 服务对内端口,dockerfile中EXPOSE的端口
      port: 80
      # 应用端口, 要和nginx的端口一致
      targetPort: 80
      # 服务对外端口,如果提示端口被占用，则换个端口重新部署
      nodePort: 32000
  selector:
    app: dev-integration-ui-develop
  type: NodePort