import { message } from 'ant-design-vue';
import { to as awaitTo } from 'await-to-js';
import { merge } from 'remeda';
import { withQuery } from 'ufo';
import type { NavigationGuardNext, RouteLocationNormalized } from 'vue-router';

interface ZionVueRouterBeforeEachOptions {
  to: RouteLocationNormalized;
  next: NavigationGuardNext;
  loginByTicket: (options: { ticket: string; tenantId?: string }) => Promise<
    | {
        prefix?: string;
        token: string;
        tokenName: string;
      }
    | null
    | undefined
  >;
  getSsoAuthUrl: (clientLoginUrl: string) => Promise<string | undefined>;
}

function removeQueryParams(url: string, paramsToRemove: string[]): string {
  // 分割 URL 和查询字符串
  const [baseUrl, queryStringOriginal] = url.split('?');

  if (queryStringOriginal) {
    let queryString = queryStringOriginal;

    // 为每个要移除的参数创建正则表达式
    paramsToRemove.forEach((param) => {
      const regex = new RegExp(`(^|&)${param}=[^&]*(&|$)`, 'g');
      queryString = queryString.replace(regex, '$1');
    });

    // 移除开头和结尾的 &，以及连续的 &
    queryString = queryString.replace(/^&+|&+$/g, '').replace(/&+/g, '&');

    // 如果还有查询参数，重新组合 URL
    return queryString ? `${baseUrl}?${queryString}` : baseUrl;
  }

  // 如果没有查询字符串，直接返回原始 URL
  return url;
}

export const asyncZionVueRouterBeforeEach = async (options: ZionVueRouterBeforeEachOptions) => {
  const defaultOptions: Partial<ZionVueRouterBeforeEachOptions> = {};

  const { to, next, loginByTicket, getSsoAuthUrl } = merge(defaultOptions, options);

  const ticket = to.query.ticket;
  const tenantId = to.query.zionAuthTenantId;
  if (
    ticket &&
    typeof ticket === 'string' &&
    (typeof tenantId === 'string' || typeof tenantId === 'undefined')
  ) {
    window.localStorage.removeItem('zion-auth-key');
    window.localStorage.removeItem('zion-auth-value');

    const clientLoginUrl = removeQueryParams(window.location.href, ['ticket', 'zionAuthTenantId']);

    // 有 ticket 则使用 ticket 登录
    const [err, res] = await awaitTo(loginByTicket({ ticket, tenantId }));
    const nextRoute = {
      ...to,
      query: { ...to.query, ticket: undefined, zionAuthTenantId: undefined },
    };

    if (err || !res) {
      if (!getSsoAuthUrl) {
        message.error('无法获取登录地址');
        throw new Error('无法获取登录地址');
      }
      const [redirectErr, redirectRes] = await awaitTo(getSsoAuthUrl(clientLoginUrl));
      if (redirectErr || !redirectRes) {
        message.error('无法获取登录地址');
        throw new Error('无法获取登录地址');
      }

      const countDownKey = 'loginByTicketError';
      let countDown = 4;
      const countDownInterval = setInterval(() => {
        countDown -= 1;
        if (countDown <= 0) {
          clearInterval(countDownInterval);
          window.location.href = withQuery(redirectRes, { clear: 'true' });
        }
        message.loading({
          content: `${countDown}秒后跳转回登录页`,
          key: countDownKey,
          duration: 0,
        });
      }, 1000);

      throw new Error('无法换取登录信息');
    }

    const { prefix, token, tokenName } = res;
    window.localStorage.setItem('zion-auth-key', tokenName);
    window.localStorage.setItem('zion-auth-value', prefix ? `${prefix} ${token}` : token);
    window.location.replace(clientLoginUrl);
    return next(nextRoute);
  }

  return true;
};
