/* eslint-env node */
const path = require('path');

require('@rushstack/eslint-patch/modern-module-resolution');

const createESLintConfig = ({ dirname } = {}) => {
  return {
    root: true,
    plugins: ['simple-import-sort', 'vue-scoped-css'],
    env: {
      node: true,
      browser: true,
    },
    extends: [
      '@unocss',
      'plugin:vue/vue3-recommended',
      'eslint:recommended',
      '@vue/eslint-config-typescript',
      'plugin:import/recommended',
      'plugin:import/typescript',
      '@vue/eslint-config-prettier',
    ],
    settings: {
      'import/resolver': {
        typescript: {
          project: [
            path.resolve(dirname, 'tsconfig.json'),
            path.resolve(dirname, 'tsconfig.app.json'),
            path.resolve(dirname, 'tsconfig.node.json'),
          ],
        },
      },
    },
    parserOptions: { ecmaVersion: 'latest' },
    rules: {
      'import/no-unresolved': ['error', { ignore: ['virtual:uno.css'] }],
      'simple-import-sort/imports': [
        'warn',
        { groups: [['^node:'], ['^@?\\w'], ['^'], ['^\\.'], ['^\\u0000']] },
      ],
      'simple-import-sort/exports': 'warn',
      'import/first': 'warn',
      'import/newline-after-import': ['warn', { considerComments: true }],
      'import/default': 'off',
      'import/no-named-as-default': 'off',

      'vue/component-name-in-template-casing': [
        'warn',
        'PascalCase',
        { registeredComponentsOnly: false },
      ],
      'vue/component-tags-order': ['warn', { order: ['script', 'template', 'style'] }],
      'vue/padding-line-between-blocks': ['warn', 'always'],
      'vue/block-lang': ['error', { script: { lang: ['ts', 'tsx'] }, style: { lang: 'less' } }],
      'vue/component-api-style': ['error', ['script-setup']],
      'vue/multi-word-component-names': [
        'error',
        {
          ignores: ['index', 'Index'],
        },
      ],

      'vue-scoped-css/enforce-style-type': ['error', { allows: ['scoped', 'module'] }],
    },
    overrides: [
      {
        files: ['**/main.ts'],
        rules: {
          'simple-import-sort/imports': 'off',
        },
      },
    ],
  };
};

module.exports = createESLintConfig;
