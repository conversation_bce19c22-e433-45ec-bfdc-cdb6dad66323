@primary-color: #578af2; // 全局主色
:root {
  --primary-color: @primary-color;
}
// 字体
@font-face {
  font-family: 'DingTalk JinBuTi';
  src:
    url('./fonts/DingTalk-JinBuTi.woff2') format('woff2'),
    url('./fonts/DingTalk-JinBuTi.woff') format('woff');
  font-weight: normal;
  font-style: italic;
  font-display: swap;
}
.font-DingTalk-JinBuTi {
  font-family: 'DingTalk JinBuTi', sans-serif;
}

// 去除自动填充密码时的背景并自定义样式
html,
body,
#html,
#body,
#app {
  @text-color: var(--ant-colorText);
  @bg-color: var(--ant-colorPrimaryBg);
  font-size: 14px;
  input:-webkit-autofill,
  input:-webkit-autofill:hover,
  input:-webkit-autofill:focus,
  input:-webkit-autofill:active {
    caret-color: @text-color !important;
    transition: background-color 2592000s ease-in-out 0s !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: @text-color !important;
    box-shadow: inset 9999px 9999px 9999px @bg-color !important;
    transition: all 0s !important;
  }
  .ant-input-affix-wrapper {
    overflow: hidden;
    .ant-input {
      z-index: 0;
    }
    & > * {
      z-index: 1;
    }

    input:-webkit-autofill,
    input:-webkit-autofill:hover,
    input:-webkit-autofill:focus,
    input:-webkit-autofill:active {
      outline: 32px solid @bg-color !important;
    }
  }

  * {
    word-break: break-all;
  }
}
// 一些样式重置
p {
  margin: 0;
}
a {
  text-decoration: none;
}

ul,
li {
  margin: 0;
  list-style: none;
}
ul {
  padding: 0;
}
// 自定义滚动条样式
html {
  body {
    /* 定制整个滚动条的宽度 */
    ::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    /* 定制滚动条滑块的样式 */
    ::-webkit-scrollbar-thumb {
      background-color: rgba(0, 0, 0, 0.15);
      border-radius: 9999px;
    }

    /* 定制滚动条轨道的样式 */
    ::-webkit-scrollbar-track {
      background: rgba(0, 0, 0, 0.15);
      border-radius: 0;
      border-radius: 9999px;
    }

    /* 定制滚动条滑块在hover时的样式 */
    ::-webkit-scrollbar-thumb:hover {
      background: rgba(0, 0, 0, 0.3);
    }
  }

  &.dark {
    body {
      ::-webkit-scrollbar-thumb {
        background-color: rgba(255, 255, 255, 0.3);
      }

      ::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.2);
      }

      ::-webkit-scrollbar-thumb:hover {
        background: rgba(255, 255, 255, 0.4);
      }
    }
  }
}

// 重置 nprogress 样式
#nprogress {
  .bar {
    background: var(--ant-colorPrimary);
  }
  .peg {
    box-shadow:
      0 0 10px var(--ant-colorPrimary),
      0 0 5px var(--ant-colorPrimary);
  }
  .spinner-icon {
    border-top-color: var(--ant-colorPrimary);
    border-left-color: var(--ant-colorPrimary);
  }
}

// 重置 ant-design-vue 某些样式
// .ant-modal-body {
//   overflow: hidden;
//   overflow-y: overlay;
//   padding-top: 0 !important;
//   padding-right: 24px;
//   width: calc(100% + 24px);
//   min-height: 120px;
//   max-height: calc(90vh - 132px);
// }
.ant-modal-title {
  position: relative;
  // padding-left: 34px;

  // &::before {
  //   content: '';
  //   position: absolute;
  //   top: 50%;
  //   left: -4px;
  //   display: block;
  //   width: 30px;
  //   height: 30px;
  //   margin-top: -12px;
  //   background-size: contain;
  //   background-repeat: no-repeat;
  //   background-image: url(../assets/images/card-title.png);
  // }
}

.ant-modal:not(.ant-modal-confirm) {
  .ant-modal-body {
    padding-top: 16px;
  }
}

.sortable-ghost {
  position: relative;
  z-index: 9999;
  outline: 1px solid var(--ant-colorPrimary);
}

.router-tab {
  min-height: 100%;

  .router-tab__item {
    z-index: 0;
    position: relative;
    margin-right: -1px;

    .router-tab__item-close {
      position: absolute;
      top: 50%;
      right: 14px;
      transform: translateY(-50%);
    }

    &::after,
    &::before {
      position: absolute;
      content: '';
      width: 1px;
      height: 10px;
      top: 50%;
      transform: translateY(-50%);
      background-color: var(--ant-colorBorder);
      // background-color: red;
    }

    &::after {
      right: 0;
    }
    &::before {
      left: 0;
    }

    &:hover,
    &.is-active {
      z-index: 10;
      &::after,
      &::before {
        background-color: transparent;
      }
    }
  }

  .router-tab__header {
    padding: 0 var(--ant-padding);
    border-color: var(--ant-colorBorder);
    background-color: var(--ant-colorBgContainer);
    border-bottom: 1px solid var(--ant-colorBorderSecondary);

    .router-tab__nav {
      li {
        position: relative;
        border-color: var(--ant-colorBorder);
        border: none;
        height: calc(100% - 6px);
        border-radius: 8px 8px 0px 0px;
        transform: translateY(6px);

        .router-tab__item-close {
          width: 13px;
        }
      }
      .router-tab__item.is-closable,
      .router-tab__item:hover.is-closable,
      .router-tab__item.is-active.is-closable {
        padding: 0 33px 0 20px;
      }
    }
  }
  .router-tab__container {
    padding: var(--ant-padding);
    overflow: unset;
    background: transparent;

    .router-alive {
      height: 100%;
    }
  }
  .router-tab__item:hover,
  .router-tab__item.is-active {
    color: var(--ant-colorPrimary);
    background-color: var(--ant-colorInfoBg);
  }
}
