import AntPathMatcher from '@howiefh/ant-path-matcher';
import { parse as parseContentDisposition } from '@tinyhttp/content-disposition';
import { message } from 'ant-design-vue';
import { to } from 'await-to-js';
import type { AxiosInstance, AxiosRequestConfig } from 'axios';
import CryptoJS from 'crypto-js';
import { JSEncrypt } from 'jsencrypt';
import { inflate } from 'pako';
import { forEachObj, isEmpty, merge, mergeAll } from 'remeda';
import { getQuery, parseURL, stringifyParsedURL } from 'ufo';
import { useRouter } from 'vue-router';
import WujieVue from 'wujie-vue3';

import router from '@/router';

import { lookup } from './mime';

const isAloneAuth = import.meta.env.VITE_AUTH_TYPE === 'alone';
// @ts-ignore
const isWuJie = !!(window.__POWERED_BY_WUJIE__ as boolean | undefined);
const isApaasSubApp = import.meta.env.VITE_AS_APAAS_SUB_APP === 'true';

const { bus: wujieBus } = WujieVue;

interface EncryptInfoResponse {
  enable: boolean;
  publicKey: string;
  privateKey: null;
  ignoreEnvs: string[];
  algorithm: 'AES' | 'SM4' | 'RSA';
  ignoreUrls?: string[];
  includeUrls?: string[];
  key: string;
  offSet: string;
}
interface CustomAxiosConfig extends AxiosRequestConfig {
  silent?: boolean;
}

const matcher = new AntPathMatcher();

// AES加密
export function aesEncrypt(_key: string, _iv: string, content: any) {
  const key = CryptoJS.enc.Utf8.parse(_key);
  const iv = CryptoJS.enc.Utf8.parse(_iv);
  const encryptParams = {
    iv,
    mode: CryptoJS.mode.CTR,
    padding: CryptoJS.pad.NoPadding,
  };
  const encrypt = CryptoJS.AES.encrypt(JSON.stringify(content), key, encryptParams);

  return encrypt.toString();
}
// AES解密
export function aesDecrypt(_key: string, _iv: string, content: string) {
  const key = CryptoJS.enc.Utf8.parse(_key);
  const iv = CryptoJS.enc.Utf8.parse(_iv);
  const decryptParams = {
    iv,
    mode: CryptoJS.mode.CTR,
    padding: CryptoJS.pad.NoPadding,
  };
  const decrypt = CryptoJS.AES.decrypt(content, key, decryptParams);
  return JSON.parse(decrypt.toString(CryptoJS.enc.Utf8));
}

interface ZionAxiosInterceptorOptions {
  /** Axios 实例 */
  axiosInstance: AxiosInstance;
  /** 查询单点登录页地址的异步方法 */
  asyncGetSSOLoginUrl: () => Promise<string | undefined | null>;
  /** 获取加密相关信息的异步方法 */
  asyncGetEncryptInfo?: () => Promise<EncryptInfoResponse | null | undefined>;
  /** 是否标准流程 */
  isStandardProcess?: boolean;
}

function getFileBase64(formData: FormData, fieldName: string) {
  return new Promise<{ file: string; originFileName: string }>((resolve, reject) => {
    const file = formData.get(fieldName) as File;
    const originFileName = file.name;
    if (!file) {
      reject(new Error(`Field ${fieldName} not found in FormData`));
      return;
    }

    const reader = new FileReader();
    reader.onloadend = () => {
      const arrayBuffer = reader.result as ArrayBuffer;
      const bytes = new Uint8Array(arrayBuffer);
      let binary = '';
      for (let i = 0; i < bytes.byteLength; i++) {
        binary += String.fromCharCode(bytes[i]);
      }
      const base64String = btoa(binary);
      const mime = lookup(originFileName) || 'text/plain';
      resolve({ file: `data:${mime};base64,${base64String}`, originFileName });
    };
    reader.onerror = () => {
      reject(reader.error);
    };
    reader.readAsArrayBuffer(file);
  });
}

function base64ToFile(base64: string, fileName: string): File {
  const arr = base64.split(',');
  const mime = arr[0].match(/:(.*?);/)?.[1];
  const bstr = atob(arr[1]);
  let n = bstr.length;
  const u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  return new File([inflate(u8arr)], fileName, { type: mime });
}

/**
 * 适配 ZION 的 Axios 拦截器
 */
export const enableZionAxiosInterceptor = (options: ZionAxiosInterceptorOptions) => {
  let encryptInfo: EncryptInfoResponse | null | undefined = null;
  let encryptInfoPromise: Promise<void> | null = null;

  const resetEncryptInfo = () => {
    encryptInfo = null;
    encryptInfoPromise = null;
  };

  /** 计算是否需要加密 */
  const isNeedEncrypt = (_path: string, baseURL: string) => {
    // 如果不是以 / 开头，则加上 /
    const path = _path.startsWith('/') ? _path : `/${_path}`;

    if (!encryptInfo || !encryptInfo.enable || !path) return false;

    const { ignoreUrls, includeUrls } = encryptInfo;

    // 将baseURL按/分割成数组
    const baseURLArray = baseURL.split('/').filter(Boolean);

    // 生成paths数组
    const paths = [path];
    for (let i = baseURLArray.length - 1; i >= 0; i--) {
      const currentPath = '/' + baseURLArray.slice(i).join('/') + path;
      paths.push(currentPath);
    }

    // 检查是否在ignoreUrls中
    if (ignoreUrls && ignoreUrls.length) {
      if (paths.some((p) => ignoreUrls.some((pattern) => matcher.match(pattern, p)))) return false;
    }

    // 检查是否在includeUrls中
    if (includeUrls && includeUrls.length) {
      return paths.some((p) => includeUrls.some((pattern) => matcher.match(pattern, p)));
    }

    return false;
  };

  const encrypt = (options: { data: any }) => {
    const { data } = options;

    if (!encryptInfo || !encryptInfo.enable || typeof data !== 'object') {
      return data;
    }

    if (encryptInfo.algorithm === 'AES') {
      return aesEncrypt(encryptInfo.key, encryptInfo.offSet, data);
    }

    return data;
  };
  const decrypt = (options: { data: any }) => {
    const { data } = options;

    if (!encryptInfo || !encryptInfo.enable || typeof data !== 'string') {
      return data;
    }

    if (encryptInfo.algorithm === 'AES')
      return aesDecrypt(encryptInfo.key, encryptInfo.offSet, data);

    return data;
  };

  const defaultOptions: Partial<ZionAxiosInterceptorOptions> = { isStandardProcess: true };

  const { axiosInstance, asyncGetSSOLoginUrl, asyncGetEncryptInfo, isStandardProcess } = merge(
    defaultOptions,
    options,
  );

  const asyncSetEncryptInfo = async () => {
    if (!localStorage.getItem('zion-auth-value')) {
      return false;
    }
    if (!asyncGetEncryptInfo || encryptInfo) return;

    if (!encryptInfoPromise) {
      encryptInfoPromise = (async () => {
        const [err, res] = await to(asyncGetEncryptInfo());
        if (err || !res) {
          localStorage.clear();
          sessionStorage.clear();

          const currentHref = window.location.href;
          const countDownKey = 'asyncGetEncryptInfoError';
          let countDown = 4;
          const countDownInterval = setInterval(() => {
            if (currentHref !== window.location.href) {
              clearInterval(countDownInterval);
              return;
            }

            countDown -= 1;
            if (countDown <= 0) {
              clearInterval(countDownInterval);
              localStorage.setItem('zion-encrypt-info-error', 'true');
              window.location.reload();
            }
            message.loading({
              content: `${countDown}秒后跳转回登录页`,
              key: countDownKey,
              duration: 0,
            });
          }, 1000);

          throw new Error('获取加密信息失败');
        }
        const asyncGetEncryptInfoDoneEvent = new Event('asyncGetEncryptInfoDone');
        document.dispatchEvent(asyncGetEncryptInfoDoneEvent);

        if (!res.enable) return;

        const rsaEncryptor = new JSEncrypt();
        // javascript-obfuscator(stringArray: true)
        const rsaKey =
          (() => 'MIIC')() +
          (() => 'dwIB')() +
          (() => 'ADAN')() +
          (() => 'Bgkq')() +
          (() => 'hkiG')() +
          (() => '9w0B')() +
          (() => 'AQEF')() +
          (() => 'AASC')() +
          (() => 'AmEw')() +
          (() => 'ggJd')() +
          (() => 'AgEA')() +
          (() => 'AoGB')() +
          (() => 'AJrH')() +
          (() => 'YEPI')() +
          (() => '8g/7')() +
          (() => 'LomD')() +
          (() => 'c8ew')() +
          (() => 'oG9y')() +
          (() => 'NLEl')() +
          (() => 'tSCS')() +
          (() => 'zq5M')() +
          (() => 'mxR/')() +
          (() => 'Smtf')() +
          (() => 't8JT')() +
          (() => '0jbU')() +
          (() => 'WAhD')() +
          (() => 'q3Yd')() +
          (() => 'bpYA')() +
          (() => '3/G8')() +
          (() => 'd2Ed')() +
          (() => 'tva4')() +
          (() => 'KpX0')() +
          (() => 'dLLy')() +
          (() => 'abG7')() +
          (() => 'tWCs')() +
          (() => 'YbNu')() +
          (() => 'qfb+')() +
          (() => 'rgzp')() +
          (() => 'pXoz')() +
          (() => 'fiHq')() +
          (() => 'VnNT')() +
          (() => '62e3')() +
          (() => 'Fnd4')() +
          (() => '2kMd')() +
          (() => '4Cse')() +
          (() => 'qOTM')() +
          (() => 'DsXB')() +
          (() => '1m3Q')() +
          (() => 'NOPZ')() +
          (() => '5q1a')() +
          (() => 'ZY6/')() +
          (() => 'rwsq')() +
          (() => 'rkya')() +
          (() => 'Pcer')() +
          (() => 'Ucib')() +
          (() => 'AgMB')() +
          (() => 'AAEC')() +
          (() => 'gYAM')() +
          (() => 'm1Sx')() +
          (() => '8LoW')() +
          (() => 'pyTh')() +
          (() => 'On3B')() +
          (() => 'tBc9')() +
          (() => 'PJYi')() +
          (() => 'H6oj')() +
          (() => 'HxIK')() +
          (() => 'jF7N')() +
          (() => 'Spjr')() +
          (() => 'Kf6z')() +
          (() => 'q1Qh')() +
          (() => '9yW8')() +
          (() => 'dpaO')() +
          (() => 'plMN')() +
          (() => 'kMCs')() +
          (() => '1eb7')() +
          (() => 'WApB')() +
          (() => 'Cjsd')() +
          (() => 'v62J')() +
          (() => 'rckB')() +
          (() => '+iEP')() +
          (() => 'PeP+')() +
          (() => 'UvYf')() +
          (() => 'zA6V')() +
          (() => '+6M2')() +
          (() => 'eg2v')() +
          (() => 'vtsQ')() +
          (() => 'ApbB')() +
          (() => 'fabL')() +
          (() => 'Oj7Y')() +
          (() => 'PjFv')() +
          (() => 'rpHS')() +
          (() => 'Ss4U')() +
          (() => 'q9MW')() +
          (() => 'dXv5')() +
          (() => 'BxkU')() +
          (() => 'emAc')() +
          (() => 'vGfH')() +
          (() => '/xx0')() +
          (() => 'aVRx')() +
          (() => 'O/KS')() +
          (() => 'gQJB')() +
          (() => 'AOsu')() +
          (() => 'vN9X')() +
          (() => 'zPL7')() +
          (() => 'oIDz')() +
          (() => 'GK9z')() +
          (() => 'ITIF')() +
          (() => 'pHoB')() +
          (() => 'ATM1')() +
          (() => 'GlWD')() +
          (() => '4X7R')() +
          (() => 'scYp')() +
          (() => 'q8JN')() +
          (() => '0aY/')() +
          (() => 'zx0Z')() +
          (() => 'zspe')() +
          (() => 'PPVY')() +
          (() => '775m')() +
          (() => 'eeUr')() +
          (() => 'qWDj')() +
          (() => 'z9uP')() +
          (() => '3jB3')() +
          (() => '7aEC')() +
          (() => 'QQCo')() +
          (() => 'eq41')() +
          (() => '0NHp')() +
          (() => 'SqUJ')() +
          (() => 'p0oP')() +
          (() => 'vpJN')() +
          (() => 'hjcz')() +
          (() => 'xaBH')() +
          (() => 'tpYQ')() +
          (() => '5L5n')() +
          (() => 'R0X/')() +
          (() => 'rWQ3')() +
          (() => 'WyEf')() +
          (() => 'E5GN')() +
          (() => 'cNV5')() +
          (() => 'x8Ko')() +
          (() => 'N6Dc')() +
          (() => 'U+n0')() +
          (() => 'SjJc')() +
          (() => '55Kf')() +
          (() => 'wOBZ')() +
          (() => 'YrS7')() +
          (() => 'AkEA')() +
          (() => 'ihSA')() +
          (() => '0+iq')() +
          (() => '7iaK')() +
          (() => 'j+sq')() +
          (() => 'nShN')() +
          (() => 'Xx8s')() +
          (() => '+GzK')() +
          (() => 'lZQi')() +
          (() => 'B/9M')() +
          (() => 'T7cy')() +
          (() => 'VxR0')() +
          (() => 'QbqK')() +
          (() => '0r84')() +
          (() => 'DO/w')() +
          (() => 'F6TI')() +
          (() => 'yYwU')() +
          (() => 'BogI')() +
          (() => 'HWNL')() +
          (() => 'KRXY')() +
          (() => 'HiMT')() +
          (() => '4wUm')() +
          (() => 'wQJA')() +
          (() => 'e26V')() +
          (() => '31uD')() +
          (() => 'zlUf')() +
          (() => 'oHjt')() +
          (() => 'a6eE')() +
          (() => '7EMe')() +
          (() => 'glGR')() +
          (() => 'YbjF')() +
          (() => 'LM9J')() +
          (() => 'J8Ux')() +
          (() => '5WWU')() +
          (() => '/HFJ')() +
          (() => 'TMk7')() +
          (() => 'Y9J8')() +
          (() => 's+HE')() +
          (() => 'HRXU')() +
          (() => 'Rlc8')() +
          (() => 'GfuZ')() +
          (() => '9jK6')() +
          (() => 'W0wl')() +
          (() => 'kZcd')() +
          (() => 'LQJB')() +
          (() => 'AObX')() +
          (() => '+Bgw')() +
          (() => 'TxW3')() +
          (() => '2LIK')() +
          (() => 'dAJ/')() +
          (() => 'Hiid')() +
          (() => 'aGr3')() +
          (() => 'Vofz')() +
          (() => 'NZk9')() +
          (() => '9PVe')() +
          (() => '7AEP')() +
          (() => 'ext4')() +
          (() => 'ZC6j')() +
          (() => 'dt5k')() +
          (() => 'omgI')() +
          (() => 'mnsf')() +
          (() => '603c')() +
          (() => 'v7Uu')() +
          (() => 'WMe/')() +
          (() => '1grC')() +
          (() => 'kTng')() +
          (() => 'fZI=')();
        rsaEncryptor.setPrivateKey(rsaKey);
        encryptInfo = {
          ...res,
          key: rsaEncryptor.decrypt(res.key) || '',
          offSet: rsaEncryptor.decrypt(res.offSet) || '',
        };
        const key = CryptoJS.enc.Utf8.parse(encryptInfo.key);
        const iv = CryptoJS.enc.Utf8.parse(encryptInfo.offSet);
        const decryptParams = {
          iv,
          mode: CryptoJS.mode.CTR,
          padding: CryptoJS.pad.NoPadding,
        };
        const decrypt = CryptoJS.AES.decrypt(encryptInfo.publicKey, key, decryptParams);
        encryptInfo = {
          ...encryptInfo,
          publicKey: decrypt.toString(CryptoJS.enc.Utf8),
        };
      })();
    }

    await encryptInfoPromise;
  };

  // 参数校验
  if (!axiosInstance) {
    console.error('enableZionAxiosInterceptor: 未提供 options.axiosInstance 参数');
    return { resetEncryptInfo };
  }
  if (!asyncGetSSOLoginUrl) {
    console.error('enableZionAxiosInterceptor: 未提供 options.asyncGetSSOLoginUrl 参数');
    return { resetEncryptInfo };
  }

  // 请求拦截器：按照添加的顺序，后添加的先执行
  axiosInstance.interceptors.request.use(
    async (config) => {
      const isEncryptInfoSelf = config.url?.endsWith('/encrypt/info');
      if (!isEncryptInfoSelf) {
        await asyncSetEncryptInfo();
      }

      // 补充文件下载标识给后端用
      if (config.responseType === 'blob') {
        config.headers['Is-Download'] = 'true';
        if (isNeedEncrypt(config.url ?? '', config.baseURL ?? '')) {
          config.responseType = undefined;
        }
      }

      // 纠正query参数
      const query = getQuery(config.url ?? '');
      if (!isEmpty(query)) {
        config.params = mergeAll([{}, config.params, query]);
      }
      const url = parseURL(config.url ?? '');
      url.search = '';
      config.url = stringifyParsedURL(url);

      // query 参数加密
      if (config.params) {
        if (isNeedEncrypt(config.url ?? '', config.baseURL ?? '')) {
          const encryptedData = encrypt({ data: config.params });
          forEachObj.indexed(config.params, (val, key) => {
            config.params[key] = '';
          });
          config.params.encryptReqParams = encryptedData;
        }
      }
      // body 参数加密
      if (config.data) {
        if (isNeedEncrypt(config.url ?? '', config.baseURL ?? '')) {
          if (config.data instanceof FormData) {
            const fileKey = 'file';
            const originFileNameKey = 'originFileName';
            const res = await getFileBase64(config.data, fileKey);
            config.data.set(fileKey, encrypt({ data: { data: res.file } }));
            config.data.set(originFileNameKey, encrypt({ data: { data: res.originFileName } }));
          } else {
            config.data = {
              encryptReqParams: encrypt({ data: config.data }),
            };
          }
        }
      }

      if (isApaasSubApp) {
        const token = sessionStorage.getItem('Access-Token');
        if (token) {
          config.headers['Authorization'] = token;
        }
      } else {
        const router = useRouter();
        const isZionMicroAppPathSelf = router?.currentRoute.value?.path?.startsWith('/micro-app');
        const authStorageFn = isZionMicroAppPathSelf ? sessionStorage : localStorage;
        const authKey = authStorageFn.getItem('zion-auth-key');
        const authValue = authStorageFn.getItem('zion-auth-value');
        if (authKey && authValue) {
          config.headers[authKey] = authValue;
        } else {
          console.warn('enableZionAxiosInterceptor: 未获取到登录信息');
        }

        const appKey = sessionStorage.getItem('sysAppKey') || localStorage.getItem('sysAppKey');
        if (appKey && isZionMicroAppPathSelf) {
          // 只在底座系统页面请求中需要加上appKey
          config.headers['App-Key'] = appKey;
        }
      }

      return config;
    },
    (error: any) => {
      return Promise.reject(error);
    },
  );

  // 响应拦截器：按照添加的顺序，先添加的先执行
  axiosInstance.interceptors.response.use(
    async (response) => {
      const isEncryptInfoSelf = response.config.url?.endsWith('/encrypt/info');
      if (!isEncryptInfoSelf) {
        await asyncSetEncryptInfo();
      }

      // 响应结果解密
      if (isNeedEncrypt(response.config.url ?? '', response.config.baseURL ?? '')) {
        const decryptedData = decrypt({
          data: response.data,
        });
        if (response.config.headers['Is-Download'] === 'true') {
          response.config.responseType = 'blob';
          const filename = (parseContentDisposition(response.headers['content-disposition'] || '')
            ?.parameters?.filename || '未知文件') as string;
          const mime = lookup(filename) || 'text/plain';
          response.data = base64ToFile(`data:${mime};base64,${decryptedData.data}`, filename);
        } else {
          response.data = decryptedData;
        }
      }

      if (!isStandardProcess) {
        return response;
      }

      if (response.config.responseType === 'blob') {
        return response;
      }

      if (response.data.success !== true) {
        const errMessage = response.data.errMessage ?? '请求失败';
        const silent = (response.config as CustomAxiosConfig).silent;
        !silent && message.error(errMessage);

        return Promise.reject(errMessage);
      }

      return response;
    },
    async (error: any) => {
      if (!isStandardProcess) {
        return Promise.reject(error);
      }
      const fullPath = window.location.href;
      const errMessage = error.response?.data?.errMessage;

      // 401 未登录
      if (error?.response?.status === 401) {
        const isEncryptInfoError = localStorage.getItem('zion-encrypt-info-error') === 'true';

        localStorage.clear();
        sessionStorage.clear();

        // 如果是 APaaS 子应用，通知主应用401，主应用会跳转到登录页
        if (isApaasSubApp) {
          // @ts-ignore
          window.microApp.dispatch({ type: 'logout' });
          return;
        }
        // 如果是无界模式，通知主应用401，主应用会跳转到登录页
        if (isWuJie) {
          wujieBus.$emit('redirectToLogin', fullPath);
          return Promise.reject(error);
        }
        // 如果是单体认证模式，直接跳转到登录页
        if (isAloneAuth) {
          const query: Record<string, string> = { redirect: encodeURIComponent(fullPath) };
          const { href } = router.resolve({
            name: 'login',
            query,
          });
          window.location.replace(href);
          return Promise.reject(error);
        }

        // 否则走微服务的跳转登录逻辑
        const [getSSOLoginUrlErr, ssoLoginUrl] = await to(asyncGetSSOLoginUrl());

        if (getSSOLoginUrlErr) return Promise.reject(getSSOLoginUrlErr);

        if (!ssoLoginUrl) {
          console.error('enableZionAxiosInterceptor: 获取单点登录页地址失败');
          return Promise.reject(error);
        }

        await new Promise((resolve) => setTimeout(resolve, 500));
        const __ssoLoginUrl = isEncryptInfoError
          ? `${ssoLoginUrl}${ssoLoginUrl.includes('?') ? '&' : '?'}clear=true`
          : ssoLoginUrl;
        window.location.href = __ssoLoginUrl;
        return Promise.reject(error);
      }

      message.error(errMessage ?? '请求失败');

      return Promise.reject(error);
    },
  );

  return { resetEncryptInfo };
};
