import * as echarts from 'echarts';

declare module 'echarts/types/dist/echarts' {
  interface LiquidFillSeriesOption {
    type: 'liquidFill';
    data: number[];
    radius?: string | number;
    center?: string[];
    color?: any;
    backgroundStyle?: any;
    label?: any;
    outline?: any;
  }

  interface SeriesOption {
    type?: 'liquidFill' | SeriesOption['type'];
  }
}

declare module 'echarts' {
  interface SeriesOption extends echarts.SeriesOption {
    type?: 'liquidFill' | echarts.SeriesOption['type'];
  }
}
