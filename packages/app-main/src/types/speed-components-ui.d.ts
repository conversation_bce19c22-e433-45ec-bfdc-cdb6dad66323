declare module 'speed-components-ui' {
  import { Plugin } from 'vue';

  interface SpeedComsOptions {
    transformRequestRes?: (res: any) => any;
    useLoadConfig?: {
      pageKey?: string;
      pageSizeKey?: string;
    };
  }

  const SpeedComs: Plugin & {
    install: (app: any, options?: SpeedComsOptions) => void;
  };

  export default SpeedComs;
}

declare module 'speed-components-ui/dist/style.css' {
  const style: string;
  export default style;
}
