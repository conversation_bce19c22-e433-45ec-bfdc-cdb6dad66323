<?xml version="1.0" encoding="UTF-8"?>
<svg width="40px" height="40px" viewBox="0 0 40 40" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组 33</title>
    <defs>
        <linearGradient x1="28.7558764%" y1="75.6735444%" x2="107.994127%" y2="-6.31086826%" id="linearGradient-1">
            <stop stop-color="#00C4A2" offset="0%"></stop>
            <stop stop-color="#11EFC8" offset="100%"></stop>
        </linearGradient>
        <path d="M31.3276843,24.1897761 C30.8644958,25.742188 30.3106891,26.7341761 29.6662642,27.1657406 C29.6702602,27.1716486 29.2218766,27.1760796 28.3211136,27.1790336 L28.0373133,27.1798755 C27.9877514,27.1800085 27.9370585,27.1801377 27.8852346,27.1802632 L27.5607202,27.1809722 C27.5043725,27.181083 27.446894,27.1811901 27.3882844,27.1812935 L27.0230558,27.1818695 C26.9599225,27.1819581 26.8956582,27.182043 26.830263,27.1821243 L25.9912347,27.1829219 C25.9167919,27.1829735 25.8412181,27.1830216 25.7645134,27.1830659 L22.5145738,27.1830659 C22.4107263,27.1830216 22.3057479,27.1829735 22.1996385,27.1829219 L20.8720403,27.1821243 C20.7568834,27.182043 20.6405954,27.1819581 20.5231766,27.1818695 L19.805092,27.1812935 C19.6831493,27.1811901 19.5600757,27.181083 19.4358711,27.1809722 L18.6770724,27.1802632 C18.548344,27.1801377 18.4184847,27.1800085 18.2874944,27.1798755 L17.4879815,27.1790336 C14.7777005,27.1760796 11.61504,27.1716486 8,27.1657406 L10.0177057,24.1897761 L31.3276843,24.1897761 Z M19.4331709,15.5329015 L14.726363,23.7241076 L10.3112762,23.7241076 L15.0860313,15.5329015 L19.4331709,15.5329015 Z M24.5158125,15.5329015 C23.6781711,17.1242492 23.6781711,17.9199231 24.5158125,17.9199231 C25.0797896,17.9199231 28.8753518,17.3556666 30.6834689,19.2275949 C31.5837328,20.1596305 31.8607881,21.6584681 31.5146349,23.7241076 L27.9286757,23.7241076 C28.7917933,22.4907813 28.697047,21.7175291 27.6444368,21.4043509 C26.9652876,21.2022869 23.4058231,22.1044302 21.6876102,20.0085669 C21.0957599,19.2866324 20.8423708,17.7947439 20.9274427,15.5329015 L24.5158125,15.5329015 Z M21.3816083,12 L19.5932905,15.0371197 L8.85116878,15.0371197 L10.8866029,12 L21.3816083,12 Z M32.524846,12 L30.4752754,15.0371197 L21.3816083,15.0371197 C22.2540178,13.0123732 23.0170829,12 23.6708037,12 L32.524846,12 L32.524846,12 Z" id="path-2"></path>
        <filter x="-18.3%" y="-29.6%" width="136.7%" height="159.3%" filterUnits="objectBoundingBox" id="filter-3">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="1.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.109803922 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="工作台-sm" transform="translate(-1679.000000, -1411.000000)" fill-rule="nonzero">
            <g id="编组-2" transform="translate(1402.000000, 1255.000000)">
                <g id="编组-5" transform="translate(42.000000, 64.000000)">
                    <g id="编组-17" transform="translate(215.000000, 80.000000)">
                        <g id="编组-33" transform="translate(20.000000, 12.000000)">
                            <rect id="矩形备份-6" fill="url(#linearGradient-1)" x="0" y="0" width="40" height="40"></rect>
                            <g id="形状结合">
                                <use fill="black" fill-opacity="1" filter="url(#filter-3)" xlink:href="#path-2"></use>
                                <use fill="#FFFFFF" xlink:href="#path-2"></use>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>