@import '../../share/common.less';
body {
  background: #f7f9ff url('@/assets/images/bg.png') no-repeat left top;
  background-size: cover;
}

/* 定义一些变量 */
:root {
  --zion-text-base: #090c20;
  --zion-text-base1: #565865;
  --zion-warning-bg: rgba(253, 142, 35, 0.1);
  --zion-warning-text: #f99638;
  --zion-gray-text: #a6a6a6;
  --zion-gray-text-1: #c4c5c5;
  --zion-gray-text-2: #6c6e7c;
  --zion-gray-text-3: #a7a8b0;
  --zion-black-text-1: rgba(51, 52, 53, 0.8118);
  --zion-table-header-bg: rgba(244, 246, 254, 1);
  --zion-border-color: #e8e8e8;
  --zion-border-color-1: #cacacf;
  --zion-border-color-2: #979797;
  --zion-blue-color: #3173ff;
  --zion-error-color: #e94c4c;
}
// 文本溢出工具类

.overflow-hidden-two {
  display: -webkit-box !important;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  display: -moz-box !important;
  -moz-line-clamp: 2;
  -moz-box-orient: vertical;
  overflow-wrap: break-word;
  word-break: break-all;
  white-space: normal;
  overflow: hidden;
}
.link-underline {
  text-decoration: underline;
  & > span {
    text-decoration: underline;
  }
}
// 切换视图的按钮
.modeBtn {
  background-color: #f1f4ff;
  border-radius: 2px;
  margin-left: 22px;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.65);
  padding: 3px 3px;
  .ant-checkbox-inner {
    display: none;
    opacity: 0;
  }
  .ant-checkbox-checked {
    opacity: 0;
  }
}
.ant-tooltip.error-tooltip {
  .ant-tooltip-inner {
    color: var(--zion-error-color);
  }
}
.blue-border-left {
  border-left: 4px solid var(--zion-blue-border-color);
}
.back-btn {
  .icon-font {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  &:hover .icon-font {
    transform: translateX(-3px); // 向左偏移3px，可根据需要调整
  }
}
// 提示框
.warning-alert {
  padding: 5px 10px;
  border-radius: 6px;
  background-color: var(--zion-warning-bg);
  color: var(--zion-warning-text);
}
.info-alert {
  padding: 5px 10px;
  border-radius: 6px;
  background-color: #e6f4ff;
}

.fullscreen-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
}

.transition-all-container {
  *,
  *::before,
  *::after {
    transition: all 0.1s;
  }
}
.ant-btn.link-small {
  padding: 0 4px;
}
.text-tips {
  margin-top: 4px;
  font-size: 12px;
  color: rgb(156, 163, 175);
}
.full-modal {
  .ant-modal {
    max-width: 100% !important;
    width: 100vw !important;
    top: 0;
    left: 0;
    padding-bottom: 0;
    margin: 0;
  }
  .ant-modal-content {
    display: flex;
    flex-direction: column;
    height: calc(100vh);
    width: 100%;
  }
  .ant-modal-body {
    flex: 1;
    overflow-y: auto;
  }
}
.ant-tree.hide-tree-switcher .ant-tree-switcher {
  display: none !important;
}
.ant-layout {
  // background: transparent !important;
}
.ant-menu-light .ant-menu-item-selected {
  background: #f4f6fe;
}
// 统计的表格样式复写
.stat-table {
  .ant-table-thead th {
    background-color: var(--zion-table-header-bg) !important;
  }

  .ant-table-tbody td {
    border-bottom: none !important;
    border-top: none !important;
    padding-top: 10px !important;
    padding-bottom: 10px !important;
  }
  &.stat-table-compact {
    .ant-table-tbody td {
      padding-top: 7px !important;
      padding-bottom: 7px !important;
    }
  }
  // 增加下顶部间距
  .ant-table-tbody tr:first-child td {
    padding-top: 15px !important;
  }
}
.ant-select.rounded-select {
  .ant-select-selector {
    border-radius: 20px !important;
  }
}
.vc-color-wrap {
  width: 100px !important;
  height: 30px !important;
  border-radius: 4px !important;
}
