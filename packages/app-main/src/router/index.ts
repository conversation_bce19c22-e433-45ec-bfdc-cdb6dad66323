import awaitTo from 'await-to-js';
import { createRouter, createWebHistory } from 'vue-router';

import api from '@/apis/index';
import { asyncZionVueRouterBeforeEach } from '@/share';
import NProgress from '@/share/nprogress';

import asyncRoutes from './asyncRoutes';
import constRoutes from './constRoutes';

const router = createRouter({
  history: createWebHistory(import.meta.env.VITE_BASIC_PATH),
  scrollBehavior: () => ({
    top: 0,
    left: 0,
  }),
  routes: [...constRoutes, ...asyncRoutes],
});

router.beforeEach(async (to, from, next) => {
  NProgress.start();

  await asyncZionVueRouterBeforeEach({
    to,
    next,
    loginByTicket: async ({ ticket, tenantId }) => {
      const [err, res] = await awaitTo(api.loginByTicket({ ticket, tenantId }));

      if (err || !res.data.data) {
        return;
      }

      return res.data.data;
    },
    getSsoAuthUrl: async (clientLoginUrl) => {
      const [err, res] = await awaitTo(api.getSsoAuthUrl({ clientLoginUrl }));

      if (err || !res.data.data) {
        return;
      }

      return res.data.data;
    },
  });

  next();
});

router.afterEach(() => {
  NProgress.done();
});

export default router;
