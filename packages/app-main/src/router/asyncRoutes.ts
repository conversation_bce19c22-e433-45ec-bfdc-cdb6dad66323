import { Base64 } from 'js-base64';
import { type RouteRecordRaw } from 'vue-router';

import { flatListKey } from '@/stores/menuList';

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'main-layout',
    redirect: '/home',
    component: () => import('@/layouts/index.vue'),
    children: [
      // 工作台
      {
        path: '/home',
        component: () => import('@/views/home/<USER>'),
        meta: {
          title: '工作台',
          pagePermission: { permission: 'home' },
        },
      },
      {
        path: '/home/<USER>',
        component: () => import('@/views/home/<USER>'),
        meta: {
          title: '公司管理人员',
          pagePermission: { permission: 'companyManager' },
        },
      },
      {
        path: '/home/<USER>',
        component: () => import('@/views/home/<USER>'),
        meta: {
          title: '部门经理',
          pagePermission: { permission: 'departmentManager' },
        },
      },
      {
        path: '/home/<USER>',
        component: () => import('@/views/home/<USER>'),
        meta: {
          title: '项目经理',
          pagePermission: { permission: 'projectManager' },
        },
      },
      {
        path: '/home/<USER>',
        component: () => import('@/views/home/<USER>'),
        meta: {
          title: 'SM',
          pagePermission: { permission: 'scrumMaster' },
        },
      },
      {
        path: '/home/<USER>',
        component: () => import('@/views/home/<USER>'),
        meta: {
          title: '流程经理',
          pagePermission: { permission: 'processManager' },
        },
      },
      {
        path: '/home/<USER>',
        component: () => import('@/views/home/<USER>'),
        meta: {
          title: '开发人员',
          pagePermission: { permission: 'developer' },
        },
      },
      {
        path: '/home/<USER>',
        component: () => import('@/views/home/<USER>'),
        meta: {
          title: 'AI能力应用分析',
          pagePermission: { permission: 'aiCapabilityAnalysis' },
        },
      },
      {
        path: '/home/<USER>',
        component: () => import('@/views/home/<USER>'),
        meta: {
          title: '首页',
          pagePermission: { permission: 'dashboard' },
        },
      },
      // 项目列表
      {
        path: '/project',
        component: () => import('@/views/project/index.vue'),
        children: [],
        meta: {
          title: '项目列表',
          // excludesSider: true,
          // withoutPadding: true,
          pagePermission: { permission: 'programme.grid' },
        },
      },
      // 项目详情(这里不用子路由了)
      {
        path: '/project/detail/:id/check',
        component: () => import('@/views/project/detail/index.vue'),
        meta: {
          title: '项目详情',
          pagePermission: { permission: 'project.detail' },
          menuKey: '/project',
        },
      },
      // 推进审核页
      {
        path: '/project/advance/audit',
        component: () => import('@/views/project/detail/AdvanceAudit.vue'),
        meta: {
          title: '审批详情',
          menuKey: '/approval',
        },
      },
      // 裁剪审核页
      {
        path: '/project/cropping/audit',
        component: () => import('@/views/project/detail/CroppingAudit.vue'),
        meta: {
          title: '审批详情',
          menuKey: '/approval',
        },
      },
      // 开始研发
      {
        path: '/project/:id/start-dev',
        component: () => import('@/views/project/start-dev/index.vue'),
        redirect: (to) => `/project/${to.params.id}/start-dev/step1`,
        meta: {
          menuKey: '/project',
          title: '开始研发',
        },
        children: [
          {
            path: 'step1',
            component: () => import('@/views/project/start-dev/Step1.vue'),
            meta: {
              title: '开始研发',
              menuKey: '/project',
              step: 0,
            },
          },
          {
            path: 'step2',
            component: () => import('@/views/project/start-dev/Step2.vue'),
            meta: {
              title: '开始研发',
              menuKey: '/project',
              step: 1,
            },
          },
          {
            path: 'step3',
            component: () => import('@/views/project/start-dev/Step3.vue'),
            meta: {
              menuKey: '/project',
              title: '开始研发',
              step: 2,
            },
          },
        ],
      },
      // 管控级别配置
      {
        path: '/checkpoint/level',
        component: () => import('@/views/checkpoint/level/index.vue'),
        meta: {
          title: '管控级别配置',
          pagePermission: { permission: 'checkpoint.level' },
        },
      },
      {
        path: '/checkpoint/level/add',
        component: () => import('@/views/checkpoint/level/Edit.vue'),
        meta: {
          title: '新增管控级别',
          menuKey: '/checkpoint/level',
        },
      },
      {
        path: '/checkpoint/level/edit/:id',
        component: () => import('@/views/checkpoint/level/Edit.vue'),
        meta: {
          title: '编辑管控级别',
          menuKey: '/checkpoint/level',
        },
      },
      {
        path: '/checkpoint/level/detail/:id',
        component: () => import('@/views/checkpoint/level/Edit.vue'),
        meta: {
          title: '查看管控级别',
          menuKey: '/checkpoint/level',
        },
      },
      // 管控流程配置
      {
        path: '/checkpoint/process',
        component: () => import('@/views/checkpoint/process/index.vue'),
        meta: {
          title: '管控流程配置',
          pagePermission: { permission: 'checkpoint.process' },
        },
      },

      {
        path: '/checkpoint/process/add',
        component: () => import('@/views/checkpoint/process/Edit.vue'),
        meta: {
          title: '新增管控流程',
          menuKey: '/checkpoint/process',
        },
      },
      {
        path: '/checkpoint/process/edit/:id',
        component: () => import('@/views/checkpoint/process/Edit.vue'),
        meta: {
          title: '编辑管控流程',
          menuKey: '/checkpoint/process',
        },
      },
      {
        path: '/checkpoint/process/detail/:id',
        component: () => import('@/views/checkpoint/process/Edit.vue'),
        meta: {
          title: '查看管控流程',
          menuKey: '/checkpoint/process',
        },
      },
      // 研发资源管理
      {
        path: '/resource',
        name: 'resource',
        component: () => import('@/views/resource/index.vue'),
        meta: {
          title: '研发资源',
          pagePermission: { permission: 'resource' },
        },
      },
      // 公共审批
      {
        path: '/approval',
        name: 'approval',
        component: () => import('@/views/approval/index.vue'),
        meta: {
          title: '公共审批',
        },
      },
      // 公共审批-研发资源
      {
        path: '/resource/approve/:id',
        component: () => import('@/views/resource/detail/index.vue'),
        meta: {
          title: '研发资源审批',
          menuKey: '/approval',
          tabKey: '/resource/approve',
        },
      },
    ],
  },
  // 开发空间
  {
    path: '/development-space',
    name: 'development-space',
    redirect: '/component-market',
    component: () => import('@/layouts/DevelopmentSpaceLayout.vue'),
    children: [
      // 组件市场
      {
        path: '/component-market',
        name: 'component-market',
        component: () => import('@/views/component-market/index.vue'),
        meta: {
          title: '通用能力超市',
        },
      },

      // 组件市场-增量安装
      {
        path: '/component-market/incremental-diff',
        name: 'component-market-incremental-diff',
        component: () => import('@/views/component-market/IncrementalDiff.vue'),
        meta: {
          title: '增量安装',
          excludesSider: true,
        },
      },

      // 源码导出
      {
        path: '/code-export',
        name: 'code-export',
        component: () => import('@/views/code-export/index.vue'),
        meta: {
          title: '源码导出',
        },
      },
      // 微前端-动态获取地址
      {
        path: '/micro-app/dynamic/:url',
        name: 'micro-app--dynamic',
        component: () => import('@/views/micro-app/micro-app-dynamic.vue'),
        meta: {
          keepAlive: false,
          pagePermission: { permission: 'micro.app.dynamic' },
        },
      },
    ],
  },
  {
    path: '/ai-doc',
    name: 'ai-doc',
    component: () => import('@/views/ai-doc/index.vue'),
  },
];

function areStringsEquivalent(str1: string, str2: string): boolean {
  // 定义一个辅助函数来清理字符串
  const cleanString = (s: string): string => {
    // 使用正则表达式去除首尾的空格和斜杠
    return s.replace(/^[\s/]+|[\s/]+$/g, '');
  };

  // 清理并比较两个字符串
  return cleanString(str1) === cleanString(str2);
}

function getUrlParameter(): string {
  try {
    const pathname = window.location.pathname;

    // 正则表达式匹配 /micro-app/dynamic/ 后面的所有内容
    const regex = /\/micro-app\/dynamic\/(.+)$/;

    // 尝试匹配完整路径
    const match = pathname.match(regex);

    if (match && match[1]) {
      // 如果匹配成功，返回捕获的组
      return decodeURIComponent(match[1]);
    }

    // 如果没有匹配到，可能存在 base-url，尝试匹配路径的最后部分
    const pathParts = pathname.split('/');
    const dynamicIndex = pathParts.indexOf('dynamic');

    if (dynamicIndex !== -1 && dynamicIndex < pathParts.length - 1) {
      // 如果找到 'dynamic' 并且它不是最后一个部分，返回下一个部分
      return decodeURIComponent(pathParts[dynamicIndex + 1]);
    }

    // 如果都没有匹配到，返回 undefined
    return '';
  } catch (error) {
    return '';
  }
}

function addTabMeta(routes: RouteRecordRaw[]): RouteRecordRaw[] {
  return routes.map((route) => {
    const newRoute = { ...route };

    // 如果路由没有 meta 属性，创建一个空对象
    if (!newRoute.meta) {
      newRoute.meta = {};
    }

    // 设置 title
    if (newRoute.meta.title === undefined) {
      newRoute.meta.title = () => {
        const flatList = JSON.parse(localStorage.getItem(flatListKey) || '[]') as any[];

        const titleByPath = flatList.find(
          (item) => item.path && areStringsEquivalent(item.path, route.path),
        )?.name;

        const titleByPermission = flatList.find(
          (item) => item.permission && item.permission === route.meta?.pagePermission?.permission,
        )?.name;

        const wujieTitle = flatList.find(
          (item) =>
            item.path &&
            (areStringsEquivalent(
              item.path,
              window.location.pathname.replace(
                import.meta.env.VITE_BASIC_PATH.replace(/^[\s/]+|[\s/]+$/g, ''),
                '',
              ),
            ) ||
              areStringsEquivalent(
                item.path,
                Base64.decode(decodeURIComponent(getUrlParameter())),
              )),
        )?.name;

        return titleByPath ?? wujieTitle ?? titleByPermission;
      };
    }

    // 设置 key(tab)
    if (newRoute.meta.key === undefined) {
      newRoute.meta.key = (route) => {
        if (!newRoute.meta?.tabKey) return route.fullPath;

        if (typeof newRoute.meta?.tabKey === 'string') return newRoute.meta?.tabKey;

        return newRoute.meta?.tabKey(route);
      };
    }

    // 如果有子路由，递归处理
    if (newRoute.children && newRoute.children.length > 0) {
      newRoute.children = addTabMeta(newRoute.children);
    }

    return newRoute;
  });
}

export default addTabMeta(routes);
