import { type RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  // 登录
  // {
  //   path: '/login',
  //   name: 'login',
  //   component: () => import('@/views/login/index.vue'),
  // },
  // // 天翼登录跳转
  // {
  //   path: '/ty/ssoLogin',
  //   name: 'ty',
  //   component: () => import('../../../app-auth/src/views/login/TianyiAuth.vue'),
  // },
  // // 门户定制登录页
  // {
  //   path: '/login-by-portal',
  //   name: 'login-by-portal',
  //   component: () => import('@/views/login/LoginByPortal.vue'),
  // }
];

export default routes;
