import to from 'await-to-js';
import { JSEncrypt } from 'jsencrypt';

import platformSystemPublicApi from '@/apis/platform/systemPublic';

let publicKey: string | null = null;

export const asyncRSAEncode = async (text: string) => {
  if (!publicKey) {
    const [err, res] = await to(platformSystemPublicApi.encodeByPublicKey());

    if (err || !res.data.data) {
      const msg = '获取加密公钥失败';
      console.error(msg);
      return Promise.reject(msg);
    }

    publicKey = res.data.data;
  }

  const rsaEncryptor = new JSEncrypt();
  rsaEncryptor.setPublicKey(publicKey);

  return rsaEncryptor.encrypt(text);
};

export default {};
