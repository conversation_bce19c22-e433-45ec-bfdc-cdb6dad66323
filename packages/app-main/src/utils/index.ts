import dayjs from 'dayjs';
import { size, snakeCase, uniq } from 'lodash-es';

// 兼容的url打开（微服务和单体）
import router from '../router';

import 'dayjs/locale/zh-cn';

/**
 * 格式化时间
 * @param {*} value
 * @param {*} type
 */
export function formatTime(
  time: number | string | Date = new Date(),
  type = 'YYYY-MM-DD HH:mm:ss',
) {
  const date = new Date(time);
  return time ? dayjs(date).format(type) : '';
}
/**
 * 获取前N天的日期
 * @param days 天数
 * @param type 格式
 * @returns
 */
export function getDateOfAgo(days: number, type = 'YYYY-MM-DD HH:mm:ss') {
  return dayjs().subtract(days, 'day').startOf('day').format(type);
}
export function isImageFile(filename: string) {
  // Define a comprehensive regular expression to match image file extensions
  const imageExtensions = /\.(jpg|jpeg|png|gif|bmp|svg|webp|tiff|tif|ico|heic)$/i;

  // Test the filename against the regular expression
  return imageExtensions.test(filename);
}

/**
 * 校验浏览器 URL 地址的合法性
 * @param url 要校验的 URL 字符串
 * @returns {boolean} 是否为合法 URL
 */
export function isValidUrl(url: string): boolean {
  const urlPattern =
    /^(https?:\/\/)([a-zA-Z0-9.-]+)(:[0-9]+)?(\/[a-zA-Z0-9\-._~:/?#[\]@!$&'()*+,;=]*)?$/;
  return urlPattern.test(url);
}

// 获取 query 的 URL 参数
export const getQueryParams = (search: string): Record<string, string> => {
  const params = new URLSearchParams(search);
  const queryParams: Record<string, string> = {};

  for (const [key, value] of params.entries()) {
    queryParams[key] = value;
  }

  return queryParams;
};
export const queryParamsToString = (queryParams: Record<string, any>): string => {
  return Object.keys(queryParams)
    .map((key) => `${encodeURIComponent(key)}=${encodeURIComponent(queryParams[key])}`)
    .join('&');
};

/**
 * 从props配置中获取默认值
 * @param props
 * @param overrideProps
 * @returns
 */

export function getDefaultFromProps<T = Record<string, any>>(
  props: Record<string, any>,
  overrideProps: T,
): T | Record<string, any> {
  const defaults = Object.entries(props).reduce((temp: any, [key, value]) => {
    temp[key] = value?.default;
    return temp;
  }, {});
  return {
    ...defaults,
    ...overrideProps,
  };
}

/**
 * 生成id
 * @param length
 * @returns
 */
export const generateId = (prefix = '', length = 10) => {
  const str = generateRandomStr(length);
  return `${prefix}${str}`;
};
/**
 * 生成指定长度的随机英文字母串
 * @param length
 * @returns
 */
export const generateRandomStr = (length = 10) => {
  const chars = 'abcdefghijklmnopqrstuvwxyz';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(getSecureRandomNumber(chars.length));
  }
  return result;
};
/**
 * 将下划线格式转换成大驼峰格式
 * @param str
 * @returns
 */
export const toCamelCase = (str: string) => {
  return str.replace(/_([a-z])/g, (g) => g[1].toUpperCase());
};
/**
 * 将大驼峰格式转换成小驼峰格式
 * @param str
 * @returns
 */
export const toSmallCamelCase = (str: string) => {
  if (!str) return '';
  return str.charAt(0).toLowerCase() + str.slice(1);
};
/**
 * 下载文件
 * @param content
 * @param filename
 */
export function downloadFile(content: BlobPart, filename: string) {
  const a = document.createElement('a');
  const blob = content instanceof Blob ? content : new Blob([content]);
  const url = window.URL.createObjectURL(blob);
  a.href = url;
  a.download = filename;
  a.click();
  window.URL.revokeObjectURL(url);
}
// 列举一部分关键字
export const javaSysKeyWordsRge =
  /\b(abstract|assert|boolean|break|byte|case|catch|char|class|const|continue|default|do|double|else|enum|extends|final|finally|float|for|if|implements|import|instanceof|int|interface|long|native|new|package|private|protected|public|return|short|static|strictfp|super|switch|synchronized|this|throw|throws|transient|try|void|volatile|while)\b/;
export const jsSysKeyWordsRge =
  /\b(break|case|catch|class|const|continue|debugger|default|delete|do|else|export|extends|finally|for|function|if|import|in|instanceof|new|return|super|switch|this|throw|try|typeof|var|void|while|with|yield)\b/;
export const camelToSnakeCase = (input: string) => snakeCase(input);

// 安全随机数生成器
export function getSecureRandomNumber(max: number): number {
  const array = new Uint32Array(1);
  window.crypto.getRandomValues(array);
  return array[0] % max;
}
// 获取
// 获取随机数
export function getRandomId() {
  return Number(getSecureRandomNumber(10000).toString().substr(3, 4) + Date.now()).toString(36);
}
export const getEditorNodeId = (prefix = 'canvas_node_') => {
  return prefix + getRandomId();
};
// 判断数字是否为空，这里会过滤0,参数请传入数字
export const isNumEmpty = (num?: number | string): num is undefined => {
  return num === undefined || num === null || num === '';
};

// 交换两个元素的位置
export const swapArr = (arr: any[], index1: number, index2: number) => {
  arr[index1] = arr.splice(index2, 1, arr[index1])[0];
  return arr;
};

export const hasDuplicates = (array: string[]) => {
  return size(array) !== size(uniq(array));
};
// 将首字母转成大写

export const capitalizeFirstLetter = (str: string) => {
  return str.charAt(0).toUpperCase() + str.slice(1);
};
// 转换字符串
export const dataTransform2Str = (info: any) => {
  return info instanceof Object ? JSON.stringify(info) : info;
};
export const strTransform2Data = (str: string) => {
  try {
    const result = JSON.parse(str);
    return result;
  } catch (error) {
    return str;
  }
};

declare global {
  interface Window {
    __POWERED_BY_WUJIE__?: boolean;
    $wujie?: {
      props?: {
        jump: (routeInfo: any, openBlank: boolean) => void;
      };
    };
  }
}

export const openUrl = (routeInfo: any, openBlank = true) => {
  const isWuJie = window.__POWERED_BY_WUJIE__;
  const parentProps = window.$wujie?.props;
  console.log(parentProps);
  if (isWuJie && parentProps?.jump) {
    parentProps.jump(routeInfo, openBlank);
  } else {
    if (openBlank) {
      const { href } = router.resolve(routeInfo);
      window.open(href, '_blank');
    } else {
      router.push(routeInfo);
    }
  }
};
// 去除json 多余空格
export const removeSpacesInJson = (jsonStr: any) => {
  if (typeof jsonStr === 'string') {
    // 使用正则表达式匹配括号、逗号和冒号周围的空格
    const regex = /(\{|\[|:|,)\s+|(\s+)(\}|\]|:)/g;
    // 使用 replace() 方法替换所有匹配到的空格
    return jsonStr.replace(regex, '$1$3');
  }
  return jsonStr;
};
// 根据path设置对象的值
export const setJsonValue = (jsonObj: any, jsonPath: string, value: any) => {
  const pathArray = jsonPath.split('.');
  let currentObj = jsonObj;
  // let parentObj;
  let key;

  while (pathArray.length > 1) {
    key = pathArray.shift();
    if (currentObj[key] === undefined) {
      currentObj[key] = {};
    }
    // parentObj = currentObj;
    currentObj = currentObj[key];
  }

  key = pathArray.shift();
  if (currentObj && key in currentObj) {
    currentObj[key] = value;
  } else {
    throw new Error('Cannot set value at path ' + jsonPath);
  }
};

// export const setValueByPath = (object: any, prop: string, v: any) => {
//   prop = prop || '';
//   const paths = prop.split('.');
//   let current = object;
//   for (let i = 0, j = paths.length; i < j; i++) {
//     const path = paths[i];
//     if (!current) break;
//     if (i === j - 1) {
//       current[path] = v;
//       break;
//     }
//     current = current[path];
//   }
//   return object;
// };
export const getValueByPath = (object: any, prop: string) => {
  prop = prop || '';
  const paths = prop.split('.');
  let current = object;
  for (let i = 0, j = paths.length; i < j; i++) {
    const path = paths[i];
    if (!current) break;
    current = current[path];
    if (i === j - 1) {
      break;
    }
  }
  return current;
};

/** *
 *
 * 获取请求的UUID，指定长度和进制,如
 * getUUID(8, 2)   //"01001010" 8 character (base=2)
 * getUUID(8, 10) // "47473046" 8 character ID (base=10)
 * getUUID(8, 16) // "098F4D35"。 8 character ID (base=16)
 *
 */
export function getUUID(len: number = 8, radix?: number) {
  const chars = '0123456789abcdefghijklmnopqrstuvwxyz'.split('');
  const uuid = [];
  let i;
  radix = radix || chars.length;
  if (len) {
    for (i = 0; i < len; i++) {
      const randomIndex = getSecureRandomNumber(radix);
      uuid[i] = chars[randomIndex];
    }
  } else {
    let r;
    uuid[8] = uuid[13] = uuid[18] = uuid[23] = '-';
    uuid[14] = '4';
    for (i = 0; i < 36; i++) {
      if (!uuid[i]) {
        r = 0 | (getSecureRandomNumber(10000) * 16);
        uuid[i] = chars[i === 19 ? (r & 0x3) | 0x8 : r];
      }
    }
  }
  return uuid.join('');
}

// 部分md提取方法 TODO:Markdown 解析库（如 marked.js 或 showdown.js）来准确解析
export const extractJsCodeBlocks = (expression: string) => {
  const regex = /```javascript\n([\s\S]*?)```/g; // 简易点
  const matches = [];
  let match;
  while ((match = regex.exec(expression)) !== null) {
    if (match[1]) {
      matches.push(match[1]);
    }
  }

  return matches;
};

export const extractGroovyCodeBlocks = (markdownText: string) => {
  const regex =
    /(?:^|\n)\`\`\` *[gG][rR][oO][oO][vV][yY](?:-[a-zA-Z]*)? *(?:\n|$)([\s\S]*)(?:^|\n)\`\`\`(?:\n|$)/g;
  const matches = [];
  let match;

  while ((match = regex.exec(markdownText)) !== null) {
    matches.push(match[1].trim());
  }

  return matches;
};
// 将 Base64 转换为 Blob
export function base64ToBlob(base64: string, mime: string) {
  const byteCharacters = atob(base64);
  const byteNumbers = new Array(byteCharacters.length);
  for (let i = 0; i < byteCharacters.length; i++) {
    byteNumbers[i] = byteCharacters.charCodeAt(i);
  }
  const byteArray = new Uint8Array(byteNumbers);
  return new Blob([byteArray], { type: mime });
}
// 将模板中的{xxx}替换为data[xxx]
export function renderTemplate(template: string, data: Record<string, string>) {
  return template.replace(/{(\w+)}/g, (match, key) => data[key] || match);
}
