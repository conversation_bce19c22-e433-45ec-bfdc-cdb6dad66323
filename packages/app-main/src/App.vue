<script setup lang="ts">
  import { theme } from 'ant-design-vue';
  import antdChinese from 'ant-design-vue/es/locale/zh_CN';
  import { getPopupContainer } from 'app-main/src/utils/get-popup-container';
  import { locale as dayjsLocale } from 'dayjs';
  import dayjsChinese from 'dayjs/locale/zh-cn';
  import { computed } from 'vue';

  import AntTokenAsCssVariables from '@/components/AntTokenAsCssVariables/index.vue';

  import { useCurrentDark } from './stores/currentDark';

  dayjsLocale(dayjsChinese);

  const currentDark = useCurrentDark();

  const antdThemeAlgorithm = computed(() => [
    currentDark.isDark ? theme.darkAlgorithm : theme.defaultAlgorithm,
  ]);
</script>

<template>
  <AConfigProvider
    :locale="antdChinese"
    :theme="{ algorithm: antdThemeAlgorithm }"
    :get-popup-container="getPopupContainer"
  >
    <AntTokenAsCssVariables />
    <RouterView />
  </AConfigProvider>
</template>
