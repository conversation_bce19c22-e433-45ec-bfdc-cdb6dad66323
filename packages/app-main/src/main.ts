// import RouterTab from '@fantage9/vue-router-tab';
// import Antd from 'ant-design-vue';
import type {} from 'ant-design-vue/typings/global.d.ts';
import { createPinia } from 'pinia';
import { createApp } from 'vue';
import SpeedComs from 'speed-components-ui/components';
import 'speed-components-ui/dist/style.css';
import * as echarts from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { BarChart } from 'echarts/charts';
import { CardPane, TitleCard0, TitleCard1 } from '@/components/Layout';
import {
  TooltipComponent,
  GridComponent,
  TitleComponent,
  LegendComponent,
} from 'echarts/components';

import App from './App.vue';
import router from './router';
import 'virtual:uno.css';
import 'ant-design-vue/dist/reset.css';
import '@fantage9/vue-router-tab/dist/vue-router-tab.css';
import '@/assets/styles/global.less';
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate';

const app = createApp(App);
const pinia = createPinia();
pinia.use(piniaPluginPersistedstate);
// app.use(Antd);
app.use(SpeedComs, {
  transformRequestRes: (res: any) => {
    return res.data;
  },
  useLoadConfig: {
    pageKey: 'pageIndex',
    pageSizeKey: 'pageSize',
  },
});
app.use(pinia);
app.use(router);
// 注册一些全局用到的组件
app.component('CardPane', CardPane);
app.component('TitleCard0', TitleCard0);
app.component('TitleCard1', TitleCard1);

echarts.use([
  CanvasRenderer,
  BarChart,
  TooltipComponent,
  GridComponent,
  TitleComponent,
  LegendComponent,
]);
// app.use(RouterTab);

app.mount('#app');
