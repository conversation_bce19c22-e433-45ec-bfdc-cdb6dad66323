<script setup lang="ts">
  import { DownOutlined, ReloadOutlined, SearchOutlined, UpOutlined } from '@ant-design/icons-vue';
  import { useElementSize } from '@vueuse/core';
  import { type FormInstance, message } from 'ant-design-vue';
  import to from 'await-to-js';
  import { clone, equals } from 'remeda';
  import { computed, h, nextTick, onMounted, ref } from 'vue';

  import MainListCard from '../MainListCard/MainListCard.vue';

  const props = withDefaults(
    defineProps<{
      /** 过滤表单 model 值 */
      filterFormState?: Record<string, any>;
      /** 是否查询数据中 */
      loading: boolean;
      /** 是否显示 border */
      bordered?: boolean;
    }>(),
    {
      filterFormState: undefined,
      bordered: false,
    },
  );
  const slots = defineSlots<{
    /** 过滤表单 */
    filterForm?: void;
    /** 表格操作 */
    tableActions?: void;
    /** 表格 */
    table?: void;
    /** 分页 */
    pagination?: void;
  }>();
  const emit = defineEmits<{
    /** 查询 */
    query: [];
    /** 重置 */
    reset: [];
  }>();

  /** 过滤表单 ref */
  const filterFormRef = ref<FormInstance>();
  /** 上次起作用的过滤条件 */
  const lastFilterFormState = ref(clone(props.filterFormState));
  /** 过滤条件是否存在变更 */
  const filterFormStateChange = computed(
    () => !equals(lastFilterFormState.value, clone(props.filterFormState)),
  );
  /** 过滤表单容器 ref */
  const filterFormContainerRef = ref<HTMLElement>();
  /** 过滤表单容器高度 */
  const { height: filterFormContainerHeight } = useElementSize(filterFormContainerRef);
  /** 是否展开过滤表单 */
  const isExpandFilterForm = ref(false);
  /** 是否展示过滤表单展开按钮 */
  const showExpandFilterFormButton = computed(() => filterFormContainerHeight.value > 54 + 24);
  /** 自动计算过滤表单 label 宽度 */
  const autoFilterFormLabelWidth = ref<number>(0);
  /** 计算过滤表单 label 宽度 */
  const calcFilterFormLabelWidth = async () => {
    await nextTick();

    const labels = filterFormContainerRef.value?.querySelectorAll('.ant-form-item-label');

    if (!labels?.length) return;
    autoFilterFormLabelWidth.value = Math.max(
      ...Array.from(labels).map((label) => label.clientWidth),
    );
  };
  onMounted(calcFilterFormLabelWidth);
  /** 提交表单 */
  const submitForm = async (reset = false) => {
    console.log(filterFormRef.value, ';filterFormRef.value');
    if (!filterFormRef.value) {
      message.error('查询表单未初始化，请稍后重试');
      return;
    }

    // 校验表单
    const [validError] = await to(filterFormRef.value.validate());

    if (validError) {
      return;
    }

    if (reset) {
      filterFormRef.value.resetFields();
      emit('reset');
    }

    await nextTick();
    emit('query');
    lastFilterFormState.value = clone(props.filterFormState);
  };
</script>

<template>
  <div class="table-list-container space-y-[22px]">
    <ACard
      v-if="slots.filterForm"
      :bordered="props.bordered"
      :body-style="{ padding: 0 }"
      style="border-radius: 8px 8px 0 0"
    >
      <div
        class="filter-form"
        :class="{
          'filter-form-inexpanded': !isExpandFilterForm,
          'disable-action': props.loading,
        }"
      >
        <div ref="filterFormContainerRef">
          <AForm
            ref="filterFormRef"
            autocomplete="off"
            :model="props.filterFormState"
            :label-col="
              autoFilterFormLabelWidth
                ? { style: { width: `${autoFilterFormLabelWidth}px` } }
                : undefined
            "
            @finish="submitForm()"
          >
            <MainListCard
              :cols-width="360"
              :gap-x="12"
              :gap-y="0"
              :class="{ 'pr-[240px]': !isExpandFilterForm }"
            >
              <slot name="filterForm" />
            </MainListCard>

            <div
              class="filter-form-actions flex items-center justify-end space-x-2"
              :class="{ 'filter-form-actions-inexpanded': !isExpandFilterForm }"
            >
              <div v-show="showExpandFilterFormButton">
                <a @click="isExpandFilterForm = !isExpandFilterForm">
                  <span>
                    {{ isExpandFilterForm ? '收起' : '展开' }}
                    <span>
                      <UpOutlined v-if="isExpandFilterForm" />
                      <DownOutlined v-else />
                    </span>
                  </span>
                </a>
              </div>
              <div class="space-x-2">
                <AButton
                  :icon="h(ReloadOutlined)"
                  @click="submitForm(true)"
                >
                  重置
                </AButton>
                <AButton
                  type="primary"
                  html-type="submit"
                  :icon="h(SearchOutlined)"
                >
                  查询
                </AButton>
                <!-- <ABadge
                  :dot="filterFormStateChange"
                  title="查询条件已变更，点击重新查询"
                >
                  <AButton
                    type="primary"
                    html-type="submit"
                    :icon="h(SearchOutlined)"
                  >
                    查询
                  </AButton>
                </ABadge> -->
              </div>
            </div>
          </AForm>
        </div>
      </div>
    </ACard>

    <ACard
      :bordered="props.bordered"
      :body-style="{ padding: 0 }"
      style="margin-top: 0; border-radius: 0 0 8px 8px; border-top: 1px solid #eee"
    >
      <div class="p-[22px] space-y-4">
        <div
          v-if="slots.tableActions"
          class="flex justify-end space-x-2"
        >
          <slot name="tableActions" />
        </div>
        <ASpin
          v-if="!!slots.table || !!slots.pagination"
          :spinning="props.loading"
        >
          <div class="space-y-4">
            <div v-if="slots.table"><slot name="table" /></div>
            <div v-if="slots.pagination">
              <slot name="pagination" />
            </div>
          </div>
        </ASpin>
      </div>
    </ACard>
  </div>
</template>

<style lang="less" scoped>
  // 禁止操作
  .disable-action {
    opacity: 0.5;
    pointer-events: none;
  }

  .table-list-container:deep(.filter-form) {
    @interval: 22px;

    position: relative;
    padding: @interval;
    overflow: hidden;
    min-height: 32px + @interval * 2;

    &.filter-form-inexpanded {
      max-height: 32px + @interval * 2;
    }

    .ant-form-item {
      .ant-picker {
        width: 100%;
      }
    }

    .filter-form-actions {
      margin-top: @interval;

      &.filter-form-actions-inexpanded {
        position: absolute;
        right: @interval;
        bottom: @interval;
        margin-top: 0;
      }
    }
  }
</style>
