<script lang="tsx" setup>
  import { computed, useAttrs } from 'vue';

  import type { ConfirmProps } from './index.vue';
  import ConfirmButton from './index.vue';

  interface DeleteProps extends ConfirmProps {
    title?: string;
    text?: string;
    danger?: boolean;
    actionTitle?: string;
    // type: 'primary' | 'ghost' | 'dashed' | 'link' | 'text' | 'default';
    before?: () => boolean;
  }
  const props = withDefaults(defineProps<DeleteProps>(), {
    title: '是否确定删除已选数据？',
    actionTitle: '',
    danger: true,
    text: '删除后选择数据将彻底删除，请知悉',
  });
  const tipsText = computed(() =>
    props.actionTitle ? `删除后[ ${props.actionTitle} ]将彻底删除,请知悉` : props.text,
  );
  const emit = defineEmits(['confirm']);
</script>

<template>
  <ConfirmButton
    :before="before"
    :title="title"
    :text="tipsText"
    v-bind="{ ...$props, ...$attrs }"
    @confirm="emit('confirm')"
  >
    <slot>
      <AButton
        v-bind="{ ...$props, ...$attrs }"
        :danger="danger"
        size="small"
      >
        删除
      </AButton>
    </slot>
  </ConfirmButton>
</template>
