<script setup lang="ts">
import { computed, ref, onMounted, onBeforeMount, watch, onUnmounted, nextTick } from 'vue';
// import { projectStageOptions, ProjectStage } from '@/dicts/project';
import { DoubleRightOutlined, AimOutlined } from '@ant-design/icons-vue';
import { stageStepMap } from '@/dicts/project';
import { AntIconRender } from '@/components/AntIconRender/AntIconRender.tsx';
import { projectStageOptions, defaultStepConfig } from '@/dicts/project';
import StepDownArrow from '@/assets/images/step/step_down_arrow.png';
import StepDynamicArrowDown from '../StepDynamicArrowDown/index.vue';
import type { AgileStageItem } from '@/apis/checkpoint/agileStage';
interface Props {
  stage?: string; // 当前阶段
  canPush?: boolean; // 是否显示推进按钮
  showDownIcon?: boolean; // 是否显示向下箭头
  steps: StepItem[];
  itemWidth: number;
  mode?: 'edit' | 'check';
}
export interface StepItem {
  title: string;
  value: string; // stage是不固定的
  treeData: AgileStageItem[];
  stageIcon?: string;
}

const props = withDefaults(defineProps<Props>(), {
  stage: undefined,
  canPush: false,
  showDownIcon: true,
  steps: () => [],
  itemWidth: 200,
  mode: 'check',
});

const emit = defineEmits<{
  (e: 'push'): void;
}>();

// 获取当前步骤索引
const currentIndex = computed(() => {
  return props.steps.findIndex((step) => step.value === props.stage);
});

// 获取阶段项的一些样式配置（这里按照5的倍数获取）
const getStepStyleConfig = (index: number) => {
  if (props.mode === 'edit') {
    return defaultStepConfig;
  }
  // 如果 currentIndex 是 -1，先显示成默认的项

  if (currentIndex.value === -1) {
    return projectStageOptions[index % 5].step;
  }
  // 如果当前索引大于进行中的索引，说明是未开始的步骤
  if (index > currentIndex.value) {
    return defaultStepConfig;
  }
  // 否则返回对应的阶段配置
  return projectStageOptions[index % 5].step;
};
// 获取阶段tag配置
const getStepStageTagConfig = (index: number) => {
  
  let status = props.mode === 'check' ? 
    index === currentIndex.value
      ? 'processing'
      : index < currentIndex.value
        ? 'finished'
        : 'default' : 'default';
  const config = stageStepMap[status];
  return config;
};

// 获取连接线颜色
const getLineColor = (index: number) => {
  if (index < currentIndex.value) {
    return '#85A2FE'; // 返回固定选中
  }
  return '#979797';
};

const hoveredIndex = ref(-1);

const handleStepHover = (index: number) => {
  if (props.canPush && index === currentIndex.value) {
    hoveredIndex.value = index;
  }
};

const handleStepLeave = () => {
  hoveredIndex.value = -1;
};

const handlePush = () => {
  emit('push');
};

const stepRefs = ref<(HTMLElement | null)[]>([]);
const lineStyles = ref<Record<number, { width: string; left: string; borderColor: string }>>({});
const stepsAreaRef = ref<HTMLElement | null>(null);

// 初始化ref数组
const initStepRefs = () => {
  stepRefs.value = new Array(props.steps.length).fill(null);
};

// 计算所有连接线样式
const updateLineStyles = async (retryCount = 0) => {
  // 等待下一个 tick，确保 DOM 已经渲染
  await nextTick();

  // 检查是否所有 ref 都已经有值
  const allRefsReady = stepRefs.value.every((ref) => ref !== null);
  console.log('allRefsReady:', allRefsReady);

  if (!allRefsReady && retryCount < 5) {
    // 如果还有空的 ref，等待一段时间后重试
    setTimeout(() => {
      updateLineStyles(retryCount + 1);
    }, 100);
    return;
  }

  // 检查元素是否有有效的尺寸
  const hasValidSize = stepRefs.value.every((ref) => {
    if (!ref) return false;
    const rect = ref.getBoundingClientRect();
    return rect.width > 0 && rect.height > 0;
  });

  console.log('hasValidSize:', hasValidSize);

  if (!hasValidSize && retryCount < 10) {
    // 如果元素尺寸无效，等待更长时间后重试
    setTimeout(() => {
      updateLineStyles(retryCount + 1);
    }, 200);
    return;
  }

  props.steps.forEach((_, index) => {
    if (index < props.steps.length - 1) {
      const currentEl = stepRefs.value[index];
      const nextEl = stepRefs.value[index + 1];

      if (currentEl && nextEl) {
        const currentRect = currentEl.getBoundingClientRect();
        const nextRect = nextEl.getBoundingClientRect();
        console.log('currentRect:', currentRect, 'nextRect:', nextRect);

        // 如果尺寸仍然为 0，跳过这次计算
        if (currentRect.width === 0 || nextRect.width === 0) {
          console.log('元素尺寸为 0，跳过计算');
          return;
        }

        // 计算两个节点中心点之间的距离
        const currentCenter = currentRect.left + currentRect.width / 2;
        const nextCenter = nextRect.left + nextRect.width / 2;
        const width = nextCenter - currentCenter;
        const reduceWidth = 150; // 保持间距
        lineStyles.value[index] = {
          width: `${width - reduceWidth}px`,
          left: `${currentRect.width / 2 + reduceWidth / 2}px`,
          borderColor: getLineColor(index),
        };
      }
    }
  });
};

// 包装函数，用于 watch 和事件监听器
const updateLineStylesWrapper = () => {
  updateLineStyles();
};

// 监听窗口大小改变，只处理宽度变化
let lastWidth = window.innerWidth;
const handleResize = () => {
  const currentWidth = window.innerWidth;
  if (currentWidth !== lastWidth) {
    lastWidth = currentWidth;
    updateLineStylesWrapper();
  }
};

// 监听状态变化时更新样式
watch(() => props.steps, updateLineStylesWrapper, { deep: true });

// 监听当前阶段变化，自动滚动到当前阶段
watch(
  () => props.stage,
  () => {
    nextTick(() => {
      scrollToCurrentStage();
      // updateLineStyles(); // 更新下颜色
    });
  },
);

onMounted(() => {
  // updateLineStyles();
  // 初始化时滚动到当前阶段
  nextTick(() => {
    scrollToCurrentStage();
  });
});
onBeforeMount(() => {
  initStepRefs();
});
// 窗口大小改变时重新计算
// window.addEventListener('resize', handleResize);
// onUnmounted(() => {
//   window.removeEventListener('resize', handleResize);
// });

// 滚动到当前阶段
const scrollToCurrentStage = () => {
  // 如果 currentIndex 是 -1，滚动到第一个步骤
  const targetIndex = currentIndex.value >= 0 ? currentIndex.value : 0;

  if (stepRefs.value[targetIndex] && stepsAreaRef.value) {
    const targetStepEl = stepRefs.value[targetIndex];
    const containerEl = stepsAreaRef.value;

    if (targetStepEl && containerEl) {
      const stepRect = targetStepEl.getBoundingClientRect();
      const containerRect = containerEl.getBoundingClientRect();

      // 计算需要滚动的距离，让目标阶段居中显示
      const scrollLeft =
        targetStepEl.offsetLeft - containerEl.clientWidth / 2 + targetStepEl.clientWidth / 2;

      // 平滑滚动到目标位置
      containerEl.scrollTo({
        left: Math.max(0, scrollLeft),
        behavior: 'smooth',
      });
    }
  }
};
</script>
<template>

  <div class="project-steps">
    <!-- 步骤区域 -->
    <div class="steps-area flex overflow-x-auto" ref="stepsAreaRef">
      <template v-for="(step, index) in steps" :key="step.value">
        <!-- 步骤项 -->
        <div :class="['step-item flex flex-col flex-shrink-0', index === currentIndex ? 'active' : '']"
          :style="{ width: itemWidth + 'px' }" :ref="(el) => (stepRefs[index] = el as HTMLElement)">

          <a-flex align="center" :class="['step-item-title']" :style="{
            background: getStepStyleConfig(index).title.background,
            borderColor: getStepStyleConfig(index).borderColor,
          }">
            <span class="text-[30px] font-bold title" :style="getStepStyleConfig(index).seqTextStyle">{{ index
              + 1 }}</span>
            <!-- <div class="step-icon" :style="{
                backgroundColor: getStepStyleConfig(index).color,
              }">
                <AntIconRender class="text-[#fff]" v-if="step.stageIcon" :name="step.stageIcon" />
              </div> -->
            <div class="step-text truncate" :title="step.title" :style="getStepStyleConfig(index).titleTextStyle">
              {{ step.title }}
            </div>
            <span class="step-tag" v-if="mode === 'check'" :style="{ background: getStepStageTagConfig(index).bgColor }">{{
              getStepStageTagConfig(index).text || '' }}</span>
          </a-flex>

          <div class=" px-4 step-body"
            :style="{ background: getStepStyleConfig(index).body.background, borderWidth: '1px', borderColor: getStepStyleConfig(index).borderColor }">
            <div v-if="showDownIcon" class="text-center">
              <StepDynamicArrowDown :colors="getStepStyleConfig(index).arrowDownColors" />
            </div>
            <div class="h-[516px] overflow-y-auto">
              <slot name="bto-item-render" :item="step" :index="index"></slot>
            </div>
          </div>
        </div>
        <!-- 连接线和推进按钮 -->
        <div v-if="index < steps.length - 1" class="step-line-container">
          <div class="step-line"></div>
          <!-- 推进按钮 -->
          <a-button shape="round" ghost v-if="canPush && currentIndex === index" type="primary" size="small"
            class="push-btn absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 transition-transform duration-200 hover:scale-110"
            @click="handlePush">
            推进
            <DoubleRightOutlined />
          </a-button>
        </div>
      </template>
    </div>

    <!-- 带有当前阶段的支持显示滚动到当前阶段按钮 -->
    <!-- <div v-if="steps.length > 1" class="scroll-to-current-btn">
      <a-button type="primary" size="small" @click="scrollToCurrentStage" class="scroll-btn">
        <AimOutlined />
        {{ currentIndex >= 0 ? '定位到当前阶段' : '定位到第一个阶段' }}
      </a-button>
    </div> -->
  </div>
</template>

<style scoped lang="less">
.project-steps {
  padding-left: 20px;
  padding-right: 20px;
  background: #fff;
  border-radius: 8px;

  .steps-area {
    // margin-bottom: 20px;
    display: flex;
    overflow-x: auto;
    overflow-y: hidden;
    scrollbar-width: thin;
    scrollbar-color: #d1d5db transparent;

    &::-webkit-scrollbar {
      height: 6px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #d1d5db;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background-color: #9ca3af;
    }
  }

  .step-item {
    position: relative;
    // min-width: 80px;
    // flex: 1;
    display: flex;
    flex-direction: column;

    .step-top-out-wrapper {
      position: relative;
      width: 100%;
      display: flex;
      justify-content: center;
    }

    .step-item-title {
      border-radius: 10px 10px 0px 0px;
      padding: 7px;
      position: relative;
      border-style: solid;
      border-width: 1px;
      height: 43px;
      display: flex;
      align-items: center;

      .title {

        font-size: 30px;
        font-weight: bold;

      }

      .step-icon {
        width: 22px;
        height: 22px;
        flex-shrink: 0;
        display: flex;
        align-items: center;
        border-radius: 50%;
        justify-content: center;
      }

      .step-text {
        letter-spacing: normal;
        color: #4b5071;
        font-size: 16px;
        font-weight: bold;
        margin-left: 10px;
      }

      .step-tag {
        border-radius: 10px;
        box-sizing: border-box;
        border: 1px solid #FFFFFF;
        color: #fff;
        font-size: 12px;
        padding: 1px 6px;
        margin-left: 5px;
        flex-shrink: 0;
      }
    }

    .step-body {
      border-style: dashed;
      border-top-color: transparent !important;
    }

    &.active {
      .step-body {
        border-style: solid;
      }
    }

  }

  .step-line-container {
    position: relative;
    top: 20px;
    width: 100px;
    margin: 0 10px;
    flex-shrink: 0;
    align-self: flex-start; // 顶部对齐，防止高度撑满

    .step-line {
      height: 1px;
      width: 100%;
      border-bottom: 1px dashed rgba(151, 151, 151, 1);
    }
  }

  :deep(.ant-btn-primary.push-btn) {
    background-color: #fff;
    z-index: 10;
    width: 80px;
    height: 32px;
  }

  .steps-content {
    min-height: 20px;
  }

  .scroll-to-current-btn {
    text-align: center;
    margin-top: 16px;

    .scroll-btn {
      border-radius: 20px;
      font-size: 12px;
      height: 32px;
      padding: 0 16px;
    }
  }
}
</style>
