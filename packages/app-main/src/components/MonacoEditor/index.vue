<script setup lang="ts">
  // TODO: 适配 ant hover、active、error in FormItem 等状态的颜色
  import {
    CheckCircleFilled,
    CompressOutlined,
    CopyOutlined,
    ExpandOutlined,
  } from '@ant-design/icons-vue';
  import { Editor as MonacoEditor, loader } from '@guolao/vue-monaco-editor';
  import { useToggle, useVModel } from '@vueuse/core';
  import { theme } from 'ant-design-vue';
  import * as monaco from 'monaco-editor';
  import * as R from 'remeda';
  import { computed, ref } from 'vue';

  import { useCurrentDark } from '@/stores/currentDark';

  loader.config({ monaco });

  type Opionts = Omit<monaco.editor.IStandaloneEditorConstructionOptions, 'language'>;
  type Language =
    | 'plaintext'
    | 'abap'
    | 'apex'
    | 'azcli'
    | 'bat'
    | 'bicep'
    | 'cameligo'
    | 'clojure'
    | 'coffeescript'
    | 'c'
    | 'cpp'
    | 'csharp'
    | 'csp'
    | 'css'
    | 'cypher'
    | 'dart'
    | 'dockerfile'
    | 'ecl'
    | 'elixir'
    | 'flow9'
    | 'fsharp'
    | 'freemarker2'
    | 'freemarker2.tag-angle.interpolation-dollar'
    | 'freemarker2.tag-bracket.interpolation-dollar'
    | 'freemarker2.tag-angle.interpolation-bracket'
    | 'freemarker2.tag-bracket.interpolation-bracket'
    | 'freemarker2.tag-auto.interpolation-dollar'
    | 'freemarker2.tag-auto.interpolation-bracket'
    | 'go'
    | 'graphql'
    | 'handlebars'
    | 'hcl'
    | 'html'
    | 'ini'
    | 'java'
    | 'javascript'
    | 'julia'
    | 'kotlin'
    | 'less'
    | 'lexon'
    | 'lua'
    | 'liquid'
    | 'm3'
    | 'markdown'
    | 'mdx'
    | 'mips'
    | 'msdax'
    | 'mysql'
    | 'objective-c'
    | 'pascal'
    | 'pascaligo'
    | 'perl'
    | 'pgsql'
    | 'php'
    | 'pla'
    | 'postiats'
    | 'powerquery'
    | 'powershell'
    | 'proto'
    | 'pug'
    | 'python'
    | 'qsharp'
    | 'r'
    | 'razor'
    | 'redis'
    | 'redshift'
    | 'restructuredtext'
    | 'ruby'
    | 'rust'
    | 'sb'
    | 'scala'
    | 'scheme'
    | 'scss'
    | 'shell'
    | 'sol'
    | 'aes'
    | 'sparql'
    | 'sql'
    | 'st'
    | 'swift'
    | 'systemverilog'
    | 'verilog'
    | 'tcl'
    | 'twig'
    | 'typescript'
    | 'vb'
    | 'wgsl'
    | 'xml'
    | 'yaml'
    | 'json';

  const { token } = theme.useToken();

  const defaultOptions: Opionts = {
    tabSize: 2,
    automaticLayout: true,
    formatOnType: true,
    formatOnPaste: true,
    minimap: {
      renderCharacters: false,
      showSlider: 'always',
      size: 'proportional',
    },
  };

  const props = withDefaults(
    defineProps<{
      value?: string;
      options?: Opionts;
      height?: string;
      language: Language | 'vue';
      autoHeight?: boolean;
    }>(),
    {
      value: '',
      options: () => ({}),
      height: '512px',
      language: 'plaintext',
      autoHeight: false,
    },
  );

  const slots = defineSlots<{
    hilightLanguage?: void;
  }>();

  /** 计算实际的 language */
  const innerLanguage = computed(() => {
    if (props.language === 'vue') {
      return 'html';
    }

    return props.language;
  });
  /** 计算实际的 height */
  const realHeight = computed(() => {
    if (isFullscreen.value) {
      return '100%';
    }

    if (props.autoHeight) {
      const lineCount = props.value?.split(/\r\n|\n|\r/).length;

      return `${Math.max(lineCount * 18 + 64, 128)}px`;
    }

    return props.height;
  });

  const allOptions = computed(() => R.merge(defaultOptions, props.options));

  const currentDark = useCurrentDark();

  const valueModel = useVModel(props, 'value');

  const isFullscreen = ref(false);
  const toggleFullscreen = useToggle(isFullscreen);
</script>

<template>
  <div
    class="relative"
    :class="{
      'fullscreen-container': isFullscreen,
    }"
    :style="{ height: realHeight }"
  >
    <div class="absolute left-0 top-0 h-full w-full">
      <div
        class="h-full flex flex-col border border-[var(--ant-colorBorder)] border-solid bg-[var(--ant-colorBgContainer)]"
        :style="{ borderRadius: isFullscreen ? 0 : `${token.borderRadius}px` }"
      >
        <div
          class="flex items-center justify-between border-b border-[var(--ant-colorBorder)] border-b-solid p-1 pl-3"
        >
          <div
            v-if="slots.hilightLanguage"
            class="min-w-0 flex-1"
          >
            <slot name="hilightLanguage" />
          </div>
          <div
            v-else
            class="min-w-0 flex-1 text-[var(--ant-colorTextTertiary)]"
          >
            编辑器当前配置为
            <ATypographyText keyboard>{{ props.language }}</ATypographyText>
            语法
          </div>

          <div class="flex items-center">
            <ATypographyParagraph
              class="!mb-0"
              :copyable="{ text: props.value, tooltip: false }"
            >
              <template #copyableIcon="{ copied }">
                <AButton
                  type="text"
                  size="small"
                  :disabled="!props.value"
                  class="text-[var(--ant-colorTextTertiary)]"
                >
                  <template #icon>
                    <CheckCircleFilled
                      v-if="copied"
                      class="text-[var(--ant-colorSuccess)]"
                    />
                    <CopyOutlined v-else />
                  </template>
                  {{ copied ? '已复制' : '复制代码' }}
                </AButton>
              </template>
            </ATypographyParagraph>
            <AButton
              type="text"
              size="small"
              class="text-[var(--ant-colorTextTertiary)]"
              @click="toggleFullscreen()"
            >
              <template #icon>
                <CompressOutlined v-if="isFullscreen" />
                <ExpandOutlined v-else />
              </template>
              {{ isFullscreen ? '退出全屏' : '全屏' }}
            </AButton>
          </div>
        </div>
        <div class="min-h-0 flex-1 py-[var(--ant-borderRadius)]">
          <MonacoEditor
            v-model:value="valueModel"
            :theme="currentDark.isDark ? 'vs-dark' : 'vs'"
            :options="allOptions"
            :language="innerLanguage"
          ></MonacoEditor>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
  :deep(.monaco-editor) {
    --vscode-editor-background: var(--ant-colorBgContainer);
    --vscode-editorGutter-background: var(--ant-colorBgContainer);

    .minimap {
      border-left: 1px solid var(--ant-colorBorder);
    }

    .view-overlays .current-line {
      border-width: 1px;
    }
  }
</style>
