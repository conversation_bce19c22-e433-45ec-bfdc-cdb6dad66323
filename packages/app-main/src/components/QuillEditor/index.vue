<script setup lang="ts">
  import { QuillEditor } from '@vueup/vue-quill';
  import { useVModel } from '@vueuse/core';
  import { computed, ref, toRefs, watch } from 'vue';

  import '@vueup/vue-quill/dist/vue-quill.snow.css';
  import '@vueup/vue-quill/dist/vue-quill.bubble.css';

  const props = defineProps<{
    value?: string;
    readonly?: boolean;
    placeholder?: string;
    height?: number;
  }>();

  const editorKey = ref(0);
  watch(
    () => [props.readonly],
    () => (editorKey.value += 1),
  );

  const valueModel = useVModel(props, 'value');
  const handleUpdateContent = (content: string) => {
    valueModel.value = content === '<p><br></p>' ? '' : content;
  };

  const editorHeight = computed(() => ({
    editorMinHeight: props.height ? 'unset' : '200px',
    editorMaxHeight: props.height ? 'unset' : '80vh',
    editorConstantHeight: props.height ? `${props.height}px` : 'unset',
  }));

  const { editorMinHeight, editorMaxHeight, editorConstantHeight } = toRefs(editorHeight.value);
</script>

<template>
  <div>
    <QuillEditor
      :key="editorKey"
      :content="valueModel"
      :read-only="props.readonly"
      :placeholder="props.placeholder"
      theme="snow"
      content-type="html"
      @update:content="handleUpdateContent"
    ></QuillEditor>
  </div>
</template>

<style lang="less" scoped>
  :deep(.ql-editor) {
    min-height: v-bind(editorMinHeight);
    max-height: v-bind(editorMaxHeight);
    height: v-bind(editorConstantHeight);
  }

  :deep(.ql-toolbar) {
    border-radius: var(--ant-borderRadius) var(--ant-borderRadius) 0 0;
  }

  :deep(.ql-container) {
    border-radius: 0 0 var(--ant-borderRadius) var(--ant-borderRadius);
  }
</style>
