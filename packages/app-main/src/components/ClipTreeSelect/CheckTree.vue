<template>
    <ATree class="check-tree-wrapper" :selectable="false" :tree-data="treeData" default-expand-all
        :checkable="mode === 'edit'" :checked-keys="checkedKeys" @check="handleTreeCheck"
        :fieldNames="{ title: 'itemName', key: 'id' }" :disabled="treeConfig?.checkable === false">
        <!-- 判断下,区分编辑态还是查看态 -->
        <template #title="{ dataRef }">
            <!-- 查看态 -->
            <template v-if="mode === 'check'">
                <!-- <a-badge color="#D8D8D8" /> -->
                <span class="mr-[5px]" v-if="showLinksAndStatus">
                    <template v-if="dataRef.checkResult === 0 || dataRef.checkResult === 2">
                        <a-tooltip :title="dataRef.checkResultMsg" overlayClassName="error-tooltip" color="#fff"
                            v-if="dataRef.checkResultMsg">
                            <CloseCircleFilled v-if="dataRef.checkResult === 0" :style="{ color: '#F53F3F' }" />
                            <ExclamationCircleOutlined v-else-if="dataRef.checkResult === 2"
                                :style="{ color: '#FF8000' }" />
                        </a-tooltip>
                        <template v-else>
                            <CloseCircleFilled v-if="dataRef.checkResult === 0" :style="{ color: '#D8D8D8' }" />
                            <ExclamationCircleOutlined v-else-if="dataRef.checkResult === 2"
                                :style="{ color: '#FF8000' }" />
                        </template>
                    </template>
                    <CheckCircleFilled v-else :style="{ color: '#52C41A' }" />
                </span>
                <span>{{ dataRef.itemName }}</span>
                <!-- 检查项指引:未开始的节点不显示 -->
                <a-space
                    :size="3"
                    class="ml-[5px]"
                    v-if="showLinksAndStatus && showLinkBtn && dataRef.itemGuideInfo && safeParseJSON(dataRef.itemGuideInfo).length > 0"
                    wrap>
                    <template v-for="(link, index) in safeParseJSON(dataRef.itemGuideInfo)" :key="index">
                        <template v-if="link?.btn_name">
                            <a-tooltip title="此项目暂未关联研发云" overlayClassName="error-tooltip" color="#fff"
                                v-if="!canIclick(link)">
                                <AButton type="link" class="p-0 link-underline" disabled>
                                    {{ link?.btn_name }}
                                </AButton>
                            </a-tooltip>
                            <AButton type="link" class="p-0 text-[12px] link-underline" v-else @click="handleLinkClick({ ...link, itemId: dataRef.id })">
                                {{ link?.btn_name }}
                            </AButton>
                        </template>
                    </template>
                </a-space>
            </template>
            <!-- 编辑态 -->
            <template v-else>
                <span>{{ dataRef.itemName }}</span>
            </template>
        </template>
    </ATree>
</template>

<script setup lang="ts">
import { Tree, Space, Button, Badge, Tooltip, message } from 'ant-design-vue';
import { CheckCircleFilled, CloseCircleFilled, ExclamationCircleOutlined } from '@ant-design/icons-vue';
import type { TreeProps } from 'ant-design-vue';
import { renderTemplate } from '@/utils';
import { useRouter } from 'vue-router';
const router = useRouter();

// 定义 props
interface Props {
    treeData: any[];
    mode?: 'edit' | 'check';
    checkedKeys?: string[];
    srdcloudProjectId?: string;
    treeConfig?: {
        checkable?: boolean;
    };
    showLinksAndStatus?: boolean; // 是否显示链接和检查状态
    programmeId?: string; // 传入的项目 ID
    showLinkBtn?: boolean; // 是否显示链接按钮
}

// 定义 emits
interface Emits {
    (e: 'check', checkedKeys: any, info: any): void;
}

const props = withDefaults(defineProps<Props>(), {
    treeData: () => [],
    mode: 'check',
    checkedKeys: () => [],
    treeConfig: () => ({
        checkable: true,
    }),
    showLinksAndStatus: true, // 默认显示
    programmeId: '', // 默认空字符串
    showLinkBtn: true, // 默认显示链接按钮
});

const emit = defineEmits<Emits>();

// 安全解析 JSON
const safeParseJSON = (jsonString: string) => {
    try {
        return JSON.parse(jsonString);
    } catch (error) {
        console.warn('JSON 解析失败:', jsonString, error);
        return [];
    }
};

// TODO:研发云跳转
    const handleLinkClick = (link: { btn_name: string; btn_url: string; itemId: string } | string) => {
    if (typeof link === 'string') {
        return;
    }

    // 校验是否是有效链接
    if (!link.btn_url || typeof link.btn_url !== 'string') {
        message.warning('无效的链接地址');
        return;
    }
    // 正则校验 vueRouter-path 协议，校验是否是AI-DOC文档编辑器
    const aiDocPathRegex = /^\/ai-doc\?(?:itemId=[^&]+&programmeId=[^&]+|programmeId=[^&]+&itemId=[^&]+)$/;
    if (aiDocPathRegex.test(link.btn_url)) {
        const { href } = router.resolve({
            path: link.btn_url,
            query: { itemId: link.itemId, programmeId: props.programmeId }
        });
        return window.open(href, '_blank');
    }
    // 正则校验 URL 协议
    const urlRegex = /^(https?|ftp|file):\/\/.+/i;
    if (!urlRegex.test(link.btn_url)) {
        message.warning('无效的 URL 协议');
        return;
    }
    if (!canIclick(link)) {
        message.warning('此项目暂未关联研发云');
        return;
    }
    // 正则替换 {projectId} 为固定的 srdProjectId
    const processedUrl = renderTemplate(link.btn_url, { projectId: props.srdcloudProjectId as string });

    window.open(processedUrl, '_blank');
};
const canIclick = (link: { btn_name: string; btn_url: string } | string) => {
    if (typeof link === 'string') {
        return true;
    }
    return !link.btn_url.includes('{projectId}') || (link.btn_url.includes('{projectId}') && props.srdcloudProjectId);   // 判断是否是研发云链接
};
const handleTreeCheck: TreeProps['onCheck'] = (checkedKeys, e) => {
    emit('check', checkedKeys, e);
};
</script>

<style lang="less">
.check-tree-wrapper.ant-tree {
    background-color: transparent;

    // 这里隐藏左侧占位
    .ant-tree-switcher {
        display: none;
    }
    .ant-tree-title {
        font-size: 12px;
    }

    .ant-tree-checkbox {
        align-self: flex-start !important;
    }
    .ant-tree-node-content-wrapper:hover {
        background-color: transparent;
    }
    &.not-start {
        .ant-tree-title {
            color: #4B5071;
        }
    }
}
</style>
