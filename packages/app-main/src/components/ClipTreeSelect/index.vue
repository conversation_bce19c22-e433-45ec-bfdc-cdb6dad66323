<script setup lang="ts">
  import type { TreeProps } from 'ant-design-vue';

  // import { ProjectStage } from '@/dicts/project';
  import { Form } from 'ant-design-vue';
import { ref, watch } from 'vue';

  import {
  type AgileStage,
  getAgileStageListAll,
  getProjectClipQualityCheckResults,
  getProjectQualityCheckResults,
} from '@/apis/checkpoint/agileStage';

  import ProjectSteps, { type StepItem } from '../ProjectSteps/index.vue';
import CheckTree from './CheckTree.vue';

  // 定义 props
  interface Props {
    // 在这里定义 props
    value?: string[];
    mode: 'edit' | 'check';
    srdcloudProjectId?: string; // 研发云关联项目id
    programmeId?: string; // 项目id
    needInitCheckedForAll?: boolean; // 是否需要初始化选中
    itemWidth?: number;
    stepConfig?: {
      stage?: string;
      canPush: boolean;
    };
    treeConfig?: {
      checkable?: boolean;
    };
    sprintId?: string; // 迭代id
  }

  // 定义 emits
  interface Emits {
    (e: 'update:value', value: string[]): void;
    (e: 'push'): void;
    (e: 'getAgileStageData', data: StepItem[]): void; // 对外暴露获取全量阶段数据
  }

  // 使用 withDefaults 设置默认值
  const props = withDefaults(defineProps<Props>(), {
    // 默认值
    value: () => [],
    mode: 'check',
    stepConfig: () => ({
      status: undefined,
      canPush: false,
    }),
    itemWidth: 200,
    needInitCheckedForAll: false,
    treeConfig: () => ({
      checkable: true,
    }),
  });
  const formItemContext = Form.useInjectFormItemContext();
  interface StageTreeNode {
    title: string;
    key: string;
    status?: 'error' | 'success' | 'warning';
    links?: { name: string; path: string }[];
    children?: StageTreeNode[];
  }
  // 符合当前阶段的选中key,否则tree回报警告
  const getCurrentStageCheckedKeys = (stage: string) => {
    const curStageFullKeys =
      steps.value.find((s) => s.value === stage)?.treeData.map((t) => t.id) ?? [];
    return allCheckedKeys.value.filter((key: string) => {
      return curStageFullKeys.includes(key);
    });
  };
  // 步骤配置
  const steps = ref<StepItem[]>([]);
  const currentStageIndex = ref(0);
  // 当前阶段管控项
  const getCurrentTreeData = (status: string) => {
    const treeData =
      steps.value && steps.value.length > 0
        ? (steps.value.find((s) => s.value === status)?.treeData ?? [])
        : [];

    // 为每个节点添加key属性以符合DataNode类型
    return treeData.map((item) => ({
      ...item,
      key: item.id || `key-${item.itemCode}`,
    }));
  };
  const emit = defineEmits<Emits>();

  const allCheckedKeys = ref<string[]>([]);
  const loading = ref(false);

  const handleTreeCheck: TreeProps['onCheck'] = (_, e) => {
    const { checked, node } = e;
    const nodeKey = node.key as string;

    if (checked) {
      // 选中：添加到数组
      if (!allCheckedKeys.value.includes(nodeKey)) {
        allCheckedKeys.value.push(nodeKey);
      }
    } else {
      // 取消选中：从数组移除
      const index = allCheckedKeys.value.indexOf(nodeKey);
      if (index > -1) {
        allCheckedKeys.value.splice(index, 1);
      }
    }

    emit('update:value', allCheckedKeys.value);
    formItemContext?.onFieldChange();
  };

  // 判断某个阶段是否全部选中
  const isStageAllChecked = (stage: string) => {
    const stageTreeData = getCurrentTreeData(stage);
    const stageCheckedKeys = getCurrentStageCheckedKeys(stage);
    return stageTreeData.length > 0 && stageTreeData.length === stageCheckedKeys.length;
  };

  // 判断某个阶段是否部分选中（半选状态）
  const isStageIndeterminate = (stage: string) => {
    const stageTreeData = getCurrentTreeData(stage);
    const stageCheckedKeys = getCurrentStageCheckedKeys(stage);
    return stageCheckedKeys.length > 0 && stageCheckedKeys.length < stageTreeData.length;
  };

  // 处理阶段全选/取消全选
  const handleStageSelectAll = (checked: boolean, stage: string) => {
    const stageTreeData = getCurrentTreeData(stage);
    const stageKeys = stageTreeData.map((item) => item.key as string);

    if (checked) {
      // 全选：添加所有该阶段的key
      stageKeys.forEach((key) => {
        if (!allCheckedKeys.value.includes(key)) {
          allCheckedKeys.value.push(key);
        }
      });
    } else {
      // 取消全选：移除所有该阶段的key
      stageKeys.forEach((key) => {
        const index = allCheckedKeys.value.indexOf(key);
        if (index > -1) {
          allCheckedKeys.value.splice(index, 1);
        }
      });
    }

    emit('update:value', allCheckedKeys.value);
    formItemContext?.onFieldChange();
  };

  // 监听 props.value 变化，同步到内部状态
  watch(
    () => props.value,
    (newValue) => {
      allCheckedKeys.value = newValue;
    },
    { immediate: true },
  );

  const initAgilestageByProId = async () => {
    // 判断是否是裁剪操作(edit是裁剪)
    loading.value = true;

    try {
      const res =
        props.mode === 'check'
          ? await getProjectQualityCheckResults(props.programmeId!, props.sprintId)
          : await getProjectClipQualityCheckResults(props.programmeId!);

      // 初始化steps
      // Mock 数据用于测试滚动效果
      // steps.value = [
      //   {
      //     title: '需求分析阶段',
      //     id: '1',
      //     value: ProjectStage.DEMAND,
      //     treeData: [
      //       {
      //         id: '1-1',
      //         itemCode: 'REQ_001',
      //         itemName: '需求文档编写',
      //         itemOrder: 1,
      //         itemRules: '必须包含功能需求和非功能需求',
      //         stageId: 1,
      //         checkResult: 1,
      //         checkResultMsg: '需求文档完整',
      //         itemGuideInfo: JSON.stringify([
      //           { btn_name: '查看模板', btn_url: 'https://example.com/template1' },
      //         ]),
      //       },
      //     ],
      //   },
      //   {
      //     title: '系统设计阶段',
      //     id: '2',
      //     value: ProjectStage.DESIGN,
      //     treeData: [
      //       {
      //         id: '2-1',
      //         itemCode: 'DES_001',
      //         itemName: '架构设计',
      //         itemOrder: 1,
      //         itemRules: '必须包含系统架构图和数据库设计',
      //         stageId: 2,
      //         checkResult: 1,
      //         checkResultMsg: '架构设计合理',
      //         itemGuideInfo: JSON.stringify([
      //           { btn_name: '架构图', btn_url: 'https://example.com/arch1' },
      //         ]),
      //       },
      //     ],
      //   },
      //   {
      //     title: '开发实现阶段',
      //     id: '3',
      //     value: ProjectStage.DEV,
      //     treeData: [
      //       {
      //         id: '3-1',
      //         itemCode: 'DEV_001',
      //         itemName: '代码开发',
      //         itemOrder: 1,
      //         itemRules: '代码必须通过代码规范检查',
      //         stageId: 3,
      //         checkResult: 0,
      //         checkResultMsg: '存在代码规范问题',
      //         itemGuideInfo: JSON.stringify([
      //           { btn_name: '代码检查', btn_url: 'https://example.com/code1' },
      //         ]),
      //       },
      //     ],
      //   },
      //   {
      //     title: '测试验证阶段',
      //     id: '4',
      //     value: ProjectStage.TEST,
      //     treeData: [
      //       {
      //         id: '4-1',
      //         itemCode: 'TEST_001',
      //         itemName: '功能测试',
      //         itemOrder: 1,
      //         itemRules: '所有功能点必须测试通过',
      //         stageId: 4,
      //         checkResult: 1,
      //         checkResultMsg: '功能测试通过',
      //         itemGuideInfo: JSON.stringify([
      //           { btn_name: '测试用例', btn_url: 'https://example.com/case1' },
      //         ]),
      //       },
      //     ],
      //   },
      //   {
      //     title: '发布上线阶段',
      //     id: '5',
      //     value: ProjectStage.PUBLISH,
      //     treeData: [
      //       {
      //         id: '5-1',
      //         itemCode: 'PUB_001',
      //         itemName: '部署配置',
      //         itemOrder: 1,
      //         itemRules: '必须提供部署文档和配置说明',
      //         stageId: 5,
      //         checkResult: 1,
      //         checkResultMsg: '部署配置完整',
      //         itemGuideInfo: JSON.stringify([
      //           { btn_name: '部署文档', btn_url: 'https://example.com/deploy1' },
      //         ]),
      //       },
      //     ],
      //   },
      //   {
      //     title: '运维监控阶段',
      //     id: '6',
      //     value: 'operation_stage',
      //     treeData: [
      //       {
      //         id: '6-1',
      //         itemCode: 'OP_001',
      //         itemName: '系统监控',
      //         itemOrder: 1,
      //         itemRules: '必须配置系统监控和告警',
      //         stageId: 6,
      //         checkResult: 1,
      //         checkResultMsg: '监控配置完成',
      //         itemGuideInfo: JSON.stringify([
      //           { btn_name: '监控面板', btn_url: 'https://example.com/monitor1' },
      //         ]),
      //       },
      //     ],
      //   },
      //   {
      //     title: '项目总结阶段',
      //     id: '7',
      //     value: 'summary_stage',
      //     treeData: [
      //       {
      //         id: '7-1',
      //         itemCode: 'SUM_001',
      //         itemName: '项目总结',
      //         itemOrder: 1,
      //         itemRules: '必须完成项目总结报告',
      //         stageId: 7,
      //         checkResult: 0,
      //         checkResultMsg: '总结报告待完成',
      //         itemGuideInfo: JSON.stringify([
      //           { btn_name: '总结模板', btn_url: 'https://example.com/summary1' },
      //         ]),
      //       },
      //     ],
      //   },
      // ];

      // emit('getAgileStageData', steps.value);
      // return;

      if (res.data && res.data.success && res.data.data) {
        const agileData = res.data.data;
        // 按照 stageOrder 排序
        const sortedAgileData = agileData.sort(
          (a: AgileStage, b: AgileStage) => a.stageOrder - b.stageOrder,
        );
        steps.value = sortedAgileData.map((item: AgileStage) => ({
          title: item.stageName,
          value: item.id || '',
          croppingCheckedItems: item.croppingCheckedItems || [],
          stageIcon: item.stageIcon,
          treeData: item.agileStageItemVOList || [],
        }));
        currentStageIndex.value = steps.value.findIndex(
          (item) => item.value === props.stepConfig?.stage,
        ); // 获取当前激活阶段索引
        // 对外更新选中项
        const croppingCheckedItems = steps.value
          .map((step: any) => step.croppingCheckedItems || [])
          .flat();

        if (croppingCheckedItems.length > 0 && props.needInitCheckedForAll) {
          allCheckedKeys.value = croppingCheckedItems;
          emit('update:value', croppingCheckedItems);
        }
        emit('getAgileStageData', steps.value);
      }
    } catch (error) {
      console.error('加载数据失败:', error);
    } finally {
      loading.value = false;
    }
  };
  const initAgilestageList = async () => {
    // 这里查询的是启用的
    loading.value = true;
    try {
      // fix:这里stageStatus搜索参数好像没生效
      const res = await getAgileStageListAll({ stageStatus: 1 });

      if (res.data && res.data.success && res.data.data) {
        const agileData = res.data.data;
        // 按照 stageOrder 排序
        const sortedAgileData = agileData.sort(
          (a: AgileStage, b: AgileStage) => a.stageOrder - b.stageOrder,
        );
        // 这里没有stageCode了
        steps.value = sortedAgileData.map((item: AgileStage) => ({
          title: item.stageName,
          value: item.id || '',
          stageIcon: item.stageIcon,
          treeData: item.agileStageItemVOList || [],
        }));
      }
    } catch (error) {
      console.error('加载数据失败:', error);
    } finally {
      loading.value = false;
    }
  };
  watch(
    () => props.sprintId,
    (newValue) => {
      initAgilestageByProId();
    },
  );
  // 根据项目id获取
  if (props.programmeId) {
    initAgilestageByProId();
  } else {
    // 管控配置调用，这里获取阶段配置（启用的）
    initAgilestageList();
  }
  // 监听stepConfig.stage变化，更新当前阶段索引
  watch(
    () => props.stepConfig?.stage,
    (newValue) => {
      currentStageIndex.value = steps.value.findIndex((item) => item.value === newValue);
    },
  );
</script>

<template>
  <!-- 由于antd showline 子节点会默认加一个前缀图标，没有合适的方案改(除非css)，这里关闭了showline -->

  <!-- Loading 骨架屏 -->
  <div
    v-if="loading"
    class="skeleton-loading"
  >
    <div class="skeleton-steps flex">
      <div
        v-for="i in 5"
        :key="i"
        class="skeleton-step mx-6 flex-1"
      >
        <div class="skeleton-step-header mb-4 h-12 animate-pulse rounded-lg bg-gray-200"></div>
        <div class="skeleton-step-content">
          <div class="skeleton-tree-item mb-2 h-8 animate-pulse rounded bg-gray-100"></div>
          <div class="skeleton-tree-item mb-2 h-8 animate-pulse rounded bg-gray-100"></div>
          <div class="skeleton-tree-item h-6 animate-pulse rounded bg-gray-100"></div>
        </div>
      </div>
    </div>
  </div>

  <!-- 实际内容 -->
  <div v-else>
    <ProjectSteps
      v-if="steps.length > 0"
      :item-width="itemWidth"
      :stage="stepConfig?.stage"
      :can-push="stepConfig?.canPush"
      :steps="steps"
      :mode="mode"
      @push="emit('push')"
    >
      <template #bto-item-render="{ item, index }: { item: StepItem; index: number }">
        <div
          v-if="getCurrentTreeData(item.value)?.length > 0"
          :class="['flex flex-col mt-2']"
        >
          <div
            v-if="mode === 'edit'"
            class="mb-2 w-full"
          >
            <ACheckbox
              :disabled="!treeConfig?.checkable"
              :checked="isStageAllChecked(item.value)"
              :indeterminate="isStageIndeterminate(item.value)"
              @change="(e) => handleStageSelectAll(e.target.checked, item.value)"
            >
              全选
            </ACheckbox>
          </div>
          <CheckTree
            :class="[mode === 'check' && index > currentStageIndex && 'not-start']"
            :tree-data="getCurrentTreeData(item.value)"
            :mode="mode"
            :programme-id="programmeId"
            :checked-keys="getCurrentStageCheckedKeys(item.value)"
            :srdcloud-project-id="srdcloudProjectId"
            :tree-config="treeConfig"
            :show-links-and-status="index <= currentStageIndex"
            @check="handleTreeCheck"
          />
        </div>
        <p
          v-else
          class="mt-4 text-center text-[var(--ant-colorTextSecondary)]"
        >
          暂无质量管控规范检查结果
        </p>
      </template>
    </ProjectSteps>
    <AEmpty
      v-else
      description="暂无研发流程配置"
    />
  </div>
</template>

<style scoped lang="less"></style>
