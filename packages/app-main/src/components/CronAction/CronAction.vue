<!-- eslint-disable import/newline-after-import -->
<script setup lang="ts">
  import { EditOutlined } from '@ant-design/icons-vue';
  import { useAsyncState, useVModel } from '@vueuse/core';
  import { message } from 'ant-design-vue';
  import to from 'await-to-js';
  import { h, ref, watch } from 'vue';
  // @ts-ignore
  import Vue3Cron from 'vue3-cron-antd';

  import taskApi from '@/apis/task/index';

  const props = defineProps<{
    value?: string;
    disabled?: boolean;
    placeholder?: string;
  }>();

  const emit = defineEmits<{
    changeByCronEditor: [];
  }>();

  const isModalOpen = ref(false);
  const valueModel = ref(useVModel(props, 'value'));
  const defaultVue3CronModelValue = '* * * * * ?';
  const vue3CronModelValue = ref(defaultVue3CronModelValue);

  const {
    state: checkCronState,
    execute: checkCronExecute,
    isLoading: checkCronLoading,
  } = useAsyncState(
    () => (valueModel.value ? taskApi.checkCron(valueModel.value) : Promise.resolve(null)),
    null,
    { immediate: false },
  );

  const openModal = async () => {
    await to(checkCronExecute());

    if (checkCronState.value?.data.data === false) {
      message.warn('当前输入的cron表达式不合法，请修改或清空后再试');
      return;
    }

    vue3CronModelValue.value = valueModel.value || defaultVue3CronModelValue;

    isModalOpen.value = true;
  };
  const handleModalOk = () => {
    valueModel.value = vue3CronModelValue.value;
    emit('changeByCronEditor');

    isModalOpen.value = false;
  };

  const {
    state: triggerTimesState,
    execute: executeTriggerTimes,
    isLoading: triggerTimesLoading,
  } = useAsyncState(
    () =>
      vue3CronModelValue.value
        ? taskApi.triggerTimes(vue3CronModelValue.value)
        : Promise.resolve(null),
    null,
    {
      immediate: false,
      resetOnExecute: false,
    },
  );
  watch(vue3CronModelValue, () => executeTriggerTimes());
</script>

<template>
  <div class="flex">
    <AInput
      v-model:value.trim="valueModel"
      :disabled="props.disabled || checkCronLoading"
      allow-clear
      :placeholder="props.disabled ? '' : '请输入cron表达式'"
    />
    <AButton
      style="margin-left: 10px"
      type="dashed"
      :icon="h(EditOutlined)"
      :disabled="props.disabled"
      :loading="checkCronLoading"
      @click="openModal"
    ></AButton>
  </div>
  <AModal
    v-if="checkCronState?.data.data !== false"
    v-model:open="isModalOpen"
    title="Cron 表达式"
    :width="900"
    destroy-on-close
    @ok="handleModalOk"
  >
    <AFormItem class="mb-0">
      <div class="flex border-[0.5px] border-[var(--ant-colorBorder)] border-solid">
        <div class="flex-1 bg-[var(--ant-colorBgLayout)]">
          <ADivider orientation="left">编辑器</ADivider>
          <div class="px-8">
            <Vue3Cron
              v-model="vue3CronModelValue"
              locale="cn"
              class="cron-wrapper"
            />
          </div>
        </div>
        <div class="w-[260px] border-l-[0.5px] border-l-[var(--ant-colorBorder)] border-l-solid">
          <ADivider>表达式预览</ADivider>
          <div class="break-all px-8 text-center italic">
            {{ vue3CronModelValue }}
          </div>
          <ADivider>最近10次触发时间预览</ADivider>
          <div class="px-8 text-center">
            <ASpin :spinning="triggerTimesLoading">
              <div
                v-for="item in triggerTimesState?.data.data || []"
                :key="item"
              >
                <ATypographyText type="secondary">
                  {{ item }}
                </ATypographyText>
              </div>
            </ASpin>
          </div>
        </div>
      </div>
    </AFormItem>
  </AModal>
</template>

<style lang="less" scoped>
  .cron-wrapper:deep(.expression),
  .cron-wrapper:deep(.preview) {
    display: none;
  }

  .cron-wrapper:deep(.cron-tabs) {
    border: 0 none transparent;

    .ant-tabs-nav {
      background-color: transparent;
    }
  }
</style>
