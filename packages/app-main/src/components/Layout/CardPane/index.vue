<script lang="ts" setup>
import { InfoCircleOutlined } from '@ant-design/icons-vue';

defineOptions({
  name: 'CardPane',
});

interface EmptyProps {
  show: boolean;
  descript?: string;
}

withDefaults(
  defineProps<{
    loading?: boolean;
    empty?: EmptyProps;
    title?: string;
    tip?: string;
    height?: string | number;
  }>(),
  {
    height: '100%',
  },
);
</script>

<template>
  <div class="card-pane__card" :style="{ height: typeof height === 'number' ? `${height}px` : height }">
    <div v-if="$slots.title || title" class="card-pane__header">
      <div class="card-pane__title">
        <slot name="title">{{ title }}</slot>
        <ATooltip v-if="tip" :title="tip" color="#fff" :overlay-inner-style="{
          color: '#000',
        }">
          <InfoCircleOutlined class="ml-2 cursor-help text-[#999]" />
        </ATooltip>
        <slot name="prefix" />
      </div>
      <div v-if="$slots['extra']" class="card-pane__extra">
        <slot name="extra" />
      </div>
    </div>
    <div class="card-pane__body overflow-y-auto">
      <ASpin :spinning="loading">
        <AEmpty v-if="empty?.show" :description="empty?.descript" />
        <slot v-else />
      </ASpin>
    </div>
  </div>
</template>

<style lang="less" scoped>
.card-pane {
  &__card {
      background: rgba(255,255,255,0.8);
      border-radius: 10px;
      box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03);
  }

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    padding-bottom: 0;
    padding-top: 20px;
    // border-bottom: 1px solid #f0f0f0;
  }

  &__title {
    font-size: 16px;
    font-weight: 500;
      color: rgba(0, 0, 0, 0.85);
  }

  &__body {
    padding: 10px 20px 16px;
    height: calc(100% - 52px);
    :deep(.ant-spin-nested-loading),
    :deep(.ant-spin-container) {
      height: 100%;
    }
  }
}
</style>
