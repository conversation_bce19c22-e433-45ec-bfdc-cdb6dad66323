<template>
  <div class="title-card-0 flex flex-col">
    <!-- 蓝色箭头图标 -->
    <div class="flex items-center">
      <!-- 标题文本 -->
      <div class="title text-overflow-hidden text-[14px] font-600">
        <div class="text">
          {{ title }}
        </div>
      </div>
    </div>

    <div class="pl-[12px] py-2">
      <slot />
    </div>
  </div>
</template>

<script setup lang="ts">
import Arrow from '@/assets/images/title_blue_arrow_l.png';
interface Props {
  title?: string;
}
defineOptions({
  name: 'TitleCard1',
});
withDefaults(defineProps<Props>(), {
  title: '标题内容',
});
</script>
<style lang="less" scoped>
.title-card-0 {
  .title {
    flex: 1;
    position: relative;
    padding-right: 8px;
    padding-bottom: 10px;
    border-bottom: 1px dashed #DCDCDC;

    .text {
      padding-left: 10px;
      position: relative;
      z-index: 1;

      &::before {
        content: '';
        position: absolute;
        width: 3px;
        bottom: 0;
        left: 0;
        right: 0;
        height: 100%;
        background: #3173FF
      }
    }

  }
}
</style>
