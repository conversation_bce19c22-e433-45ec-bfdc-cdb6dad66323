<template>
  <div class="title-card-0 flex flex-col">
    <!-- 蓝色箭头图标 -->
    <div class="flex items-center">
      <img
        :src="Arrow"
        alt=""
        class="mr-[7px] w-[5px] h-auto"
      />

      <!-- 标题文本 -->
      <div class="title text-overflow-hidden text-[14px] font-600 text-[var(--zion-blue-color)]">
        <div class="text">
          {{ title }}
        </div>
      </div>
    </div>

    <div class="pl-[12px] py-2">
      <slot />
    </div>
  </div>
</template>

<script setup lang="ts">
  import Arrow from '@/assets/images/title_blue_arrow_l.png';
  interface Props {
    title?: string;
  }
  defineOptions({
    name: 'TitleCard0',
  });
  withDefaults(defineProps<Props>(), {
    title: '标题内容',
  });
</script>
<style lang="less" scoped>
  .title-card-0 {
    .title {
      flex: 1;
      height: 18px;
      position: relative;

      div.text {
        width: 100%;
        position: absolute;
        z-index: 1;
      }
      &::before {
        content: '';
        position: absolute;
        width: 198px;
        bottom: 2px;
        left: 0;
        right: 0;
        height: 50%;
        background: linear-gradient(270deg, rgba(219, 238, 255, 0) 0%, #d1e2ff 100%);
      }
    }
  }
</style>
