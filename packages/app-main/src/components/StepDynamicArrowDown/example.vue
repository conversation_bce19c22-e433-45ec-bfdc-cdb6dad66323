<template>
  <div class="arrow-examples">
    <h3>动态颜色箭头示例</h3>
    
    <!-- 使用预设颜色 -->
    <div class="example-section">
      <h4>预设颜色</h4>
      <div class="arrow-grid">
        <div 
          v-for="(config, name) in colorPresets" 
          :key="name"
          class="arrow-item"
        >
          <DynamicArrowDown 
            :color-config="config"
            :width="40"
            :height="20"
          />
          <span class="arrow-label">{{ name }}</span>
        </div>
      </div>
    </div>

    <!-- 自定义颜色 -->
    <div class="example-section">
      <h4>自定义颜色</h4>
      <div class="custom-controls">
        <div class="color-input">
          <label>起始颜色:</label>
          <input v-model="customConfig.startColor" type="color" />
        </div>
        <div class="color-input">
          <label>结束颜色:</label>
          <input v-model="customConfig.endColor" type="color" />
        </div>
        <div class="opacity-input">
          <label>起始透明度:</label>
          <input v-model="customConfig.startOpacity" type="range" min="0" max="1" step="0.1" />
          <span>{{ customConfig.startOpacity }}</span>
        </div>
        <div class="opacity-input">
          <label>结束透明度:</label>
          <input v-model="customConfig.endOpacity" type="range" min="0" max="1" step="0.1" />
          <span>{{ customConfig.endOpacity }}</span>
        </div>
      </div>
      <div class="custom-arrow">
        <DynamicArrowDown 
          :color-config="customConfig"
          :width="60"
          :height="30"
        />
      </div>
    </div>

    <!-- 在项目中使用 -->
    <div class="example-section">
      <h4>在项目卡片中使用</h4>
      <div class="project-cards">
        <div 
          v-for="(project, index) in projects" 
          :key="project.name"
          class="project-card"
        >
          <div class="card-header">
            <span class="project-name">{{ project.name }}</span>
            <DynamicArrowDown 
              :color-config="project.arrowColor"
              :width="24"
              :height="12"
            />
          </div>
          <div class="card-content">
            <div class="stat">
              <span class="stat-value">{{ project.value }}</span>
              <span class="stat-unit">{{ project.unit }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import DynamicArrowDown from './index.vue';

// 获取颜色预设
const colorPresets = {
  blue: {
    startColor: '#70B9FF',
    endColor: '#006BFF',
    startOpacity: 0,
    endOpacity: 0.4
  },
  green: {
    startColor: '#52C41A',
    endColor: '#389E0D',
    startOpacity: 0,
    endOpacity: 0.4
  },
  orange: {
    startColor: '#FFA940',
    endColor: '#FA8C16',
    startOpacity: 0,
    endOpacity: 0.4
  },
  red: {
    startColor: '#FF7875',
    endColor: '#F5222D',
    startOpacity: 0,
    endOpacity: 0.4
  },
  purple: {
    startColor: '#B37FEB',
    endColor: '#722ED1',
    startOpacity: 0,
    endOpacity: 0.4
  }
};

// 自定义颜色配置
const customConfig = reactive({
  startColor: '#FF6B6B',
  endColor: '#4ECDC4',
  startOpacity: 0.2,
  endOpacity: 0.8
});

// 项目数据示例
const projects = ref([
  {
    name: '项目A',
    value: 92,
    unit: '分',
    arrowColor: colorPresets.green
  },
  {
    name: '项目B',
    value: 49,
    unit: '%',
    arrowColor: colorPresets.blue
  },
  {
    name: '项目C',
    value: 226,
    unit: '万元',
    arrowColor: colorPresets.orange
  },
  {
    name: '项目D',
    value: 598,
    unit: '人',
    arrowColor: colorPresets.red
  },
  {
    name: '项目E',
    value: 52,
    unit: '%',
    arrowColor: colorPresets.purple
  }
]);
</script>

<style scoped lang="less">
.arrow-examples {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.example-section {
  margin-bottom: 40px;
  
  h4 {
    margin-bottom: 16px;
    color: #333;
    font-weight: 600;
  }
}

.arrow-grid {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.arrow-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  
  .arrow-label {
    font-size: 12px;
    color: #666;
    text-transform: capitalize;
  }
}

.custom-controls {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.color-input, .opacity-input {
  display: flex;
  align-items: center;
  gap: 8px;
  
  label {
    font-size: 14px;
    color: #333;
    min-width: 80px;
  }
  
  input[type="color"] {
    width: 40px;
    height: 30px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
  }
  
  input[type="range"] {
    flex: 1;
  }
  
  span {
    font-size: 12px;
    color: #666;
    min-width: 30px;
  }
}

.custom-arrow {
  display: flex;
  justify-content: center;
  padding: 20px;
  background: #f5f5f5;
  border-radius: 8px;
}

.project-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.project-card {
  background: white;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    
    .project-name {
      font-size: 14px;
      font-weight: 500;
      color: #333;
    }
  }
  
  .card-content {
    .stat {
      display: flex;
      align-items: baseline;
      gap: 4px;
      
      .stat-value {
        font-size: 24px;
        font-weight: bold;
        color: #333;
      }
      
      .stat-unit {
        font-size: 14px;
        color: #666;
      }
    }
  }
}
</style> 