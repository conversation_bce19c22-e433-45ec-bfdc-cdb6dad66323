<script setup lang="ts">
  import { useElementSize } from '@vueuse/core';
  import { computed, ref, watch } from 'vue';

  const props = withDefaults(
    defineProps<{
      colsWidth?: number;
      fixedCols?: number;
      gapX?: number;
      gapY?: number;
    }>(),
    {
      colsWidth: 320,
      fixedCols: undefined,
      gapX: 16,
      gapY: 16,
    },
  );
  const emit = defineEmits<{
    updateCols: [number];
  }>();

  const el = ref<HTMLElement>();
  const { width } = useElementSize(el);

  const cols = computed(() => props.fixedCols || Math.floor(width.value / props.colsWidth));

  watch(
    cols,
    (val) => {
      if (val >= 1) {
        emit('updateCols', val);
      }
    },
    { immediate: true },
  );
</script>

<template>
  <div
    ref="el"
    class="transition-all-container grid gap-x-2"
    :style="{
      columnGap: `${props.gapX}px`,
      rowGap: `${props.gapY}px`,
      gridTemplateColumns: `repeat(${cols}, minmax(0, 1fr))`,
    }"
  >
    <slot />
  </div>
</template>
