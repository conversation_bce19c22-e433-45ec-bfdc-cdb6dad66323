<script setup lang="ts">
  const props = withDefaults(
    defineProps<{
      column?: number;
    }>(),
    {
      column: 1,
    },
  );
</script>

<template>
  <ADescriptions
    size="small"
    :column="props.column"
  >
    <slot />
  </ADescriptions>
</template>

<style lang="less" scoped>
  &:deep(.ant-descriptions-item) {
    padding-bottom: 2px;

    .ant-descriptions-item-label {
      font-size: 13px;
      color: var(--ant-colorTextTertiary);
      padding-left: 12px;
      position: relative;

      &::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 4px;
        width: 4px;
        height: 4px;
        margin-top: -2px;
        border-radius: 9999px;
        background-color: var(--ant-colorTextTertiary);
        box-shadow: var(--ant-boxShadowTertiary);
      }
    }

    .ant-descriptions-item-content {
      color: var(--ant-colorTextSecondary);
      font-size: 12px;
      display: inline-block;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
</style>
