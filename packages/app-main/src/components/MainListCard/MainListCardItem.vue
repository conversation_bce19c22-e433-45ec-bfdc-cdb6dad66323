<template>
  <div class="main-list-card-item cursor-pointer">
    <ACard size="small">
      <div class="title h-12 truncate rounded-[var(--ant-borderRadius)] px-4 leading-12">
        <slot name="title" />
      </div>
      <div class="px-1 py-3">
        <slot name="content" />
      </div>
    </ACard>
  </div>
</template>

<style lang="less" scoped>
  .main-list-card-item {
    .title {
      background-image: linear-gradient(to right, var(--ant-colorPrimaryBg), transparent);
    }

    &:hover {
      .ant-card {
        border-left-width: 5px;
        border-color: var(--ant-colorPrimary);
        box-shadow: var(--ant-boxShadowSecondary);
      }

      .title {
        color: var(--ant-colorPrimary);
        background-image: linear-gradient(to right, var(--ant-colorPrimaryBgHover), transparent);
      }
    }
  }
</style>
