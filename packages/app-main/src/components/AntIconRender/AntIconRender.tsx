import type { AntdIconProps } from '@ant-design/icons-vue/lib/components/AntdIcon';
import * as R from 'remeda';
import { defineAsyncComponent } from 'vue';

const antIconGroups = {
  directional: {
    label: '方向性图标',
    iconNames: {
      StepBackwardOutlined: () => import('@ant-design/icons-vue/StepBackwardOutlined'),
      StepForwardOutlined: () => import('@ant-design/icons-vue/StepForwardOutlined'),
      FastBackwardOutlined: () => import('@ant-design/icons-vue/FastBackwardOutlined'),
      FastForwardOutlined: () => import('@ant-design/icons-vue/FastForwardOutlined'),
      ShrinkOutlined: () => import('@ant-design/icons-vue/ShrinkOutlined'),
      ArrowsAltOutlined: () => import('@ant-design/icons-vue/ArrowsAltOutlined'),
      DownOutlined: () => import('@ant-design/icons-vue/DownOutlined'),
      UpOutlined: () => import('@ant-design/icons-vue/UpOutlined'),
      LeftOutlined: () => import('@ant-design/icons-vue/LeftOutlined'),
      RightOutlined: () => import('@ant-design/icons-vue/RightOutlined'),
      CaretUpOutlined: () => import('@ant-design/icons-vue/CaretUpOutlined'),
      CaretDownOutlined: () => import('@ant-design/icons-vue/CaretDownOutlined'),
      CaretLeftOutlined: () => import('@ant-design/icons-vue/CaretLeftOutlined'),
      CaretRightOutlined: () => import('@ant-design/icons-vue/CaretRightOutlined'),
      UpCircleOutlined: () => import('@ant-design/icons-vue/UpCircleOutlined'),
      DownCircleOutlined: () => import('@ant-design/icons-vue/DownCircleOutlined'),
      LeftCircleOutlined: () => import('@ant-design/icons-vue/LeftCircleOutlined'),
      RightCircleOutlined: () => import('@ant-design/icons-vue/RightCircleOutlined'),
      DoubleRightOutlined: () => import('@ant-design/icons-vue/DoubleRightOutlined'),
      DoubleLeftOutlined: () => import('@ant-design/icons-vue/DoubleLeftOutlined'),
      VerticalLeftOutlined: () => import('@ant-design/icons-vue/VerticalLeftOutlined'),
      VerticalRightOutlined: () => import('@ant-design/icons-vue/VerticalRightOutlined'),
      VerticalAlignTopOutlined: () => import('@ant-design/icons-vue/VerticalAlignTopOutlined'),
      VerticalAlignMiddleOutlined: () =>
        import('@ant-design/icons-vue/VerticalAlignMiddleOutlined'),
      VerticalAlignBottomOutlined: () =>
        import('@ant-design/icons-vue/VerticalAlignBottomOutlined'),
      ForwardOutlined: () => import('@ant-design/icons-vue/ForwardOutlined'),
      BackwardOutlined: () => import('@ant-design/icons-vue/BackwardOutlined'),
      RollbackOutlined: () => import('@ant-design/icons-vue/RollbackOutlined'),
      EnterOutlined: () => import('@ant-design/icons-vue/EnterOutlined'),
      RetweetOutlined: () => import('@ant-design/icons-vue/RetweetOutlined'),
      SwapOutlined: () => import('@ant-design/icons-vue/SwapOutlined'),
      SwapLeftOutlined: () => import('@ant-design/icons-vue/SwapLeftOutlined'),
      SwapRightOutlined: () => import('@ant-design/icons-vue/SwapRightOutlined'),
      ArrowUpOutlined: () => import('@ant-design/icons-vue/ArrowUpOutlined'),
      ArrowDownOutlined: () => import('@ant-design/icons-vue/ArrowDownOutlined'),
      ArrowLeftOutlined: () => import('@ant-design/icons-vue/ArrowLeftOutlined'),
      ArrowRightOutlined: () => import('@ant-design/icons-vue/ArrowRightOutlined'),
      PlayCircleOutlined: () => import('@ant-design/icons-vue/PlayCircleOutlined'),
      UpSquareOutlined: () => import('@ant-design/icons-vue/UpSquareOutlined'),
      DownSquareOutlined: () => import('@ant-design/icons-vue/DownSquareOutlined'),
      LeftSquareOutlined: () => import('@ant-design/icons-vue/LeftSquareOutlined'),
      RightSquareOutlined: () => import('@ant-design/icons-vue/RightSquareOutlined'),
      LoginOutlined: () => import('@ant-design/icons-vue/LoginOutlined'),
      LogoutOutlined: () => import('@ant-design/icons-vue/LogoutOutlined'),
      MenuFoldOutlined: () => import('@ant-design/icons-vue/MenuFoldOutlined'),
      MenuUnfoldOutlined: () => import('@ant-design/icons-vue/MenuUnfoldOutlined'),
      BorderBottomOutlined: () => import('@ant-design/icons-vue/BorderBottomOutlined'),
      BorderHorizontalOutlined: () => import('@ant-design/icons-vue/BorderHorizontalOutlined'),
      BorderInnerOutlined: () => import('@ant-design/icons-vue/BorderInnerOutlined'),
      BorderOuterOutlined: () => import('@ant-design/icons-vue/BorderOuterOutlined'),
      BorderLeftOutlined: () => import('@ant-design/icons-vue/BorderLeftOutlined'),
      BorderRightOutlined: () => import('@ant-design/icons-vue/BorderRightOutlined'),
      BorderTopOutlined: () => import('@ant-design/icons-vue/BorderTopOutlined'),
      BorderVerticleOutlined: () => import('@ant-design/icons-vue/BorderVerticleOutlined'),
      PicCenterOutlined: () => import('@ant-design/icons-vue/PicCenterOutlined'),
      PicLeftOutlined: () => import('@ant-design/icons-vue/PicLeftOutlined'),
      PicRightOutlined: () => import('@ant-design/icons-vue/PicRightOutlined'),
      RadiusBottomleftOutlined: () => import('@ant-design/icons-vue/RadiusBottomleftOutlined'),
      RadiusBottomrightOutlined: () => import('@ant-design/icons-vue/RadiusBottomrightOutlined'),
      RadiusUpleftOutlined: () => import('@ant-design/icons-vue/RadiusUpleftOutlined'),
      RadiusUprightOutlined: () => import('@ant-design/icons-vue/RadiusUprightOutlined'),
      FullscreenOutlined: () => import('@ant-design/icons-vue/FullscreenOutlined'),
      FullscreenExitOutlined: () => import('@ant-design/icons-vue/FullscreenExitOutlined'),
    },
  },
  suggested: {
    label: '提示建议性图标',
    iconNames: {
      QuestionOutlined: () => import('@ant-design/icons-vue/QuestionOutlined'),
      QuestionCircleOutlined: () => import('@ant-design/icons-vue/QuestionCircleOutlined'),
      PlusOutlined: () => import('@ant-design/icons-vue/PlusOutlined'),
      PlusCircleOutlined: () => import('@ant-design/icons-vue/PlusCircleOutlined'),
      PauseOutlined: () => import('@ant-design/icons-vue/PauseOutlined'),
      PauseCircleOutlined: () => import('@ant-design/icons-vue/PauseCircleOutlined'),
      MinusOutlined: () => import('@ant-design/icons-vue/MinusOutlined'),
      MinusCircleOutlined: () => import('@ant-design/icons-vue/MinusCircleOutlined'),
      PlusSquareOutlined: () => import('@ant-design/icons-vue/PlusSquareOutlined'),
      MinusSquareOutlined: () => import('@ant-design/icons-vue/MinusSquareOutlined'),
      InfoOutlined: () => import('@ant-design/icons-vue/InfoOutlined'),
      InfoCircleOutlined: () => import('@ant-design/icons-vue/InfoCircleOutlined'),
      ExclamationOutlined: () => import('@ant-design/icons-vue/ExclamationOutlined'),
      ExclamationCircleOutlined: () => import('@ant-design/icons-vue/ExclamationCircleOutlined'),
      CloseOutlined: () => import('@ant-design/icons-vue/CloseOutlined'),
      CloseCircleOutlined: () => import('@ant-design/icons-vue/CloseCircleOutlined'),
      CloseSquareOutlined: () => import('@ant-design/icons-vue/CloseSquareOutlined'),
      CheckOutlined: () => import('@ant-design/icons-vue/CheckOutlined'),
      CheckCircleOutlined: () => import('@ant-design/icons-vue/CheckCircleOutlined'),
      CheckSquareOutlined: () => import('@ant-design/icons-vue/CheckSquareOutlined'),
      ClockCircleOutlined: () => import('@ant-design/icons-vue/ClockCircleOutlined'),
      WarningOutlined: () => import('@ant-design/icons-vue/WarningOutlined'),
      IssuesCloseOutlined: () => import('@ant-design/icons-vue/IssuesCloseOutlined'),
      StopOutlined: () => import('@ant-design/icons-vue/StopOutlined'),
    },
  },
  editor: {
    label: '编辑类图标',
    iconNames: {
      EditOutlined: () => import('@ant-design/icons-vue/EditOutlined'),
      FormOutlined: () => import('@ant-design/icons-vue/FormOutlined'),
      CopyOutlined: () => import('@ant-design/icons-vue/CopyOutlined'),
      ScissorOutlined: () => import('@ant-design/icons-vue/ScissorOutlined'),
      DeleteOutlined: () => import('@ant-design/icons-vue/DeleteOutlined'),
      SnippetsOutlined: () => import('@ant-design/icons-vue/SnippetsOutlined'),
      DiffOutlined: () => import('@ant-design/icons-vue/DiffOutlined'),
      HighlightOutlined: () => import('@ant-design/icons-vue/HighlightOutlined'),
      AlignCenterOutlined: () => import('@ant-design/icons-vue/AlignCenterOutlined'),
      AlignLeftOutlined: () => import('@ant-design/icons-vue/AlignLeftOutlined'),
      AlignRightOutlined: () => import('@ant-design/icons-vue/AlignRightOutlined'),
      BgColorsOutlined: () => import('@ant-design/icons-vue/BgColorsOutlined'),
      BoldOutlined: () => import('@ant-design/icons-vue/BoldOutlined'),
      ItalicOutlined: () => import('@ant-design/icons-vue/ItalicOutlined'),
      UnderlineOutlined: () => import('@ant-design/icons-vue/UnderlineOutlined'),
      StrikethroughOutlined: () => import('@ant-design/icons-vue/StrikethroughOutlined'),
      RedoOutlined: () => import('@ant-design/icons-vue/RedoOutlined'),
      UndoOutlined: () => import('@ant-design/icons-vue/UndoOutlined'),
      ZoomInOutlined: () => import('@ant-design/icons-vue/ZoomInOutlined'),
      ZoomOutOutlined: () => import('@ant-design/icons-vue/ZoomOutOutlined'),
      FontColorsOutlined: () => import('@ant-design/icons-vue/FontColorsOutlined'),
      FontSizeOutlined: () => import('@ant-design/icons-vue/FontSizeOutlined'),
      LineHeightOutlined: () => import('@ant-design/icons-vue/LineHeightOutlined'),
      DashOutlined: () => import('@ant-design/icons-vue/DashOutlined'),
      SmallDashOutlined: () => import('@ant-design/icons-vue/SmallDashOutlined'),
      SortAscendingOutlined: () => import('@ant-design/icons-vue/SortAscendingOutlined'),
      SortDescendingOutlined: () => import('@ant-design/icons-vue/SortDescendingOutlined'),
      DragOutlined: () => import('@ant-design/icons-vue/DragOutlined'),
      OrderedListOutlined: () => import('@ant-design/icons-vue/OrderedListOutlined'),
      UnorderedListOutlined: () => import('@ant-design/icons-vue/UnorderedListOutlined'),
      RadiusSettingOutlined: () => import('@ant-design/icons-vue/RadiusSettingOutlined'),
      ColumnWidthOutlined: () => import('@ant-design/icons-vue/ColumnWidthOutlined'),
      ColumnHeightOutlined: () => import('@ant-design/icons-vue/ColumnHeightOutlined'),
    },
  },
  data: {
    label: '数据类图标',
    iconNames: {
      AreaChartOutlined: () => import('@ant-design/icons-vue/AreaChartOutlined'),
      PieChartOutlined: () => import('@ant-design/icons-vue/PieChartOutlined'),
      BarChartOutlined: () => import('@ant-design/icons-vue/BarChartOutlined'),
      DotChartOutlined: () => import('@ant-design/icons-vue/DotChartOutlined'),
      LineChartOutlined: () => import('@ant-design/icons-vue/LineChartOutlined'),
      RadarChartOutlined: () => import('@ant-design/icons-vue/RadarChartOutlined'),
      HeatMapOutlined: () => import('@ant-design/icons-vue/HeatMapOutlined'),
      FallOutlined: () => import('@ant-design/icons-vue/FallOutlined'),
      RiseOutlined: () => import('@ant-design/icons-vue/RiseOutlined'),
      StockOutlined: () => import('@ant-design/icons-vue/StockOutlined'),
      BoxPlotOutlined: () => import('@ant-design/icons-vue/BoxPlotOutlined'),
      FundOutlined: () => import('@ant-design/icons-vue/FundOutlined'),
      SlidersOutlined: () => import('@ant-design/icons-vue/SlidersOutlined'),
    },
  },
  brand: {
    label: '品牌和标识',
    iconNames: {
      AndroidOutlined: () => import('@ant-design/icons-vue/AndroidOutlined'),
      AppleOutlined: () => import('@ant-design/icons-vue/AppleOutlined'),
      WindowsOutlined: () => import('@ant-design/icons-vue/WindowsOutlined'),
      IeOutlined: () => import('@ant-design/icons-vue/IeOutlined'),
      ChromeOutlined: () => import('@ant-design/icons-vue/ChromeOutlined'),
      GithubOutlined: () => import('@ant-design/icons-vue/GithubOutlined'),
      AliwangwangOutlined: () => import('@ant-design/icons-vue/AliwangwangOutlined'),
      DingdingOutlined: () => import('@ant-design/icons-vue/DingdingOutlined'),
      WeiboSquareOutlined: () => import('@ant-design/icons-vue/WeiboSquareOutlined'),
      WeiboCircleOutlined: () => import('@ant-design/icons-vue/WeiboCircleOutlined'),
      TaobaoCircleOutlined: () => import('@ant-design/icons-vue/TaobaoCircleOutlined'),
      Html5Outlined: () => import('@ant-design/icons-vue/Html5Outlined'),
      WeiboOutlined: () => import('@ant-design/icons-vue/WeiboOutlined'),
      TwitterOutlined: () => import('@ant-design/icons-vue/TwitterOutlined'),
      WechatOutlined: () => import('@ant-design/icons-vue/WechatOutlined'),
      YoutubeOutlined: () => import('@ant-design/icons-vue/YoutubeOutlined'),
      AlipayCircleOutlined: () => import('@ant-design/icons-vue/AlipayCircleOutlined'),
      TaobaoOutlined: () => import('@ant-design/icons-vue/TaobaoOutlined'),
      SkypeOutlined: () => import('@ant-design/icons-vue/SkypeOutlined'),
      QqOutlined: () => import('@ant-design/icons-vue/QqOutlined'),
      MediumWorkmarkOutlined: () => import('@ant-design/icons-vue/MediumWorkmarkOutlined'),
      GitlabOutlined: () => import('@ant-design/icons-vue/GitlabOutlined'),
      MediumOutlined: () => import('@ant-design/icons-vue/MediumOutlined'),
      GooglePlusOutlined: () => import('@ant-design/icons-vue/GooglePlusOutlined'),
      DropboxOutlined: () => import('@ant-design/icons-vue/DropboxOutlined'),
      FacebookOutlined: () => import('@ant-design/icons-vue/FacebookOutlined'),
      CodepenOutlined: () => import('@ant-design/icons-vue/CodepenOutlined'),
      CodeSandboxOutlined: () => import('@ant-design/icons-vue/CodeSandboxOutlined'),
      AmazonOutlined: () => import('@ant-design/icons-vue/AmazonOutlined'),
      GoogleOutlined: () => import('@ant-design/icons-vue/GoogleOutlined'),
      CodepenCircleOutlined: () => import('@ant-design/icons-vue/CodepenCircleOutlined'),
      AlipayOutlined: () => import('@ant-design/icons-vue/AlipayOutlined'),
      AntDesignOutlined: () => import('@ant-design/icons-vue/AntDesignOutlined'),
      AntCloudOutlined: () => import('@ant-design/icons-vue/AntCloudOutlined'),
      AliyunOutlined: () => import('@ant-design/icons-vue/AliyunOutlined'),
      ZhihuOutlined: () => import('@ant-design/icons-vue/ZhihuOutlined'),
      SlackOutlined: () => import('@ant-design/icons-vue/SlackOutlined'),
      SlackSquareOutlined: () => import('@ant-design/icons-vue/SlackSquareOutlined'),
      BehanceOutlined: () => import('@ant-design/icons-vue/BehanceOutlined'),
      BehanceSquareOutlined: () => import('@ant-design/icons-vue/BehanceSquareOutlined'),
      DribbbleOutlined: () => import('@ant-design/icons-vue/DribbbleOutlined'),
      DribbbleSquareOutlined: () => import('@ant-design/icons-vue/DribbbleSquareOutlined'),
      InstagramOutlined: () => import('@ant-design/icons-vue/InstagramOutlined'),
      YuqueOutlined: () => import('@ant-design/icons-vue/YuqueOutlined'),
      AlibabaOutlined: () => import('@ant-design/icons-vue/AlibabaOutlined'),
      YahooOutlined: () => import('@ant-design/icons-vue/YahooOutlined'),
      RedditOutlined: () => import('@ant-design/icons-vue/RedditOutlined'),
      SketchOutlined: () => import('@ant-design/icons-vue/SketchOutlined'),
    },
  },
  application: {
    label: '网站通用图标',
    iconNames: {
      AccountBookOutlined: () => import('@ant-design/icons-vue/AccountBookOutlined'),
      AimOutlined: () => import('@ant-design/icons-vue/AimOutlined'),
      AlertOutlined: () => import('@ant-design/icons-vue/AlertOutlined'),
      ApartmentOutlined: () => import('@ant-design/icons-vue/ApartmentOutlined'),
      ApiOutlined: () => import('@ant-design/icons-vue/ApiOutlined'),
      AppstoreAddOutlined: () => import('@ant-design/icons-vue/AppstoreAddOutlined'),
      AppstoreOutlined: () => import('@ant-design/icons-vue/AppstoreOutlined'),
      AudioOutlined: () => import('@ant-design/icons-vue/AudioOutlined'),
      AudioMutedOutlined: () => import('@ant-design/icons-vue/AudioMutedOutlined'),
      AuditOutlined: () => import('@ant-design/icons-vue/AuditOutlined'),
      BankOutlined: () => import('@ant-design/icons-vue/BankOutlined'),
      BarcodeOutlined: () => import('@ant-design/icons-vue/BarcodeOutlined'),
      BarsOutlined: () => import('@ant-design/icons-vue/BarsOutlined'),
      BellOutlined: () => import('@ant-design/icons-vue/BellOutlined'),
      BlockOutlined: () => import('@ant-design/icons-vue/BlockOutlined'),
      BookOutlined: () => import('@ant-design/icons-vue/BookOutlined'),
      BorderOutlined: () => import('@ant-design/icons-vue/BorderOutlined'),
      BorderlessTableOutlined: () => import('@ant-design/icons-vue/BorderlessTableOutlined'),
      BranchesOutlined: () => import('@ant-design/icons-vue/BranchesOutlined'),
      BugOutlined: () => import('@ant-design/icons-vue/BugOutlined'),
      BuildOutlined: () => import('@ant-design/icons-vue/BuildOutlined'),
      BulbOutlined: () => import('@ant-design/icons-vue/BulbOutlined'),
      CalculatorOutlined: () => import('@ant-design/icons-vue/CalculatorOutlined'),
      CalendarOutlined: () => import('@ant-design/icons-vue/CalendarOutlined'),
      CameraOutlined: () => import('@ant-design/icons-vue/CameraOutlined'),
      CarOutlined: () => import('@ant-design/icons-vue/CarOutlined'),
      CarryOutOutlined: () => import('@ant-design/icons-vue/CarryOutOutlined'),
      CiCircleOutlined: () => import('@ant-design/icons-vue/CiCircleOutlined'),
      CiOutlined: () => import('@ant-design/icons-vue/CiOutlined'),
      ClearOutlined: () => import('@ant-design/icons-vue/ClearOutlined'),
      CloudDownloadOutlined: () => import('@ant-design/icons-vue/CloudDownloadOutlined'),
      CloudOutlined: () => import('@ant-design/icons-vue/CloudOutlined'),
      CloudServerOutlined: () => import('@ant-design/icons-vue/CloudServerOutlined'),
      CloudSyncOutlined: () => import('@ant-design/icons-vue/CloudSyncOutlined'),
      CloudUploadOutlined: () => import('@ant-design/icons-vue/CloudUploadOutlined'),
      ClusterOutlined: () => import('@ant-design/icons-vue/ClusterOutlined'),
      CodeOutlined: () => import('@ant-design/icons-vue/CodeOutlined'),
      CoffeeOutlined: () => import('@ant-design/icons-vue/CoffeeOutlined'),
      CommentOutlined: () => import('@ant-design/icons-vue/CommentOutlined'),
      CompassOutlined: () => import('@ant-design/icons-vue/CompassOutlined'),
      CompressOutlined: () => import('@ant-design/icons-vue/CompressOutlined'),
      ConsoleSqlOutlined: () => import('@ant-design/icons-vue/ConsoleSqlOutlined'),
      ContactsOutlined: () => import('@ant-design/icons-vue/ContactsOutlined'),
      ContainerOutlined: () => import('@ant-design/icons-vue/ContainerOutlined'),
      ControlOutlined: () => import('@ant-design/icons-vue/ControlOutlined'),
      CopyrightCircleOutlined: () => import('@ant-design/icons-vue/CopyrightCircleOutlined'),
      CopyrightOutlined: () => import('@ant-design/icons-vue/CopyrightOutlined'),
      CreditCardOutlined: () => import('@ant-design/icons-vue/CreditCardOutlined'),
      CrownOutlined: () => import('@ant-design/icons-vue/CrownOutlined'),
      CustomerServiceOutlined: () => import('@ant-design/icons-vue/CustomerServiceOutlined'),
      DashboardOutlined: () => import('@ant-design/icons-vue/DashboardOutlined'),
      DatabaseOutlined: () => import('@ant-design/icons-vue/DatabaseOutlined'),
      DeleteColumnOutlined: () => import('@ant-design/icons-vue/DeleteColumnOutlined'),
      DeleteRowOutlined: () => import('@ant-design/icons-vue/DeleteRowOutlined'),
      DeliveredProcedureOutlined: () => import('@ant-design/icons-vue/DeliveredProcedureOutlined'),
      DeploymentUnitOutlined: () => import('@ant-design/icons-vue/DeploymentUnitOutlined'),
      DesktopOutlined: () => import('@ant-design/icons-vue/DesktopOutlined'),
      DingtalkOutlined: () => import('@ant-design/icons-vue/DingtalkOutlined'),
      DisconnectOutlined: () => import('@ant-design/icons-vue/DisconnectOutlined'),
      DislikeOutlined: () => import('@ant-design/icons-vue/DislikeOutlined'),
      DollarCircleOutlined: () => import('@ant-design/icons-vue/DollarCircleOutlined'),
      DollarOutlined: () => import('@ant-design/icons-vue/DollarOutlined'),
      DownloadOutlined: () => import('@ant-design/icons-vue/DownloadOutlined'),
      EllipsisOutlined: () => import('@ant-design/icons-vue/EllipsisOutlined'),
      EnvironmentOutlined: () => import('@ant-design/icons-vue/EnvironmentOutlined'),
      EuroCircleOutlined: () => import('@ant-design/icons-vue/EuroCircleOutlined'),
      EuroOutlined: () => import('@ant-design/icons-vue/EuroOutlined'),
      ExceptionOutlined: () => import('@ant-design/icons-vue/ExceptionOutlined'),
      ExpandAltOutlined: () => import('@ant-design/icons-vue/ExpandAltOutlined'),
      ExpandOutlined: () => import('@ant-design/icons-vue/ExpandOutlined'),
      ExperimentOutlined: () => import('@ant-design/icons-vue/ExperimentOutlined'),
      ExportOutlined: () => import('@ant-design/icons-vue/ExportOutlined'),
      EyeOutlined: () => import('@ant-design/icons-vue/EyeOutlined'),
      EyeInvisibleOutlined: () => import('@ant-design/icons-vue/EyeInvisibleOutlined'),
      FieldBinaryOutlined: () => import('@ant-design/icons-vue/FieldBinaryOutlined'),
      FieldNumberOutlined: () => import('@ant-design/icons-vue/FieldNumberOutlined'),
      FieldStringOutlined: () => import('@ant-design/icons-vue/FieldStringOutlined'),
      FieldTimeOutlined: () => import('@ant-design/icons-vue/FieldTimeOutlined'),
      FileAddOutlined: () => import('@ant-design/icons-vue/FileAddOutlined'),
      FileDoneOutlined: () => import('@ant-design/icons-vue/FileDoneOutlined'),
      FileExcelOutlined: () => import('@ant-design/icons-vue/FileExcelOutlined'),
      FileExclamationOutlined: () => import('@ant-design/icons-vue/FileExclamationOutlined'),
      FileOutlined: () => import('@ant-design/icons-vue/FileOutlined'),
      FileGifOutlined: () => import('@ant-design/icons-vue/FileGifOutlined'),
      FileImageOutlined: () => import('@ant-design/icons-vue/FileImageOutlined'),
      FileJpgOutlined: () => import('@ant-design/icons-vue/FileJpgOutlined'),
      FileMarkdownOutlined: () => import('@ant-design/icons-vue/FileMarkdownOutlined'),
      FilePdfOutlined: () => import('@ant-design/icons-vue/FilePdfOutlined'),
      FilePptOutlined: () => import('@ant-design/icons-vue/FilePptOutlined'),
      FileProtectOutlined: () => import('@ant-design/icons-vue/FileProtectOutlined'),
      FileSearchOutlined: () => import('@ant-design/icons-vue/FileSearchOutlined'),
      FileSyncOutlined: () => import('@ant-design/icons-vue/FileSyncOutlined'),
      FileTextOutlined: () => import('@ant-design/icons-vue/FileTextOutlined'),
      FileUnknownOutlined: () => import('@ant-design/icons-vue/FileUnknownOutlined'),
      FileWordOutlined: () => import('@ant-design/icons-vue/FileWordOutlined'),
      FileZipOutlined: () => import('@ant-design/icons-vue/FileZipOutlined'),
      FilterOutlined: () => import('@ant-design/icons-vue/FilterOutlined'),
      FireOutlined: () => import('@ant-design/icons-vue/FireOutlined'),
      FlagOutlined: () => import('@ant-design/icons-vue/FlagOutlined'),
      FolderAddOutlined: () => import('@ant-design/icons-vue/FolderAddOutlined'),
      FolderOutlined: () => import('@ant-design/icons-vue/FolderOutlined'),
      FolderOpenOutlined: () => import('@ant-design/icons-vue/FolderOpenOutlined'),
      FolderViewOutlined: () => import('@ant-design/icons-vue/FolderViewOutlined'),
      ForkOutlined: () => import('@ant-design/icons-vue/ForkOutlined'),
      FormatPainterOutlined: () => import('@ant-design/icons-vue/FormatPainterOutlined'),
      FrownOutlined: () => import('@ant-design/icons-vue/FrownOutlined'),
      FunctionOutlined: () => import('@ant-design/icons-vue/FunctionOutlined'),
      FundProjectionScreenOutlined: () =>
        import('@ant-design/icons-vue/FundProjectionScreenOutlined'),
      FundViewOutlined: () => import('@ant-design/icons-vue/FundViewOutlined'),
      FunnelPlotOutlined: () => import('@ant-design/icons-vue/FunnelPlotOutlined'),
      GatewayOutlined: () => import('@ant-design/icons-vue/GatewayOutlined'),
      GifOutlined: () => import('@ant-design/icons-vue/GifOutlined'),
      GiftOutlined: () => import('@ant-design/icons-vue/GiftOutlined'),
      GlobalOutlined: () => import('@ant-design/icons-vue/GlobalOutlined'),
      GoldOutlined: () => import('@ant-design/icons-vue/GoldOutlined'),
      GroupOutlined: () => import('@ant-design/icons-vue/GroupOutlined'),
      HddOutlined: () => import('@ant-design/icons-vue/HddOutlined'),
      HeartOutlined: () => import('@ant-design/icons-vue/HeartOutlined'),
      HistoryOutlined: () => import('@ant-design/icons-vue/HistoryOutlined'),
      HolderOutlined: () => import('@ant-design/icons-vue/HolderOutlined'),
      HomeOutlined: () => import('@ant-design/icons-vue/HomeOutlined'),
      HourglassOutlined: () => import('@ant-design/icons-vue/HourglassOutlined'),
      IdcardOutlined: () => import('@ant-design/icons-vue/IdcardOutlined'),
      ImportOutlined: () => import('@ant-design/icons-vue/ImportOutlined'),
      InboxOutlined: () => import('@ant-design/icons-vue/InboxOutlined'),
      InsertRowAboveOutlined: () => import('@ant-design/icons-vue/InsertRowAboveOutlined'),
      InsertRowBelowOutlined: () => import('@ant-design/icons-vue/InsertRowBelowOutlined'),
      InsertRowLeftOutlined: () => import('@ant-design/icons-vue/InsertRowLeftOutlined'),
      InsertRowRightOutlined: () => import('@ant-design/icons-vue/InsertRowRightOutlined'),
      InsuranceOutlined: () => import('@ant-design/icons-vue/InsuranceOutlined'),
      InteractionOutlined: () => import('@ant-design/icons-vue/InteractionOutlined'),
      KeyOutlined: () => import('@ant-design/icons-vue/KeyOutlined'),
      LaptopOutlined: () => import('@ant-design/icons-vue/LaptopOutlined'),
      LayoutOutlined: () => import('@ant-design/icons-vue/LayoutOutlined'),
      LikeOutlined: () => import('@ant-design/icons-vue/LikeOutlined'),
      LineOutlined: () => import('@ant-design/icons-vue/LineOutlined'),
      LinkOutlined: () => import('@ant-design/icons-vue/LinkOutlined'),
      Loading3QuartersOutlined: () => import('@ant-design/icons-vue/Loading3QuartersOutlined'),
      LoadingOutlined: () => import('@ant-design/icons-vue/LoadingOutlined'),
      LockOutlined: () => import('@ant-design/icons-vue/LockOutlined'),
      MacCommandOutlined: () => import('@ant-design/icons-vue/MacCommandOutlined'),
      MailOutlined: () => import('@ant-design/icons-vue/MailOutlined'),
      ManOutlined: () => import('@ant-design/icons-vue/ManOutlined'),
      MedicineBoxOutlined: () => import('@ant-design/icons-vue/MedicineBoxOutlined'),
      MehOutlined: () => import('@ant-design/icons-vue/MehOutlined'),
      MenuOutlined: () => import('@ant-design/icons-vue/MenuOutlined'),
      MergeCellsOutlined: () => import('@ant-design/icons-vue/MergeCellsOutlined'),
      MessageOutlined: () => import('@ant-design/icons-vue/MessageOutlined'),
      MobileOutlined: () => import('@ant-design/icons-vue/MobileOutlined'),
      MoneyCollectOutlined: () => import('@ant-design/icons-vue/MoneyCollectOutlined'),
      MonitorOutlined: () => import('@ant-design/icons-vue/MonitorOutlined'),
      MoreOutlined: () => import('@ant-design/icons-vue/MoreOutlined'),
      NodeCollapseOutlined: () => import('@ant-design/icons-vue/NodeCollapseOutlined'),
      NodeExpandOutlined: () => import('@ant-design/icons-vue/NodeExpandOutlined'),
      NodeIndexOutlined: () => import('@ant-design/icons-vue/NodeIndexOutlined'),
      NotificationOutlined: () => import('@ant-design/icons-vue/NotificationOutlined'),
      NumberOutlined: () => import('@ant-design/icons-vue/NumberOutlined'),
      OneToOneOutlined: () => import('@ant-design/icons-vue/OneToOneOutlined'),
      PaperClipOutlined: () => import('@ant-design/icons-vue/PaperClipOutlined'),
      PartitionOutlined: () => import('@ant-design/icons-vue/PartitionOutlined'),
      PayCircleOutlined: () => import('@ant-design/icons-vue/PayCircleOutlined'),
      PercentageOutlined: () => import('@ant-design/icons-vue/PercentageOutlined'),
      PhoneOutlined: () => import('@ant-design/icons-vue/PhoneOutlined'),
      PictureOutlined: () => import('@ant-design/icons-vue/PictureOutlined'),
      PlaySquareOutlined: () => import('@ant-design/icons-vue/PlaySquareOutlined'),
      PoundCircleOutlined: () => import('@ant-design/icons-vue/PoundCircleOutlined'),
      PoundOutlined: () => import('@ant-design/icons-vue/PoundOutlined'),
      PoweroffOutlined: () => import('@ant-design/icons-vue/PoweroffOutlined'),
      PrinterOutlined: () => import('@ant-design/icons-vue/PrinterOutlined'),
      ProfileOutlined: () => import('@ant-design/icons-vue/ProfileOutlined'),
      ProjectOutlined: () => import('@ant-design/icons-vue/ProjectOutlined'),
      PropertySafetyOutlined: () => import('@ant-design/icons-vue/PropertySafetyOutlined'),
      PullRequestOutlined: () => import('@ant-design/icons-vue/PullRequestOutlined'),
      PushpinOutlined: () => import('@ant-design/icons-vue/PushpinOutlined'),
      QrcodeOutlined: () => import('@ant-design/icons-vue/QrcodeOutlined'),
      ReadOutlined: () => import('@ant-design/icons-vue/ReadOutlined'),
      ReconciliationOutlined: () => import('@ant-design/icons-vue/ReconciliationOutlined'),
      RedEnvelopeOutlined: () => import('@ant-design/icons-vue/RedEnvelopeOutlined'),
      ReloadOutlined: () => import('@ant-design/icons-vue/ReloadOutlined'),
      RestOutlined: () => import('@ant-design/icons-vue/RestOutlined'),
      RobotOutlined: () => import('@ant-design/icons-vue/RobotOutlined'),
      RocketOutlined: () => import('@ant-design/icons-vue/RocketOutlined'),
      RotateLeftOutlined: () => import('@ant-design/icons-vue/RotateLeftOutlined'),
      RotateRightOutlined: () => import('@ant-design/icons-vue/RotateRightOutlined'),
      SafetyCertificateOutlined: () => import('@ant-design/icons-vue/SafetyCertificateOutlined'),
      SafetyOutlined: () => import('@ant-design/icons-vue/SafetyOutlined'),
      SaveOutlined: () => import('@ant-design/icons-vue/SaveOutlined'),
      ScanOutlined: () => import('@ant-design/icons-vue/ScanOutlined'),
      ScheduleOutlined: () => import('@ant-design/icons-vue/ScheduleOutlined'),
      SearchOutlined: () => import('@ant-design/icons-vue/SearchOutlined'),
      SecurityScanOutlined: () => import('@ant-design/icons-vue/SecurityScanOutlined'),
      SelectOutlined: () => import('@ant-design/icons-vue/SelectOutlined'),
      SendOutlined: () => import('@ant-design/icons-vue/SendOutlined'),
      SettingOutlined: () => import('@ant-design/icons-vue/SettingOutlined'),
      ShakeOutlined: () => import('@ant-design/icons-vue/ShakeOutlined'),
      ShareAltOutlined: () => import('@ant-design/icons-vue/ShareAltOutlined'),
      ShopOutlined: () => import('@ant-design/icons-vue/ShopOutlined'),
      ShoppingCartOutlined: () => import('@ant-design/icons-vue/ShoppingCartOutlined'),
      ShoppingOutlined: () => import('@ant-design/icons-vue/ShoppingOutlined'),
      SisternodeOutlined: () => import('@ant-design/icons-vue/SisternodeOutlined'),
      SkinOutlined: () => import('@ant-design/icons-vue/SkinOutlined'),
      SmileOutlined: () => import('@ant-design/icons-vue/SmileOutlined'),
      SolutionOutlined: () => import('@ant-design/icons-vue/SolutionOutlined'),
      SoundOutlined: () => import('@ant-design/icons-vue/SoundOutlined'),
      SplitCellsOutlined: () => import('@ant-design/icons-vue/SplitCellsOutlined'),
      StarOutlined: () => import('@ant-design/icons-vue/StarOutlined'),
      SubnodeOutlined: () => import('@ant-design/icons-vue/SubnodeOutlined'),
      SwitcherOutlined: () => import('@ant-design/icons-vue/SwitcherOutlined'),
      SyncOutlined: () => import('@ant-design/icons-vue/SyncOutlined'),
      TableOutlined: () => import('@ant-design/icons-vue/TableOutlined'),
      TabletOutlined: () => import('@ant-design/icons-vue/TabletOutlined'),
      TagOutlined: () => import('@ant-design/icons-vue/TagOutlined'),
      TagsOutlined: () => import('@ant-design/icons-vue/TagsOutlined'),
      TeamOutlined: () => import('@ant-design/icons-vue/TeamOutlined'),
      ThunderboltOutlined: () => import('@ant-design/icons-vue/ThunderboltOutlined'),
      ToTopOutlined: () => import('@ant-design/icons-vue/ToTopOutlined'),
      ToolOutlined: () => import('@ant-design/icons-vue/ToolOutlined'),
      TrademarkCircleOutlined: () => import('@ant-design/icons-vue/TrademarkCircleOutlined'),
      TrademarkOutlined: () => import('@ant-design/icons-vue/TrademarkOutlined'),
      TransactionOutlined: () => import('@ant-design/icons-vue/TransactionOutlined'),
      TranslationOutlined: () => import('@ant-design/icons-vue/TranslationOutlined'),
      TrophyOutlined: () => import('@ant-design/icons-vue/TrophyOutlined'),
      UngroupOutlined: () => import('@ant-design/icons-vue/UngroupOutlined'),
      UnlockOutlined: () => import('@ant-design/icons-vue/UnlockOutlined'),
      UploadOutlined: () => import('@ant-design/icons-vue/UploadOutlined'),
      UsbOutlined: () => import('@ant-design/icons-vue/UsbOutlined'),
      UserAddOutlined: () => import('@ant-design/icons-vue/UserAddOutlined'),
      UserDeleteOutlined: () => import('@ant-design/icons-vue/UserDeleteOutlined'),
      UserOutlined: () => import('@ant-design/icons-vue/UserOutlined'),
      UserSwitchOutlined: () => import('@ant-design/icons-vue/UserSwitchOutlined'),
      UsergroupAddOutlined: () => import('@ant-design/icons-vue/UsergroupAddOutlined'),
      UsergroupDeleteOutlined: () => import('@ant-design/icons-vue/UsergroupDeleteOutlined'),
      VerifiedOutlined: () => import('@ant-design/icons-vue/VerifiedOutlined'),
      VideoCameraAddOutlined: () => import('@ant-design/icons-vue/VideoCameraAddOutlined'),
      VideoCameraOutlined: () => import('@ant-design/icons-vue/VideoCameraOutlined'),
      WalletOutlined: () => import('@ant-design/icons-vue/WalletOutlined'),
      WhatsAppOutlined: () => import('@ant-design/icons-vue/WhatsAppOutlined'),
      WifiOutlined: () => import('@ant-design/icons-vue/WifiOutlined'),
      WomanOutlined: () => import('@ant-design/icons-vue/WomanOutlined'),
    },
  },
};

// FIX: mergeAll 为什么会丢失类型?
const antIcons = R.pipe(
  antIconGroups.directional.iconNames,
  R.merge(antIconGroups.suggested.iconNames),
  R.merge(antIconGroups.editor.iconNames),
  R.merge(antIconGroups.data.iconNames),
  R.merge(antIconGroups.brand.iconNames),
  R.merge(antIconGroups.application.iconNames),
);

export type antIconName = keyof typeof antIcons;
export const antIconNames = R.pipe(
  antIconGroups,
  R.values,
  R.map((item) => ({
    label: item.label,
    options: R.pipe(
      item.iconNames,
      R.keys,
      R.map((name) => ({ value: name as antIconName, label: name as antIconName })),
    ),
  })),
);

interface AntIconRenderProps extends AntdIconProps {
  name: antIconName;
}
export const AntIconRender = (props: AntIconRenderProps) => {
  if (!Object.keys(antIcons).includes(props.name)) {
    return <span></span>;
  }

  const Icon = defineAsyncComponent(antIcons[props.name]);

  return <Icon {...R.omit(props, ['name'])} />;
};
