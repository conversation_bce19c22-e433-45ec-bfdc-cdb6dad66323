<!-- <script setup lang="ts">
  import { useElementSize, useVirtualList, useVModel, useWindowSize } from '@vueuse/core';
  import * as R from 'remeda';
  import { computed, ref } from 'vue';

  import { type antIconName, antIconNames, AntIconRender } from './AntIconRender';

  const props = defineProps<{
    value?: string;
    disabled?: boolean;
    placeholder?: string;
  }>();

  const valueModel = useVModel(props, 'value');
  const searchValue = ref('');

  const windowSize = useWindowSize();
  const containerMinWidth = computed(() => 160);
  const containerRef = ref<HTMLDivElement | null>(null);
  const { width: containerWidth } = useElementSize(containerRef);
  const containerHeight = computed(() => windowSize.height.value * 0.46);
  const itemWidth = 136;
  const itemHeight = 86;
  const itemNumbersPerRow = computed(() => Math.floor((containerWidth.value - 30) / itemWidth));

  const antIconNamesByRow = computed(() =>
    R.pipe(
      antIconNames,
      R.filter((name) => name.toLowerCase().indexOf(searchValue.value.toLowerCase()) > -1),
      R.chunk(itemNumbersPerRow.value),
    ),
  );

  const { list, containerProps, wrapperProps } = useVirtualList(antIconNamesByRow, { itemHeight });

  const handleClick = (name: string) => {
    searchValue.value = '';
    valueModel.value = name;
  };
</script>

<template>
  <ASelect
    v-model:value="valueModel"
    :search-value="searchValue"
    show-search
    :disabled="props.disabled"
    :placeholder="props.placeholder"
    :dropdown-match-select-width="false"
    :open="true"
    @search="(value) => (searchValue = value)"
  >
    <template
      v-if="valueModel"
      #suffixIcon
    >
      <AntIconRender
        class="text-[var(--ant-colorTextSecondary)]"
        :name="valueModel as antIconName"
      />
    </template>
    <template #dropdownRender>
      <div
        v-bind="containerProps"
        :style="{ minWidth: `${containerMinWidth}px`, height: `${containerHeight}px` }"
      >
        <div
          ref="containerRef"
          v-bind="wrapperProps"
        >
          <div
            v-if="antIconNamesByRow.length <= 0"
            class="flex items-center justify-center"
            :style="{ minWidth: `${containerMinWidth}px`, height: `${containerHeight}px` }"
          >
            <AEmpty :description="`未搜索到 ${searchValue} 的图标`" />
          </div>
          <template v-else>
            <div
              v-for="row in list"
              :key="row.index"
              :style="{ height: `${itemHeight}px` }"
              class="flex justify-between"
            >
              <div
                v-for="name in row.data"
                :key="name"
                :style="{ width: `${itemWidth}px` }"
                class="h-full flex flex-col cursor-pointer items-center justify-center rounded-[var(--ant-borderRadius)] text-[var(--ant-colorTextSecondary)] space-y-2 hover:bg-[var(--ant-controlItemBgHover)]"
                :class="{
                  'bg-[var(--ant-colorPrimaryBg)]': valueModel === name,
                }"
                @click="handleClick(name)"
              >
                <div
                  class="text-9 leading-9"
                  :class="{ 'text-[var(--ant-colorPrimary)]': valueModel === name }"
                >
                  <AntIconRender :name="name" />
                </div>
                <div
                  class="w-full truncate px-2 text-center text-xs opacity-80"
                  :class="{ 'text-[var(--ant-colorPrimary)]': valueModel === name }"
                >
                  {{ name }}
                </div>
              </div>
              <div
                v-for="i in itemNumbersPerRow - row.data.length"
                :key="i"
                :style="{ width: `${itemWidth}px` }"
              />
            </div>
          </template>
        </div>
      </div>
    </template>
  </ASelect>
</template> -->

<template>AntIconSelect</template>
