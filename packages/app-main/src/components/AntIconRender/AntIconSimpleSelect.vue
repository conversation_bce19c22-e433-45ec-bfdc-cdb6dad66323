<script setup lang="ts">
  import { useVModel } from '@vueuse/core';
  import { ref } from 'vue';
  import Highlighter from 'vue-highlight-words';

  import { antIconNames, AntIconRender } from './AntIconRender';

  const props = defineProps<{
    value?: string;
    disabled?: boolean;
    placeholder?: string;
  }>();

  const valueModel = useVModel(props, 'value');
  const searchValue = ref('');
</script>

<template>
  <ASelect
    v-model:value="valueModel"
    :search-value="searchValue"
    show-search
    allow-clear
    :disabled="props.disabled"
    :placeholder="props.placeholder"
    @search="(value) => (searchValue = value)"
  >
    <ASelectOptGroup
      v-for="group in antIconNames"
      :key="group.label"
    >
      <template #label>
        {{ group.label }}
      </template>
      <ASelectOption
        v-for="item in group.options"
        :key="item.value"
        :value="item.value"
        :label="item.value"
      >
        <div class="icon-item space-x-2">
          <span class="icon-label">
            <AntIconRender :name="item.value" />
          </span>
          <span class="opacity-80">
            <Highlighter
              highlight-class-name="highlight"
              :search-words="[searchValue]"
              :auto-escape="true"
              :text-to-highlight="item.value"
            />
          </span>
        </div>
      </ASelectOption>
    </ASelectOptGroup>
  </ASelect>
</template>

<style lang="less" scoped>
  :deep(.highlight) {
    padding: 0;
    color: var(--ant-colorPrimary);
    font-weight: normal;
    background: none;
  }

  .icon-item {
    display: flex;
    padding-right: 16px;
    justify-content: space-between;
    align-items: center;

    .icon-label {
      font-size: 18px;
      line-height: 18px;
    }
  }

  :deep(.ant-select-selection-item) {
    .icon-item {
      padding-right: 0;
      justify-content: flex-start;

      .icon-label {
        font-size: 14px;
      }
    }
  }
</style>
