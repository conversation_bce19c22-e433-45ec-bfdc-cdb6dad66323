<script setup lang="ts">

const legendData = [{ color: '#4B5071', text: '未开始' }, { color: '#3173FF', text: '进行中' }, { color: '#08C7A5', text: '已完成' }];

</script>

<template>
    <div class="progress-legend-tooltip">
        <div v-for="(item, index) in legendData" :key="index" class="legend-item">
            <span class="color-block" :style="{ backgroundColor: item.color }"></span>
            <span class="status-text">{{ item.text }}</span>
        </div>
    </div>
</template>

<style scoped>
.progress-legend-tooltip {
    padding-left: 8px;
    padding-right: 8px;
    min-width: 120px;
    display: flex;
    font-weight: 400;
    font-size: 12px;
    gap: 8px;
}

.legend-item {
    display: flex;
    align-items: center;
    margin: 4px 0;
}

.color-block {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 2px;
    margin-right: 5px;
}

.question-icon {
    cursor: pointer;
    margin-left: 4px;
}
</style>
