<script
  setup
  lang="tsx"
  generic="
    RequestResult extends {
      data?: any;
      totalCount?: null | number;
      pageIndex?: null | number;
      pageSize?: null | number;
    }
  "
>
  import { until } from '@vueuse/core';
  import { message } from 'ant-design-vue';
  import to from 'await-to-js';
  import type { AxiosResponse } from 'axios';
  import * as R from 'remeda';
  import { computed, ref, watch } from 'vue';

  const props = withDefaults(
    defineProps<{
      request: (sizeParams: {
        pageSize: number;
        pageIndex: number;
      }) => Promise<AxiosResponse<RequestResult>>;
      immediate?: boolean;
      simple?: boolean;
      size?: 'small' | 'default';
      pageSizeStep?: number;
    }>(),
    {
      size: undefined,
      immediate: true,
      simple: false,
      pageSizeStep: 10,
    },
  );
  const emit = defineEmits<{
    listChange: [list?: RequestResult['data']];
    loadingChange: [loading: boolean];
    pageChange: [pageParams: { pageIndex: number; pageSize: number }];
  }>();

  const total = ref(0);
  const current = ref(1);
  const pageSize = ref(props.pageSizeStep);
  watch(
    () => ({ pageIndex: current.value, pageSize: pageSize.value }),
    (pageParams) => emit('pageChange', pageParams),
    { immediate: true },
  );

  const loading = ref(false);
  watch(loading, () => emit('loadingChange', loading.value), { immediate: true });

  const list = ref<RequestResult['data']>();
  watch(list, () => emit('listChange', list.value || []), { immediate: true });

  const showTotal = (total: number, range: [number, number]) => (
    <span>
      <span class="opacity-50">
        第<span> {range[0] === range[1] ? range[0] : `${range[0]}-${range[1]}`} </span>
        条/
      </span>
      <span>总共 {total} 条</span>
    </span>
  );

  const pageSizeOptions = computed(() =>
    R.pipe(
      R.times(6, (i) => Math.pow(2, i) * props.pageSizeStep),
      R.map(String),
    ),
  );
  const pageSizeSelectWidth = computed(
    () => `${96 + Math.max(...pageSizeOptions.value.map((item) => item.length)) * 10}px`,
  );

  const fetchData = async () => {
    if (pageSize.value <= 0) {
      await until(() => props.pageSizeStep).toBeTruthy();
      pageSize.value = props.pageSizeStep;
    }
    loading.value = true;
    const [err, res] = await to(
      props.request({ pageIndex: current.value, pageSize: pageSize.value }),
    );
    loading.value = false;

    const records = res?.data.data;

    if (err || !records) {
      return;
    }

    if (current.value > 1 && (!Array.isArray(records) || records.length <= 0)) {
      message.info(`第 ${current.value} 页无数据，已切换到第 1 页`);
      current.value = 1;
      loading.value = true;
      await new Promise((resolve) => setTimeout(resolve, 1000));
      loading.value = false;
      fetchData();
      return;
    }

    total.value = res.data.totalCount || 0;
    current.value = res.data.pageIndex || 1;
    pageSize.value = res.data.pageSize || props.pageSizeStep;
    list.value = records;
  };

  if (props.immediate) {
    fetchData();
  }

  const fetchDataButResetPage = () => {
    current.value = 1;
    fetchData();
  };

  defineExpose({ fetchData, fetchDataButResetPage });
</script>

<template>
  <div>
    <div class="flex justify-end">
      <APagination
        v-model:current="current"
        v-model:page-size="pageSize"
        :disabled="loading"
        :total="total"
        :show-total="showTotal"
        :size="props.size"
        :page-size-options="pageSizeOptions"
        show-size-changer
        show-quick-jumper
        :simple="simple"
        @change="fetchData"
      />
    </div>
  </div>
</template>

<style lang="less" scoped>
  :deep(.ant-pagination-options) {
    .ant-select {
      width: v-bind(pageSizeSelectWidth);
    }
  }
</style>
