<script setup lang="tsx">
  import type { MenuInfo, MenuItemType } from 'ant-design-vue/es/menu/src/interface';
  import { Base64 } from 'js-base64';
  import * as R from 'remeda';
  import { computed, ref, watch } from 'vue';
  import { useRoute } from 'vue-router';

  import { useCurrentDark } from '@/stores/currentDark';

  import type { MenuTreeItem } from '../../apis/platform/systemMenu';
  import { AntIconRender } from '../../components/AntIconRender/AntIconRender';
  import treeHandle from '../../utils/treeHandle';

  const props = defineProps<{
    menu: MenuTreeItem[];
    loading: boolean;
    error?: string | any;
    inlineIndent?: number;
  }>();
  const emit = defineEmits<{
    click: [info: MenuInfo];
  }>();

  const inlineIndent = computed(() => props.inlineIndent || 24);

  const route = useRoute();

  const { handleTreeDataToList } = treeHandle();

  const flatMenuTree = computed(() => handleTreeDataToList(props.menu || []));

  // 获取query的url参数
  const getQueryParams = (search: string): Record<string, string> => {
    const params = new URLSearchParams(search);
    const queryParams: Record<string, string> = {};

    for (const [key, value] of params.entries()) {
      queryParams[key] = value;
    }

    return queryParams;
  };

  /** 是否处于暗黑模式 */
  const currentDark = useCurrentDark();

  /** 将菜单转化为 AMenu 接受的格式 */
  const convertMenu = (menu: MenuTreeItem[]): MenuItemType[] => {
    return menu.map<MenuItemType>((item) => {
      const { children, resourceId, name, icon } = item;

      return {
        originServerData: item,
        ...item,
        key: resourceId,
        label: name,
        icon: () => <AntIconRender name={icon} />,
        children:
          children && Array.isArray(children) && children.length > 0
            ? convertMenu(children)
            : undefined,
      };
    });
  };

  /** 先去掉字符串首尾空格(多个)，再去掉首尾所有斜杠(多个) */
  const trimSlash = (str?: string) => (str || '').trim().replace(/^\/+|\/+$/g, '');
  /** 菜单树菜单配置 */
  const menuItems = computed(() => {
    const tree = props.menu || [];

    // 转化为 Antd vue 支持的格式
    return convertMenu(tree);
  });
  /** 菜单树当前选中的菜单项 key */
  const menuSelectedKeys = computed(() => {
    const menuKey = R.findLast(route.matched, (item) => !!item.meta?.menuKey)?.meta?.menuKey;
    const urlFromRouteParams = route.params.url;

    let key: string | undefined;

    if (menuKey) {
      key = flatMenuTree.value.find(
        (item) => trimSlash(item.path) === trimSlash(menuKey),
      )?.resourceId;
    }

    if (!key) {
      // 1.兼容如果path内置了参数，注：这里只处理？情况 2.处理在线开发的预览页面（由于基础path都是一样的，这里通过pageId区分）
      key = flatMenuTree.value.find((item) => {
        const queryIndex = item.path.indexOf('?');
        if (queryIndex !== -1) {
          const basePath = item.path.split('?')[0];
          const search = item.path.split('?')[1];
          if (basePath === '/micro-app/online-dev-center/zero-code/workbench/preview') {
            const pageId = getQueryParams(search).pageId;
            return pageId === (route.query.pageId as string);
          } else {
            return trimSlash(basePath) === trimSlash(route.path);
          }
        }
        return trimSlash(item.path) === trimSlash(route.path);
      })?.resourceId;
    }

    if (!key && typeof urlFromRouteParams === 'string' && urlFromRouteParams) {
      // 从路由参数中获取经过 base64 + encodeURIComponent 编码的 url
      // 为动态嵌入的微前端页面提供菜单选中状态，但其他页面也可以通过这种方式传递 url
      const url = Base64.decode(decodeURIComponent(urlFromRouteParams));
      key = flatMenuTree.value.find((item) => trimSlash(item.path) === trimSlash(url))?.resourceId;
    }

    return key ? [key] : [];
  });
  /** 菜单树当前展开的 SubMenu 菜单项 key 数组 */
  const menuOpenKeys = ref<string[]>([]);
  /** 设置当前选中状态 */
  const setActivedMenu = () => {
    const openKeys: string[] = [];

    const findParent = (key: string) => {
      const item = flatMenuTree.value.find((item) => item.resourceId === key);

      if (item) {
        openKeys.push(item.resourceId);

        if (item.parentId) {
          findParent(item.parentId);
        }
      }
    };
    findParent(menuSelectedKeys.value[0] || '');

    menuOpenKeys.value = R.reject(openKeys, (item) => item === menuSelectedKeys.value[0]);
  };
  watch(() => props.menu, setActivedMenu, { immediate: true, deep: true });
</script>

<template>
  <div
    v-if="props.loading"
    class="h-100% min-h-140px flex items-center justify-center"
  >
    <ASpin />
  </div>
  <AMenu
    v-else
    v-model:open-keys="menuOpenKeys"
    :selected-keys="menuSelectedKeys"
    :inline-indent="inlineIndent"
    mode="inline"
    :theme="currentDark.isDark ? 'dark' : 'light'"
    :items="menuItems"
    class="bg-transparent !border-r-none"
    @click="(info) => emit('click', info)"
  />
</template>
