// 用于快速展示分数的水波图
<script setup lang="ts">
  import { computed } from 'vue';
  import VChart from 'vue-echarts';
  import 'echarts-liquidfill';
  import * as echarts from 'echarts/core';
  import { CanvasRenderer } from 'echarts/renderers';
  echarts.use([CanvasRenderer]);

  interface Props {
    value: number;
    width?: string | number;
    height?: string | number;
    stageOptions: {
      valueStyle?: {
        color: string;
      };
      // 外边框
      outline?: {
        itemStyle: {
          color: string;
          borderColor: string;
        };
      };
      // 填充色(仅支持单色)
      color?: string;
    };
    // 系列配置（目前建议只传入label）
    serizeOptions?: {
      label: any;
    };
    unit?: string;
  }
  const percent = computed(() => {
    return Number((props.value / 100).toFixed(2));
  });
  const props = withDefaults(defineProps<Props>(), {
    width: '100%',
    height: '100%',
    unit: '分',
    value: 0,
  });

  const liquidOption = computed(() => {
    return {
      series: [
        {
          type: 'liquidFill',
          radius: '90%',
          waveLength: '100%', // 波长
          data:
            props.value === 100 || props.value === 0
              ? [
                  {
                    amplitude: 0, // 设置振浮为0
                    itemStyle: {
                      opacity: 0.8,
                    },
                    value: percent.value,
                  },
                ]
              : [
                  {
                    itemStyle: {
                      opacity: 0.3,
                    },
                    value: percent.value - 0.002,
                  },
                  {
                    itemStyle: {
                      opacity: 0.8,
                    },
                    value: percent.value,
                  },
                ],

          label: {
            formatter: () => '{value|' + props.value + '分}',
            rich: {
              value: {
                color: '#FBFCFF',
                fontSize: 25,
                lineHeight: 25,
                ...(props.stageOptions.valueStyle || {}),
              },
            },
            fontSize: 14,
            align: 'center',
          },
          outline: {
            show: true,
            borderDistance: 6,
            // 去掉shadowBlur，它自带了的
            itemStyle: {
              color: '#EAF6E7',
              borderColor: '#EAF6E7',
              borderWidth: 1,
              shadowBlur: 0,
              ...(props.stageOptions.outline?.itemStyle ?? {}),
            },
          },
          backgroundStyle: {
            color: 'rgba(0,0,0,.05)',
            borderColor: 'rgba(255, 255, 255, .4)',
            borderWidth: 1,
            shadowBlur: 0,
            shadowColor: '#fff',
          },
          // 背景可以带点阴影,否则白色文字不明显
          // itemStyle: {
          //   shadowBlur: 0,
          // },
          color: [props.stageOptions.color || '#EAF6E7'],
          ...(props.serizeOptions || {}),
        },
      ],
    };
  });
</script>

<template>
  <VChart
    :option="liquidOption"
    autoresize
    :style="{
      width: typeof width === 'number' ? `${width}px` : width,
      height: typeof height === 'number' ? `${height}px` : height,
    }"
  />
</template>

<style scoped>
  /* 水波图容器样式 */
</style>
