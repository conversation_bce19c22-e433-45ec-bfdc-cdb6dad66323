<script setup lang="ts">
  import { computed, toRef } from 'vue';

  import {
    useUserPermission,
    useUserPermissionsData,
    useUserPermissionsEvery,
    useUserPermissionsSome,
  } from '@/hooks/useUserPermissions';

  const props = defineProps<{
    type?: 'page';
    permission?: string;
    permissionsEvery?: string[];
    permissionsSome?: string[];
  }>();

  const userPermissionsData = useUserPermissionsData();

  const permissionRef = toRef(props, 'permission');
  const permissionsEveryRef = toRef(props, 'permissionsEvery');
  const permissionsSomeRef = toRef(props, 'permissionsSome');

  const hasPermission = useUserPermission(permissionRef);
  const hasPermissionsEvery = useUserPermissionsEvery(permissionsEveryRef);
  const hasPermissionsSome = useUserPermissionsSome(permissionsSomeRef);

  const ignoreCheck = computed(() => {
    return !props.permission && !props.permissionsEvery && !props.permissionsSome;
  });

  const canShow = computed(() => {
    if (!userPermissionsData.isReady) {
      return false;
    }

    return hasPermission.value ?? hasPermissionsEvery.value ?? hasPermissionsSome.value;
  });
</script>

<template>
  <template v-if="!ignoreCheck && !userPermissionsData.isReady"></template>
  <slot v-else-if="ignoreCheck || canShow" />
  <div
    v-else-if="props.type === 'page'"
    class="h-full p-4"
  >
    <ACard class="min-h-full">
      <AResult status="warning">
        <template #title>
          <span>当前账号没有访问该页面的权限</span>
        </template>
      </AResult>
    </ACard>
  </div>
</template>
