<script lang="ts" setup>
  import { LoadingOutlined, PlusOutlined } from '@ant-design/icons-vue';
  import type { UploadProps } from 'ant-design-vue';
  import { message } from 'ant-design-vue';
  import type { UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface';
  import { ref } from 'vue';

  import attachmentApi from '@/apis';

  const props = withDefaults(
    defineProps<{
      modelValue: string;
      appId?: string;
      dirId?: string;
      tenantId?: string;
      limit?: number;
    }>(),
    {
      limit: 2,
      appId: '',
      dirId: '',
      tenantId: '',
    },
  );
  const emits = defineEmits<{
    (e: 'update:modelValue', value: string): void;
  }>();

  const fileList = ref([]);
  const loading = ref<boolean>(false);
  const beforeUpload = (file: UploadProps['fileList'][number]) => {
    if (!file.type.startsWith('image/')) {
      message.error('只能上传图片!');
      return false;
    }
    if (!(file.size / 1024 / 1024 < props.limit)) {
      message.error(`文件大小超出限制，最大支持${props.limit}MB!`);
      return false;
    }
    return true;
  };
  // const { appId: aId, tenantId: tId } = useAppidAndTenantid();

  function customRequest(e: UploadRequestOption) {
    console.log(e);
    loading.value = true;
    attachmentApi
      .attachmentUpload({
        file: e.file,
        // appId: aId.value || props.appId,
        // tenantId: tId.value || props.tenantId,
        dirId: props.dirId,
      })
      .then((res) => {
        if (res.data.success && res.data.data?.id) {
          emits('update:modelValue', res.data.data?.id as string);
        }
      })
      .finally(() => {
        loading.value = false;
      });
  }
</script>

<template>
  <AUpload
    v-model:file-list="fileList"
    name="file"
    list-type="picture-card"
    class="avatar-uploader"
    accept="image/*"
    :show-upload-list="false"
    :before-upload="beforeUpload"
    :custom-request="customRequest"
    v-bind="$attrs"
  >
    <slot
      v-if="modelValue"
      name="img"
    >
      <ImgPreview
        :id="modelValue"
        class="rounded-2"
      />
    </slot>
    <slot
      v-else
      name="default"
    >
      <div>
        <LoadingOutlined v-if="loading"></LoadingOutlined>
        <PlusOutlined v-else></PlusOutlined>
        <div class="ant-upload-text">图片上传</div>
      </div>
    </slot>
  </AUpload>
</template>

<style scoped>
  .avatar-uploader > .ant-upload {
    width: 128px;
    height: 128px;
  }

  .ant-upload-select-picture-card i {
    font-size: 32px;
    color: #999;
  }

  .ant-upload-select-picture-card .ant-upload-text {
    margin-top: 8px;
    color: #666;
    font-size: 12px;
  }
</style>
