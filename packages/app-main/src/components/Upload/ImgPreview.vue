<script setup lang="ts">
  import { FileImageOutlined } from '@ant-design/icons-vue';

  import attachmentApi from '@/apis/attachment/index';

  const props = withDefaults(
    defineProps<{
      id: string;
      alt?: string;
      default?: string;
      imgClass?: string;
      canPreview?: boolean;
    }>(),
    { alt: undefined, default: undefined, imgClass: undefined, canPreview: false },
  );
  const url = ref(props.default || '');
  const spinning = ref(false);
  const isError = ref(false);
  watch(
    () => props.id,
    (val) => {
      if (val) {
        spinning.value = true;
        attachmentApi
          .downloadById(val)
          .then((res) => {
            console.log(res);
            if (res.data.type.startsWith('image/')) {
              url.value = URL.createObjectURL(res.data);
            } else {
              isError.value = true;
            }
          })
          .catch(() => {
            isError.value = true;
          })
          .finally(() => {
            spinning.value = false;
          });
      }
    },
    {
      immediate: true,
    },
  );
  onBeforeUnmount(() => {
    url.value && URL.revokeObjectURL(url.value);
  });

  const isPreviewVisible = ref(false);
  const setIsPreviewVisible = (visible: boolean) => {
    isPreviewVisible.value = visible;
  };
</script>

<template>
  <ASpin
    v-if="spinning"
    wrapper-class-name="h-full w-full"
  />
  <FileImageOutlined
    v-else-if="isError || !props.id"
    class="text-2xl"
  />
  <img
    v-else
    :src="url"
    class="h-full w-full"
    :class="imgClass"
    :style="{ cursor: props.canPreview ? 'pointer' : undefined }"
    :alt="alt"
    @click="setIsPreviewVisible(true)"
  />

  <AImage
    v-if="props.canPreview"
    class="!hidden"
    :preview="{
      visible: isPreviewVisible,
      onVisibleChange: setIsPreviewVisible,
    }"
    :src="url"
  />
</template>
