import { APPROVE_STATUS_STRS } from '@/apis/code-generate/approval';
export enum AUDIT_ACTION {
  AGREE = 'agree',
  REJECT = 'reject',
}
// 审批状态
export enum AUDTI_STATUS {
  PROCESS = 1,
  REJECT = 2,
  SUCCESS = 3,
}
export const statusOptions = [
  {
    value: AUDTI_STATUS.PROCESS,
    label: APPROVE_STATUS_STRS.PROCESSING,
  },
  {
    value: AUDTI_STATUS.REJECT,
    label: APPROVE_STATUS_STRS.REJECT,
  },
  {
    value: AUDTI_STATUS.SUCCESS,
    label: APPROVE_STATUS_STRS.PASS,
  },
];

export const auditOptions = [
  {
    value: AUDIT_ACTION.AGREE,
    label: '同意',
    color: 'success',
  },
  {
    value: AUDIT_ACTION.REJECT,
    label: '拒绝',
    color: 'error',
  },
];
// 获取对应配置
export const getAuditOption = (status: AUDIT_ACTION) => {
  return auditOptions.find((item) => item.value === status);
};

export const getStatusOption = (status: AUDTI_STATUS) => {
  return statusOptions.find((item) => item.value === status);
};
