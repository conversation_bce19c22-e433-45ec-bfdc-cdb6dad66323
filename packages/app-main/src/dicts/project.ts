// 项目阶段枚举(目前一些用到的)
export enum ProjectStage {
  DEMAND = 'demand_stage',
  DESIGN = 'design_stage',
  DEV = 'dev_stage',
  TEST = 'test_stage',
  PUBLISH = 'publish_stage',
}

// 这里仅导出 3中颜色（已完成的，进行中的，未开始的）
export const stageStepMap = {
  finished: {
    text: '已执行',
    bgColor: '#73D045',
  },
  processing: {
    text: '执行中',
    bgColor: '#5E98FC',
  },
  default: {
    text: '未执行',
    bgColor: '#C2C2C2',
  },
};

// 项目阶段状态配置（已废弃，后端阶段是动态的）
const textClipStyleStr =
  '-webkit-text-fill-color: transparent;background-clip: text;text-fill-color: transparent;';
export const projectStageOptions = [
  {
    value: ProjectStage.DEMAND,
    label: '需求阶段',
    // 阶段步骤样式配置
    step: {
      arrowDownColors: [{ opacity: 0, color: '#70B9FF' }, { opacity: 1, color: '#006BFF' }], // 箭头颜色渐变
      titleTextStyle: `background: linear-gradient(116deg, #2F7DFF 44%, #5B98FF 100%);${textClipStyleStr}`,
      seqTextStyle: `background: linear-gradient(173deg, #2F7DFF 1%, #5B98FF 99%);${textClipStyleStr}`,
      title: {
        background: 'linear-gradient(114deg,rgba(47, 125, 255, .1) 45%,rgba(91, 152, 255,.1) 100%)',
      },
      body: {
        background: '#F3F7FF',
      },
      borderColor: '#C5DAFF',
    },
    tag: { color: '#5C208E', bgColor: 'rgba(121, 128, 255, .4)' },
  },
  {
    value: ProjectStage.DESIGN,
    label: '设计阶段',
    // 状态tag配置
    step: {
      arrowDownColors: [{ opacity: 0, color: '#F9A800' }, { opacity: 1, color: '#FFDA8E' }], // 箭头颜色渐变
      titleTextStyle: `background: linear-gradient(117deg, #F9A800 44%, #FFBF37 100%);${textClipStyleStr}`,
      seqTextStyle: `background: linear-gradient(169deg, #F9A800 3%, #FFBF37 100%);${textClipStyleStr}`,
      title: {
        background: 'linear-gradient(112deg, #F9A800 46%, #FFBF37 100%);',
      },
      body: {
        background: '#FFFDF8',
      },
      borderColor: '#FFE1B1',
    },
    tag: { color: '#5C208E', bgColor: 'rgba(121, 128, 255, .4)' },
  },
  {
    value: ProjectStage.DEV,
    label: '研发阶段',
    step: {
      arrowDownColors: [{ opacity: 0, color: '#35C7CC' }, { opacity: 1, color: '#1CCDB7' }], // 箭头颜色渐变
      titleTextStyle: `background: linear-gradient(117deg, #1CCDB7 44%, #76D3C4 100%);${textClipStyleStr}`,
      seqTextStyle: `background: linear-gradient(169deg, #1CCDB7 3%, #76D3C4 100%);${textClipStyleStr}`,
      title: {
        background: 'linear-gradient(#1CCDB7 0%, #76D3C4 100%);',
      },
      body: {
        background: '#F5F9FB',
      },
      borderColor: '#56E1CF',
    },
    // 状态tag配置
    tag: { color: '#536AFF', bgColor: 'rgba(83, 142, 255, .35)' },
  },
  {
    value: ProjectStage.TEST,
    label: '测试阶段',
    step: {
      arrowDownColors: [{ opacity: 0, color: '#94DEA3' }, { opacity: 1, color: '#95EDA6' }], // 箭头颜色渐变
      titleTextStyle: `background: linear-gradient(117deg, #4BD46E 44%, #67DF81 100%);${textClipStyleStr}`,
      seqTextStyle: `background: linear-gradient(169deg, #25C755 3%, #67DF81 99%);${textClipStyleStr}`,
      title: {
        background: 'linear-gradient(112deg, #25C755 46%, #67DF81 99%);',
      },
      body: {
        background: '#F5F9FA',
      },
      borderColor: '#7CEF9E',
    },
    // 状态tag配置
    tag: { color: '#8C2A13', bgColor: 'rgba(255, 120, 2, .4)' },
  },
  {
    value: ProjectStage.PUBLISH,
    label: '上线阶段',
    step: {
      arrowDownColors: [{ opacity: 0, color: '#70B9FF' }, { opacity: 1, color: '#006BFF' }], // 箭头颜色渐变
      titleTextStyle: `background: linear-gradient(117deg, #555DEE 45%, #747CED 99%);${textClipStyleStr}`,
      seqTextStyle: `background: linear-gradient(169deg, #555DEE 5%, #747CED 99%);${textClipStyleStr}`,
      title: {
        background: 'linear-gradient(rgba(85, 93, 238, .1) 0%, rgba(116, 124, 237, .1) 100%)',
      },
      body: {
        background: '#F6F8FE',
      },
      borderColor: '#C3C6FF',
    },
    tag: { color: '#268309', bgColor: 'rgba(98, 214, 44, .4)' },
  },
] as const;
// 默认未开始的阶段配置
export const defaultStepConfig = {
  arrowDownColors: [
    { opacity: 1, color: '#FCFCFC' },
    { opacity: 1, color: '#C4C4C4' },
  ], // 箭头颜色渐变
  titleTextStyle: 'color:#969696',
  seqTextStyle: 'color:#969696',
  borderColor: '#E7E7E7',
  title: {
    background: 'linear-gradient(rgba(74, 74, 74, .1) 0%,rgba(172, 172, 172, .1) 100%)',
  },
  body: {
    background: '#FCFCFC',
  },
};

// 获取阶段配置的辅助函数（已废弃）
export const getStageConfig = (stage: ProjectStage) => {
  return projectStageOptions.find((option) => option.value === stage);
};

// 获取阶段标签的辅助函数（已废弃）
export const getStageLabel = (stage: ProjectStage) => {
  return getStageConfig(stage)?.label || stage;
};

export const devopingStatus = [
  {
    value: 0,
    label: '未开始',
  },
  {
    value: 4,
    label: '放行测试中',
  },
];
// 研发与进度色值获取 （ 50%红色，50-80橙色，80以上绿色）
export const getProgressStrokeColor = (percent: number) => {
  if (percent < 50) {
    return '#EE6060';
  } else if (percent < 80) {
    return '#FF7102';
  } else {
    return '#49CE20';
  }
};
// 研发进度 渐变获取
export const getProgressLinearColor = (percent: number) => {
  if (percent < 50) {
    return ['#EE3B3B', '#FF9696'];
  } else if (percent < 80) {
    return ['#FF9696', '#FFD196'];
  } else {
    return ['#49CE20', '#95EC7B'];
  }
};
// 过程符合度（过程符合度小于90%红色，大于90-99橙色，100绿色）
export const getConformityColor = (percent: number) => {
  if (percent < 90) {
    return '#EE6060';
  } else if (90 <= percent && percent < 100) {
    return '#FF7102';
  } else {
    return '#49CE20';
  }
};
export const getConformityBorderColor = (percent: number) => {
  if (percent < 90) {
    return '#EAF6E7';
  } else if (90 < percent && percent < 100) {
    return '#F5F9E7';
  } else {
    return '#EAF6E7';
  }
};
// 项目等级颜色
export const getLevelColor = (level: string) => {
  const colorMap: Record<string, { color: string; bgColor: string }> = {
    C1: {
      color: '#5C208E',
      bgColor: '#E2E4FF',
    },
    C2: {
      color: '#C15500',
      bgColor: '#FFC89A',
    },
    C3: {
      color: '#4357D8',
      bgColor: '#B3D5FF',
    },
    C4: {
      color: '#268309',
      bgColor: '#BFEDAC',
    },
  };
  return colorMap[level] || { color: '#333', bgColor: '#FAFAFA' };
};
//
// 研发人员AI使用情况
export const getAiUsedColor = (percent: number) => {
  if (percent < 30) {
    return '#EE6060';
  } else if (percent < 80) {
    return '#FF7102';
  } else {
    return '#49CE20';
  }
};

// 根据分数获取一些水波图配置（过程符合度）
export const getConformityLiquidFillStageOptions = (percent: number) => {
  if (percent < 90) {
    return {
      // 对于差距较大的处理线文字颜色
      valueStyle: {
        // 将颜色转为rgba格式
        color: percent < 50 ? 'rgba(255, 73, 73, 0.8)' : 'rgba(251, 252, 255, 1)',
      },
      outline: {
        itemStyle: {
          color: 'rgba(253, 243, 243, 1)',
          borderColor: 'rgba(255, 73, 73, .05)',
        },
      },
      color: 'rgba(255, 73, 73, .8)',
    };
  } else if (90 <= percent && percent < 100) {
    return {
      valueStyle: {
        color: '#FBFCFF',
      },
      outline: {
        itemStyle: {
          color: 'rgba(253, 247, 243, 1)',
          borderColor: 'rgba(255, 113, 2, .05)',
        },
      },
      color: 'rgba(255, 125, 25, 1)',
    };
  } else {
    return {
      valueStyle: {
        color: '#FBFCFF',
      },
      outline: {
        itemStyle: {
          color: 'rgba(234, 246, 231, 1)',
          borderColor: '#EAF6E7',
        },
      },
      color: '#49CE20',
    };
  }
};
