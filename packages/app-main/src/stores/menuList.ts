import { useAsyncState, useLocalStorage } from '@vueuse/core';
import { defineStore } from 'pinia';
import { computed, watchEffect } from 'vue';

import api, { type MenuTreeItem } from '@/apis';

function flattenMenuTree(menuTree: MenuTreeItem[]): MenuTreeItem[] {
  const result: MenuTreeItem[] = [];

  function flatten(items: MenuTreeItem[]) {
    for (const item of items) {
      // 创建一个新对象，不包含 children 属性
      const { children, ...flatItem } = item;
      result.push(flatItem);

      // 如果有子项，递归处理
      if (children && children.length > 0) {
        flatten(children);
      }
    }
  }

  flatten(menuTree);
  return result;
}

export const storeKey = 'menu-list';
export const flatListKey = 'flat-menu-list';

export const useMenuList = defineStore(storeKey, () => {
  const { state, isLoading } = useAsyncState(async () => {
    const res = await api.tree();

    return res.data.data || [];
  }, []);

  const list = computed(() => state.value);

  const flatList = useLocalStorage<MenuTreeItem[]>(flatListKey, []);
  watchEffect(() => {
    flatList.value = flattenMenuTree(list.value);
  });

  return { list, flatList, isLoading };
});
