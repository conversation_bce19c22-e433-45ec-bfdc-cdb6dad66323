// 多步骤表单数据汇总 store（函数式写法）

import { defineStore } from 'pinia';
import type { PersistedStateOptions } from 'pinia-plugin-persistedstate';

import { type Project } from '@/apis/project/project';

export type Step1Form = {
  version?: string;
};
export type Step2Form = {
  componentIds: string[];
};
export type Step3Form = {
  resource: {
    name: string;
    cpuSize?: number;
    memorySize?: number;
    diskSize?: number;
    port: { name: string; port?: number }[];
    validDateRange?: [string, string];
  };
  middleware: string[];
};
const projectInfo = ref<Project | null>(null);
const defaultStep1Form = (): Step1Form => ({
  version: undefined,
});
const defaultStep2Form = (): Step2Form => ({
  componentIds: [],
});
export const defaultStep3Form = (): Step3Form => ({
  resource: {
    name: '',
    cpuSize: undefined,
    memorySize: undefined,
    diskSize: undefined,
    port: [],
    validDateRange: undefined,
  },
  middleware: [],
});
export const useStartDevFormStore = defineStore(
  'startDevForm',
  () => {
    const step1Form = ref<Step1Form>(defaultStep1Form());
    const step2Form = ref<Step2Form>(defaultStep2Form());
    const step3Form = ref<Step3Form>(defaultStep3Form());
    const fullFormInfo = computed(() => {
      return { ...step1Form.value, ...step2Form.value, ...step3Form.value };
    });
    function setProjectInfo(val: any) {
      projectInfo.value = val;
    }
    function setStep1Form(val: any) {
      step1Form.value = val;
    }

    function setStep2Form(val: any) {
      step2Form.value = val;
    }

    function setStep3Form(val: any) {
      step3Form.value = val;
    }

    function reset() {
      step1Form.value = defaultStep1Form();
      step2Form.value = defaultStep2Form();
      step3Form.value = defaultStep3Form();
      projectInfo.value = null;
    }

    return {
      step1Form,
      step2Form,
      step3Form,
      setStep1Form,
      setStep2Form,
      setStep3Form,
      projectInfo,
      setProjectInfo,
      fullFormInfo,
      reset,
    };
  },
  {
    persist: true as unknown as PersistedStateOptions,
  },
);
