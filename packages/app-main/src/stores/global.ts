import { defineStore } from 'pinia';
import { ref, computed } from 'vue';

// 全局标记，确保事件监听器只添加一次
let eventListenerAdded = false;

// 存放一些全局的信息
export const useGlobal = defineStore('login', () => {
  const asyncGetEncryptInfoDone = ref(false);
  
  // 确保事件监听器只添加一次
  if (!eventListenerAdded) {
    document.addEventListener('asyncGetEncryptInfoDone', () => {
      asyncGetEncryptInfoDone.value = true;
    });
    eventListenerAdded = true;
  }
  
  /** 是否登录并且获取到加密的info接口 */
  const hasLogin = computed(
    () => !!window.localStorage.getItem('zion-auth-value') && asyncGetEncryptInfoDone.value,
  );
  
  return { hasLogin };
});
