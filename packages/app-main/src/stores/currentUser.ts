import { useAsyncState } from '@vueuse/core';
import { defineStore } from 'pinia';

import api from '@/apis/index';

const isAloneAuth = import.meta.env.VITE_AUTH_TYPE === 'alone';

const storeKey = 'pinia-user';

export const useCurrentUser = defineStore(storeKey, () => {
  const currentUser = useAsyncState(async () => {
    if (isAloneAuth) return;

    const res = api.getCurrentUser();

    return res;
  }, null);

  return currentUser;
});
