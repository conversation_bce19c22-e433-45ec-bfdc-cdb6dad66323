import { useStorage } from '@vueuse/core';
import { defineStore } from 'pinia';

const storeKey = 'pinia-dark';

export const useCurrentDark = defineStore(storeKey, () => {
  const isDark = useStorage(storeKey, false);

  // @ts-ignore
  window.$wujie?.bus.$on('currentDarkChange', function (val: boolean) {
    isDark.value = val;
    document.documentElement.classList.toggle('dark', isDark.value);
  });

  return { isDark };
});
