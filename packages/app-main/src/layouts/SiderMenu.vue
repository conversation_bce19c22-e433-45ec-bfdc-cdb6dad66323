<script setup lang="tsx">
  import type { MenuInfo } from 'ant-design-vue/es/menu/src/interface';
  import { useRouter } from 'vue-router';

  import type { MenuTreeItem } from '@/apis';
  import MenuTree from '@/components/MenuTree/index.vue';

  const router = useRouter();
  const { menu, loading = false } = defineProps<{
    menu: MenuTreeItem[];
    loading?: boolean;
  }>();

  /** 菜单树点击事件 */
  const handleMenuClick = (info: MenuInfo) => {
    const path = info.item.path;

    if (typeof path !== 'string' || !path) {
      return;
    }

    router.push(path);
  };
</script>

<template>
  <MenuTree
    :menu="menu"
    :loading="loading"
    @click="handleMenuClick"
  />
</template>
