<script lang="ts" setup>
  import { computed } from 'vue';
  import { useRoute } from 'vue-router';

  import type { MenuTreeItem } from '@/apis';

  interface IProps {
    menus: MenuTreeItem[];
  }
  const props = withDefaults(defineProps<IProps>(), { menus: () => [] });
  const route = useRoute();
  const matchedParentChain = computed(() => {
    const activeMenu = route.meta?.menuKey || '';
    return findParentChain(props.menus, (item) => item.path === (activeMenu || route.path)) || [];
  });
  const parentChain = computed(() => {
    const _parentChain = matchedParentChain.value.filter((item) => !!item.name);
    return route.meta?.menuKey
      ? [
          ..._parentChain,
          {
            name: route.meta?.title,
            path: route.path,
          },
        ]
      : _parentChain;
  });
  /**
   *
   * @param tree 数状数据
   * @param filter 过滤函数
   * @param options 选项
   * @returns 返回满足过滤函数的元素在树状数据中的父级链
   */

  function findParentChain<T extends { children?: any[] }>(
    tree: T[],
    filter: (item: T) => boolean,
    options?: {
      parentChain?: T[];
    },
  ): T[] {
    const parentChain = options?.parentChain || [];

    if (!tree) return [];
    for (const data of tree) {
      parentChain.push(data);
      if (filter(data)) return parentChain;
      if (data.children) {
        const findChildren = findParentChain(data.children, filter, { parentChain });
        if (findChildren.length) return findChildren;
      }
      parentChain.pop();
    }
    return [];
  }
</script>

<template>
  <div class="breadcrumb-bar">
    <ABreadcrumb class="breadcrumb">
      <ABreadcrumbItem
        v-for="item in parentChain"
        :key="item.path"
        :href="item.path"
      >
        <RouterLink :to="item.path">
          {{ item.name }}
        </RouterLink>
        <!-- <template
          v-if="item.children?.length"
          #overlay
        >
          <AMenu>
            <template
              v-for="subitem in item.children"
              :key="subitem.url"
            >
              <AMenuItem class="breadcrumb-menu-item">
                <RouterLink :to="subitem.url">
                  <IconFont
                    v-if="subitem.icon"
                    :type="subitem.icon"
                    style="margin-right: 5px"
                  />
                  {{ subitem.title }}
                </RouterLink>
              </AMenuItem>
            </template>
          </AMenu>
        </template> -->
      </ABreadcrumbItem>
    </ABreadcrumb>
  </div>
</template>

<style lang="less" scoped>
  .breadcrumb-bar {
    display: flex;
    width: 100%;
    height: 30px;
    align-items: center;
    padding-left: 16px;
  }
  .trigger {
    font-size: 18px;
    cursor: pointer;
    transition: color 0.3s;
    color: #666;
    width: 20px;
    margin-right: 10px;
  }
  .trigger:hover {
    color: var(--ant-colorPrimary);
  }
  .home-ico {
    color: #d1e9ff;
    margin-right: 8px;
  }
  .breadcrumb-menu-item a.router-link-active {
    color: var(--ant-colorPrimary);
  }
</style>
