<script setup lang="ts">
  import { computed } from 'vue';

  const props = withDefaults(
    defineProps<{
      size: number;
      showTitle?: boolean;
    }>(),
    {
      size: 44,
      showTitle: true, // 设置默认值为 true
    },
  );

  /** 网站标题 */
  const siteTitle = computed(() => (import.meta.env.VITE_BASIC_PAGE_TITLE as string) || '');
</script>

<template>
  <div
    class="flex items-center justify-center border-b-1 border-b-[#E5E5E5] border-b-solid px-3 py-6 space-x-2"
  >
    <ATooltip
      v-if="!showTitle"
      placement="right"
    >
      <template #title>
        <span v-if="!showTitle">{{ siteTitle }}</span>
      </template>
      <img
        src=" /favicon.svg"
        :height="props.size"
      />
    </ATooltip>
    <img
      v-if="showTitle"
      src=" /favicon.svg"
      :height="props.size"
    />
    <div
      v-if="showTitle"
      class="font-DingTalk-JinBuTi cursor-default overflow-hidden text-ellipsis whitespace-nowrap text-[var(--ant-colorText)]"
      :style="{ fontSize: `${Math.floor(props.size * 0.75)}px` }"
    >
      {{ siteTitle }}
    </div>
  </div>
</template>
