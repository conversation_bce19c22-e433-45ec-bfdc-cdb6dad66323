<script setup lang="ts">
  import { MenuFoldOutlined, MenuUnfoldOutlined } from '@ant-design/icons-vue';
  import { useAsyncState, useToggle } from '@vueuse/core';
  import { ref } from 'vue';

  import api from '@/apis';
  import { useCurrentDark } from '@/stores/currentDark';

  import BreadCrumb from './BreadCrumb.vue';
  import HeaderBar from './HeaderBar.vue';
  import LogoTitle from './LogoTitle.vue';
  import SiderMenu from './SiderMenu.vue';

  /** 是否处于暗黑模式 */
  const currentDark = useCurrentDark();

  /** 头部区域高度 */
  const headerHeight = 54;

  /** 侧边区域展开宽度 */
  const siderWidth = 248;
  /** 侧边区域收起宽度 */
  const siderCollapsedWidth = 64;
  /** 侧边栏是否折叠 */
  const isSiderCollapsed = ref(false);
  /** 切换侧边栏折叠状态 */
  const toggleIsSiderCollapsed = useToggle(isSiderCollapsed);

  const { state, isLoading } = useAsyncState(api.tree, null);
</script>

<template>
  <ALayout class="h-[100vh]">
    <ALayoutSider
      v-model:collapsed="isSiderCollapsed"
      :width="siderWidth"
      :collapsed-width="siderCollapsedWidth"
      theme="light"
      class="top-0 h-screen shadow-[var(--ant-boxShadow)] !sticky"
      :class="{ 'border-r-solid border-r-[var(--ant-colorBorder)] border-r-1': currentDark.isDark }"
    >
      <div
        class="relative h-full flex flex-col"
        :style="{ paddingBottom: `${siderCollapsedWidth}px` }"
      >
        <LogoTitle
          :size="26"
          :show-title="!isSiderCollapsed"
        />
        <div class="z-10 flex-1 overflow-auto px-2 py-4">
          <SiderMenu
            :menu="state?.data.data || []"
            :loading="isLoading"
          />
        </div>
        <div
          class="absolute bottom-0 left-0 w-full flex justify-start border-t-1 border-t-[var(--ant-colorBorderSecondary)] border-t-solid py-3"
        >
          <div
            class="h-full flex items-center justify-center"
            :style="{ width: `${siderCollapsedWidth}px` }"
          >
            <AButton
              type="text"
              size="small"
              @click="toggleIsSiderCollapsed()"
            >
              <template #icon>
                <MenuUnfoldOutlined v-if="isSiderCollapsed" />
                <MenuFoldOutlined v-else />
              </template>
            </AButton>
          </div>
        </div>
      </div>
    </ALayoutSider>
    <ALayout
      class="bg"
      :style="{ paddingTop: `${headerHeight}px` }"
    >
      <ALayoutHeader
        class="fixed left-248px right-0 top-0 z-100 bg-transparent !border-0 !p-0 !leading-none"
        :style="{ height: `${headerHeight}px`, background: 'transparent !important' }"
      >
        <HeaderBar>
          <template #left>
            <BreadCrumb :menus="state?.data.data || []" />
          </template>
        </HeaderBar>
      </ALayoutHeader>
      <ALayoutContent>
        <div class="h-full overflow-y-auto p-[var(--ant-paddingLG)] pt-2">
          <slot />
        </div>
      </ALayoutContent>
    </ALayout>
  </ALayout>
</template>

<style lang="less" scoped>
  .bg {
    background: #fff url('@/assets/images/bg.png') no-repeat left top;
    background-size: cover;
  }
</style>
