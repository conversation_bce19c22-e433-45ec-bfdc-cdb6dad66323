<script setup lang="ts">
  import { computed, ref } from 'vue';

  import MainLayout from './MainLayout.vue';
  import { useGlobal } from '@/stores/global';
  const route = useRoute();
  // @ts-ignore
  const isWuJie = !!(window.__POWERED_BY_WUJIE__ as boolean | undefined);

  const isAloneAuth = import.meta.env.VITE_AUTH_TYPE === 'alone';

  const globalStore = useGlobal();
</script>

<template>
  <div v-show="globalStore.hasLogin || isWuJie">
    <ALayout v-if="isWuJie || isAloneAuth">
      <ALayoutContent>
        <UserPermissions
          type="page"
          v-bind="route.meta.pagePermission"
        >
          <RouterView />
        </UserPermissions>
      </ALayoutContent>
    </ALayout>

    <MainLayout v-else>
      <UserPermissions
        type="page"
        v-bind="route.meta.pagePermission"
      >
        <RouterView />
      </UserPermissions>
    </MainLayout>
  </div>
</template>
