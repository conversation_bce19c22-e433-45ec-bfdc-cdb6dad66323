<script setup lang="tsx">
  import { message } from 'ant-design-vue';
  import type { MenuInfo } from 'ant-design-vue/es/menu/src/interface';
  import { Base64 } from 'js-base64';
  import { useRouter } from 'vue-router';

  import { type MenuTreeItem } from '@/apis/code-generate/zion';
  import MenuTree from '@/components/MenuTree/index.vue';

  const router = useRouter();
  const isDev = import.meta.env.MODE === 'development';
  const handleTestForm = () => {
    router.push({
      name: 'micro-app--dynamic',
      params: { url: encodeURIComponent(Base64.encode('http://localhost:3009/')) },
    });
  };

  const props = withDefaults(
    defineProps<{
      siderCollapsed?: boolean;
    }>(),
    {
      siderCollapsed: false, // 设置默认值为 false
    },
  );

  const menuTreeFixedCls = computed(() => {
    return props.siderCollapsed
      ? 'slide-fixed-menu slide-fixed-menu-collapsed'
      : 'slide-fixed-menu';
  });

  /** 菜单树点击事件 */
  const handleMenuClick = (info: MenuInfo) => {
    const menuItem = info.item?.originServerData as MenuTreeItem | undefined;

    if (!menuItem) {
      message.error('获取菜单项数据失败，请联系管理员');
      return;
    }

    if (!menuItem.path) {
      message.error('菜单项未配置 path，请联系管理员');
      return;
    }

    if (menuItem.type === 3) {
      // 外链
      window.open(menuItem.path, '_blank');
      return;
    } else if (menuItem.isLink) {
      // 微前端
      router.push({
        name: 'micro-app--dynamic',
        params: { url: encodeURIComponent(Base64.encode(menuItem.path)) },
      });
      return;
    } else {
      // 普通路由
      router.push(menuItem.path);
    }
  };

  const fixedMenuList: MenuTreeItem[] = [
    {
      resourceId: '1879063500826931201',
      appId: null,
      name: '通用能力超市',
      code: '',
      permission: '',
      icon: 'AppstoreOutlined',
      path: '/component-market',
      sort: 1,
      visible: true,
      type: 1,
      parentId: '0',
      isLink: false,
      children: [],
    },
    // {
    //   resourceId: '1818180651007971330',
    //   appId: null,
    //   name: '代码生成',
    //   code: 'application',
    //   permission: 'application',
    //   icon: 'AppstoreAddOutlined',
    //   path: '',
    //   sort: 2,
    //   visible: true,
    //   type: 4,
    //   parentId: '0',
    //   isLink: false,
    //   children: [
    //     {
    //       resourceId: '1818181788977172482',
    //       appId: null,
    //       name: '前端开发',
    //       code: 'applicationDevelop',
    //       permission: 'application.develop',
    //       icon: 'AppstoreAddOutlined',
    //       path: '',
    //       sort: 2,
    //       visible: true,
    //       type: 4,
    //       parentId: '1818180651007971330',
    //       isLink: false,
    //       children: [
    //         {
    //           resourceId: '1818182928896724994',
    //           appId: null,
    //           name: '页面编排',
    //           code: 'applicationPage',
    //           permission: 'application.page',
    //           icon: 'FileAddOutlined',
    //           path: '/application/page',
    //           sort: 1,
    //           visible: true,
    //           type: 1,
    //           parentId: '1818181788977172482',
    //           isLink: false,
    //           children: [],
    //         },
    //       ],
    //     },
    //     {
    //       resourceId: '1818184203415687169',
    //       appId: null,
    //       name: '后端开发',
    //       code: 'applicationApiDev',
    //       permission: 'application.apiDev',
    //       icon: 'UsbOutlined',
    //       path: '',
    //       sort: 3,
    //       visible: true,
    //       type: 4,
    //       parentId: '1818180651007971330',
    //       isLink: false,
    //       children: [
    //         {
    //           resourceId: '1818194550289698818',
    //           appId: null,
    //           name: 'API列表',
    //           code: 'applicationApiOrchestration',
    //           permission: 'application.apiOrchestration',
    //           icon: 'ApiOutlined',
    //           path: '/application/api-orchestration',
    //           sort: 1,
    //           visible: true,
    //           type: 1,
    //           parentId: '1818184203415687169',
    //           isLink: false,
    //           children: [],
    //         },
    //         {
    //           resourceId: '1851083169266728962',
    //           appId: null,
    //           name: '数据模型',
    //           code: 'DataModelList',
    //           permission: 'data.model.list',
    //           icon: 'HddOutlined',
    //           path: '/application/data-model/list',
    //           sort: 1,
    //           visible: true,
    //           type: 1,
    //           parentId: '1818184203415687169',
    //           isLink: false,
    //           children: [],
    //         },
    //         // TODO：展示隐藏
    //         // {
    //         //   resourceId: '1851083685220646914',
    //         //   appId: null,
    //         //   name: '选项集',
    //         //   code: 'DataEnumList',
    //         //   permission: 'data.enum.list',
    //         //   icon: 'ControlOutlined',
    //         //   path: '/application/data-enum/list',
    //         //   sort: 1,
    //         //   visible: true,
    //         //   type: 1,
    //         //   parentId: '1818184203415687169',
    //         //   isLink: false,
    //         //   children: [],
    //         // },
    //         // {
    //         //   resourceId: '1881981772349648898',
    //         //   appId: null,
    //         //   name: '结构对象',
    //         //   code: '',
    //         //   permission: 'application.structure.list',
    //         //   icon: 'ApartmentOutlined',
    //         //   path: '/application/structure-manage/list',
    //         //   sort: 4,
    //         //   visible: true,
    //         //   type: 1,
    //         //   parentId: '1818184203415687169',
    //         //   isLink: false,
    //         //   children: [],
    //         // },
    //       ],
    //     },
    //   ],
    // },
    {
      resourceId: '1879063500826934911',
      appId: null,
      name: '源码导出',
      code: '',
      permission: '',
      icon: 'CloudDownloadOutlined',
      path: '/code-export',
      sort: 3,
      visible: true,
      type: 1,
      parentId: '0',
      isLink: false,
      children: [],
    },
  ];
</script>

<template>
  <MenuTree
    :class="menuTreeFixedCls"
    :menu="fixedMenuList"
    :loading="false"
    @click="handleMenuClick"
  />
</template>

<style lang="less" scoped>
  .slide-fixed-menu {
    :deep(.ant-menu-item) {
      padding-left: 14px !important;

      &.ant-menu-item-selected {
        background: rgba(49, 115, 255, 0.06);
        color: #4b5071;
      }

      .ant-menu-item-icon {
        padding: 5px;
        border-radius: 50%;
        color: #fff !important;
        font-weight: 600;
      }

      &:not(:last-child) {
        .ant-menu-item-icon {
          background-color: var(--ant-orange);
          border: solid 1px var(--ant-orange);
        }
      }

      &:last-child {
        .ant-menu-item-icon {
          background-color: rgba(0, 196, 162, 1);
          border: solid 1px rgba(0, 196, 162, 1);
        }
      }
    }

    &.slide-fixed-menu-collapsed {
      :deep(.ant-menu-item) {
        padding: 0 !important;
        text-align: center;

        .ant-menu-item-icon {
          position: relative;
          left: 5px;
          top: 1px;
        }
      }
    }
  }
</style>
