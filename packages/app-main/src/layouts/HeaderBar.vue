<script setup lang="ts">
  import { LogoutOutlined } from '@ant-design/icons-vue';
  import { message } from 'ant-design-vue';
  import type { MenuClickEventHandler } from 'ant-design-vue/es/menu/src/interface';
  import to from 'await-to-js';
  import { computed, ref } from 'vue';

  import AntTokenAsCssVariablesQueryButton from '@/components/AntTokenAsCssVariables/AntTokenAsCssVariablesQueryButton.vue';
  import { useCurrentDark } from '@/stores/currentDark';
  import { useCurrentUser } from '@/stores/currentUser';

  import api from '../apis/index';

  /** 是否处于暗黑模式 */
  const currentDark = useCurrentDark();

  /** 是否展示 Ant Design Token 快速查询 按钮 */
  const showAntTokenAsCssVariablesQueryButton = computed(
    () =>
      import.meta.env.VITE_SHOW_ANT_TOKEN_AS_CSS_VARIABLES_ON_DEV === 'true' && import.meta.env.DEV,
  );

  /** 用户信息 */
  const currentUser = useCurrentUser();

  /** 用户菜单 loading */
  const userMenuLoading = ref(false);
  /** 退出登录 */
  const handleLogout = async () => {
    userMenuLoading.value = true;
    const [err] = await to(api.logout());
    userMenuLoading.value = false;
    if (err) {
      return;
    }
    message.info('已退出登录');
    window.location.reload();
  };
  /** 点击用户菜单 */
  const handleUserMenuClick: MenuClickEventHandler = ({ key }) => {
    switch (key) {
      case 'logout':
        handleLogout();
        break;
      default:
        break;
    }
  };
</script>

<template>
  <div class="h-full flex items-center pl-6 pr-4">
    <slot name="left" />
    <!-- <LogoTitle :size="40" /> -->
    <div class="flex flex-1 items-center justify-end space-x-4">
      <template v-if="showAntTokenAsCssVariablesQueryButton">
        <span><AntTokenAsCssVariablesQueryButton /></span>
        <!-- <ADivider type="vertical" /> -->
      </template>

      <!-- <span><DarkSwitch v-model:checked="currentDark.isDark" /></span> -->
      <ADivider type="vertical" />

      <span>
        <ADropdown
          :disabled="userMenuLoading"
          trigger="click"
        >
          <AButton
            type="text"
            :loading="userMenuLoading"
            size="small"
          >
            <template #icon>
              <span class="relative w-[26px]">
                <div class="absolute right-0 h-[30px] w-[30px] -top-[20px]">
                  <img
                    v-if="currentUser.state?.data.data?.avatar"
                    :src="currentUser.state?.data.data?.avatar"
                    width="100%"
                    height="100%"
                    class="rounded-full"
                  />
                  <div
                    v-else
                    class="inline-block h-full w-full rounded-full bg-[var(--ant-colorTextQuaternary)]"
                  >
                    <img
                      src="./assets/images/user-default.jpg"
                      width="100%"
                      height="100%"
                      class="rounded-full"
                    />
                  </div>
                </div>
              </span>
            </template>
            <span class="ml-2">{{ currentUser.state?.data.data?.nikeName }}</span>
          </AButton>
          <template #overlay>
            <AMenu @click="handleUserMenuClick">
              <AMenuItem
                key="logout"
                danger
              >
                <span class="flex items-center">
                  <LogoutOutlined />
                  退出登录
                </span>
              </AMenuItem>
            </AMenu>
          </template>
        </ADropdown>
      </span>
    </div>
  </div>
</template>
