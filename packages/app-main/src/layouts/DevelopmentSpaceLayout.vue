<script setup lang="ts">
  import { MenuFoldOutlined, MenuUnfoldOutlined } from '@ant-design/icons-vue';
  import { useToggle } from '@vueuse/core';
  import { computed, ref } from 'vue';
  import { RouterView, useRoute } from 'vue-router';

  import { type IAppInfo } from '@/hooks/useAppidAndTenantid';
  import { headerHeight } from '@/layouts/config';
  import ProjectSelect from '@/views/project/components/ProjectSelect.vue';

  import SiderMenu from './DevelopmentSpaceSiderMenu.vue';
  import HeaderBar from './HeaderBar.vue';
  import LogoTitle from './LogoTitle.vue';
  import SiderMenuFixed from './SiderMenuFixed.vue';
  import { useGlobal } from '@/stores/global';
  const isAloneAuth = import.meta.env.VITE_AUTH_TYPE === 'alone';

  const { appId, updataAppInfo } = useAppidAndTenantid();

  const route = useRoute();
  const router = useRouter();
  const globalStore = useGlobal();
  
 
  /** 头部区域高度 */
  const cssHeaderHeight = `${headerHeight}px`;

  /** 侧边区域展开宽度 */
  const siderWidth = 248;
  /** 侧边栏收起宽度 */
  const siderCollapsedWidth = 64;
  /** 侧边栏是否折叠 */
  const isSiderCollapsed = ref(false);
  /** 切换侧边栏折叠状态 */
  const toggleIsSiderCollapsed = useToggle(isSiderCollapsed);

 
  const showMainLayout = computed(() => route.query.mainLayout !== 'none');
  // 登录成功，通知其他应用

  const authKey = localStorage.getItem('zion-auth-key');
  const authValue = localStorage.getItem('zion-auth-value');
  window.opener?.postMessage(
    {
      type: 'loginSuccess',
      authKey,
      authValue,
    },
    '*',
  );
  console.log('登录成功，已获取token');

  const changeSelectApp = (appInfo: IAppInfo) => {
    updataAppInfo(appInfo);
    router.replace({ ...route });
    // location.reload();
  };

  const alertMsg = ref(true);
</script>

<template>
  <div v-show="globalStore.hasLogin || isAloneAuth">
    <ALayout v-if="showMainLayout">
      <ALayoutSider
        v-if="route.meta?.excludesSider"
        :width="0"
        theme="light"
      >
        <div class="h-screen"></div>
      </ALayoutSider>
      <ALayoutSider
        v-else
        :collapsed="isSiderCollapsed"
        :width="siderWidth"
        :collapsed-width="siderCollapsedWidth"
        theme="light"
        class="top-0 h-screen border-r-1 border-r-[var(--ant-colorBorderSecondary)] border-r-solid !sticky"
      >
        <div
          class="relative h-full flex flex-col"
          :style="{ paddingBottom: `48px` }"
        >
          <div
            class="relative z-10 flex flex-1 flex-col items-start justify-between overflow-hidden py-0"
          >
            <div
              class="w-full flex flex-1 flex-col"
              :class="[alertMsg ? 'max-h-[calc(100%-160px)]' : 'max-h-[calc(100%-106px)]']"
            >
              <LogoTitle
                class="w-full"
                :size="26"
                :show-title="!isSiderCollapsed"
              />
              <div
                class="w-full pt-7px"
                :class="[isSiderCollapsed ? 'px-0' : 'px-16px']"
              >
                <ProjectSelect
                  v-show="isSiderCollapsed"
                  v-model:value="appId"
                  :trigger="isSiderCollapsed ? 'hover' : 'click'"
                  :placement="isSiderCollapsed ? 'right' : 'right'"
                  :bordered="false"
                  :collapsed="isSiderCollapsed"
                  :query="{
                    devStates: '1',
                    stages: '1935665310006820868',
                  }"
                  mode="dropDown"
                  @select-item="changeSelectApp"
                ></ProjectSelect>
                <ADivider class="my-7px" />
              </div>
              <div class="min-h-[40px] w-full flex-1 overflow-auto">
                <SiderMenu :key="appId" />
              </div>
            </div>
            <div class="w-full px-8px">
              <ADivider
                class="mb-0 mt-7px"
                :class="{ 'mb-3px': !isSiderCollapsed }"
              />
              <div class="sider-menu-fixed">
                <div
                  v-show="!isSiderCollapsed"
                  :class="{ 'min-h-4': isSiderCollapsed }"
                >
                  <AAlert
                    :message="
                      isSiderCollapsed ? '' : '此区域内容为辅助预览功能，不会导出到实际源码中'
                    "
                    banner
                    closable
                    type="info"
                    @close="alertMsg = false"
                  />
                </div>
                <SiderMenuFixed :sider-collapsed="isSiderCollapsed" />
              </div>
            </div>
          </div>
          <div
            class="absolute bottom-0 left-0 w-full flex justify-start border-t-1 border-t-[var(--ant-colorBorderSecondary)] border-t-solid py-3"
          >
            <div
              class="h-full flex items-center justify-center"
              :style="{ width: `${siderCollapsedWidth}px` }"
            >
              <AButton
                type="text"
                size="small"
                @click="toggleIsSiderCollapsed()"
              >
                <template #icon>
                  <MenuUnfoldOutlined v-if="isSiderCollapsed" />
                  <MenuFoldOutlined v-else />
                </template>
              </AButton>
            </div>
          </div>
        </div>
      </ALayoutSider>
      <ALayout
        class="bg h-[100vh]"
        :style="{ paddingTop: `${headerHeight}px` }"
      >
        <ALayoutHeader
          class="fixed left-0 right-0 top-0 z-100 border-b-1 border-b-[var(--ant-colorBorderSecondary)] border-b-solid important-bg-transparent !p-0 !leading-none"
          :style="{ height: `${headerHeight}px`, left: `${siderWidth}px` }"
        >
          <HeaderBar />
        </ALayoutHeader>
        <ALayoutContent
          :key="appId"
          class="content h-[calc(100%-74px)] overflow-y-auto p-[var(--ant-paddingLG)] pt-2"
        >
          <RouterView />
        </ALayoutContent>
      </ALayout>
    </ALayout>
    <RouterView v-else />
  </div>
</template>

<style lang="less" scoped>
  :deep(.content) {
    .router-tab__header {
      position: sticky;
      top: v-bind(cssHeaderHeight);
    }
  }

  .bg {
    background: #fff url('@/assets/images/bg.png') no-repeat left top;
    background-size: cover;
  }

  .sider-menu-fixed {
    &:deep(.ant-alert) {
      align-items: flex-start;
    }

    &:deep(.ant-alert .ant-alert-icon) {
      padding-top: 4px;
    }

    &:deep(.ant-alert .ant-alert-close-icon) {
      padding-top: 4px;
    }

    &:deep(.ant-alert-info) {
      background-color: transparent;

      .ant-alert-icon {
        color: var(--ant-colorTextTertiary);
      }

      .ant-alert-message {
        font-size: 12px;
      }
    }
  }
</style>
