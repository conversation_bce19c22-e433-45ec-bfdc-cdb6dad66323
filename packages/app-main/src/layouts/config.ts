// 是否启用路由 tab 切换栏
const useRouterTab = import.meta.env.VITE_USE_ROUTERTAB === 'true';

/** 路由 tab 切换栏区域高度 */
export const routerTabHeight = 40;

/** 头部区域高度 */
export const headerHeight = 54;

/** 上下padding区域高度 24*2 */
export const paddingHeight = 48;

/** main 右侧滚动区域高度 */
export const cssMainScrollHeight = useRouterTab
  ? `${routerTabHeight + headerHeight + paddingHeight}px`
  : `${headerHeight + paddingHeight}px`;
