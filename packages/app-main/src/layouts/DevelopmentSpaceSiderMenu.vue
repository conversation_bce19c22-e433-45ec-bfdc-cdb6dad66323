<script setup lang="tsx">
  import { useAsyncState, useEventListener } from '@vueuse/core';
  import { message } from 'ant-design-vue';
  import type { MenuInfo } from 'ant-design-vue/es/menu/src/interface';
  import { Base64 } from 'js-base64';
  import { useRouter } from 'vue-router';

  import codeGenerateZionApi, { type MenuTreeItem } from '@/apis/code-generate/zion';
  import MenuTree from '@/components/MenuTree/index.vue';

  const router = useRouter();
  const { appId } = useAppidAndTenantid();
  const { state, isLoading, execute } = useAsyncState(
    async () => {
      const res = await codeGenerateZionApi.getAppResourceTree(appId.value);
      const menuData = res.data.data || [];
      return menuData;
    },
    [],
    { resetOnExecute: false },
  );
  const isDev = import.meta.env.MODE === 'development';
  useEventListener(window, 'updateMenu', () => {
    execute();
  });
  const openOnlineDev = () => {
    router.push({
      name: 'micro-app--dynamic',
      params: { url: encodeURIComponent(Base64.encode('http://localhost:3009')) },
    });
  };
  /** 菜单树点击事件 */
  const handleMenuClick = (info: MenuInfo) => {
    const menuItem = info.item?.originServerData as MenuTreeItem | undefined;

    if (!menuItem) {
      message.error('获取菜单项数据失败，请联系管理员');
      return;
    }

    if (!menuItem.path) {
      message.error('菜单项未配置 path，请联系管理员');
      return;
    }

    if (menuItem.type === 3) {
      // 外链
      window.open(menuItem.path, '_blank');
      return;
    } else if (menuItem.isLink) {
      // 微前端
      router.push({
        name: 'micro-app--dynamic',
        params: { url: encodeURIComponent(Base64.encode(menuItem.path)) },
      });
      return;
    } else {
      // 普通路由
      router.push(menuItem.path);
    }
  };
</script>

<template>
  <ASpin
    class="min-h-140px w-full flex items-center justify-center"
    :spinning="isLoading"
  >
    <MenuTree
      v-if="state?.length && appId"
      class="px-8px"
      :inline-indent="18"
      :menu="state || []"
      :loading="isLoading"
      @click="handleMenuClick"
    />
    <AEmpty
      v-if="(!appId || !state?.length) && !isLoading"
      class="font-32px mt-50% scale-70"
    ></AEmpty>
  </ASpin>
</template>

<style lang="less" scoped></style>
