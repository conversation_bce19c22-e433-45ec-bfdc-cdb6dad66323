import { useStorage, watchImmediate } from '@vueuse/core';
import { clone, isDeepEqual } from 'remeda';

export interface IAppInfo {
  appId?: string;
  sysAppKey?: string;
}

export default () => {
  const appId = useStorage('appId', '', window.sessionStorage); // 工作空间项目Id
  const sysAppKey = useStorage('sysAppKey', '', window.sessionStorage);
  const route = useRoute();
  const router = useRouter();

  const updataAppInfo = (appInfo: IAppInfo) => {
    /** 尝试获取appId */
    if (appInfo.appId && typeof appInfo.appId === 'string') {
      appId.value = appInfo.appId;
    }
    /** 尝试获取sysAppKey */
    if (appInfo.sysAppKey && typeof appInfo.sysAppKey === 'string') {
      sysAppKey.value = appInfo.sysAppKey;
    }
  };

  watchImmediate(appId, (val) => {
    console.log('appId changed', val);
  });

  updataAppInfo(route.query);
  /** 删除appId和sysAppKey */
  const currentRouteQuery = route.query;
  const clearedRouteQuery = clone(currentRouteQuery);
  delete clearedRouteQuery.appId;
  delete clearedRouteQuery.sysAppKey;
  if (!isDeepEqual(currentRouteQuery, clearedRouteQuery)) {
    setTimeout(() => {
    router.replace({
      ...route,
      query: {
        ...route.query,
        appId: undefined,
        sysAppKey: undefined,
        },
      });
    }, 1000);
  }

  if (!appId.value && route.path !== '/home') {
    router.push('/project');
  }

  return {
    appId,
    sysAppKey,
    updataAppInfo,
  };
};
