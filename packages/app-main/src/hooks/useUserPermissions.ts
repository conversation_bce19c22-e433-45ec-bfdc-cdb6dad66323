import { reactiveComputed, useAsyncState } from '@vueuse/core';
import { type Ref, ref, unref, watchEffect } from 'vue';

import api from '@/apis';

const userPermissionsState = useAsyncState(
  async () => {
    const { data } = await api.getUserPermissions();
    return data.data || [];
  },
  [],
  { immediate: false },
);

const userPermissionsData = reactiveComputed(() => ({
  ...userPermissionsState,
  state: undefined,
  value: userPermissionsState.state.value,
}));

/** 获取权限标识原始数据 */
export const useUserPermissionsData = () => {
  if (!userPermissionsState.isReady.value && !userPermissionsState.isLoading.value) {
    userPermissionsState.execute();
  }

  return userPermissionsData;
};

/** 查询用户是否存在单个权限码 */
export const useUserPermission = (userPermission?: string | Ref<string | undefined>) => {
  // 创建一个响应式引用来存储结果
  const result = ref<boolean | null>(null);

  watchEffect(() => {
    const permission = unref(userPermission);

    if (!userPermissionsData.isReady || !permission) {
      result.value = null;
      return;
    }

    if (userPermissionsData.value.includes('*')) {
      result.value = true;
      return;
    }

    result.value = userPermissionsData.value.includes(permission);
  });

  return result;
};

/** 查询用户是否同时存在多个权限码 */
export const useUserPermissionsEvery = (userPermissions?: string[] | Ref<string[] | undefined>) => {
  const result = ref<boolean | null>(null);

  watchEffect(() => {
    const permissions = unref(userPermissions);

    if (!userPermissionsData.isReady || !permissions) {
      result.value = null;
      return;
    }

    if (userPermissionsData.value.includes('*')) {
      result.value = true;
      return;
    }

    result.value = permissions.every((permission) =>
      userPermissionsData.value.includes(permission),
    );
  });

  return result;
};

/** 查询用户是否存在多个权限码之一 */
export const useUserPermissionsSome = (userPermissions?: string[] | Ref<string[] | undefined>) => {
  const result = ref<boolean | null>(null);

  watchEffect(() => {
    const permissions = unref(userPermissions);

    if (!userPermissionsData.isReady || !permissions) {
      result.value = null;
      return;
    }

    if (userPermissionsData.value.includes('*')) {
      result.value = true;
      return;
    }

    result.value = permissions.some((permission) => userPermissionsData.value.includes(permission));
  });

  return result;
};

/** 查询当前用户是否是超管账户 */
export const useUserPermissionSuperAdmin = () => {
  const result = ref<boolean | null>(null);

  watchEffect(() => {
    if (!userPermissionsData.isReady) {
      result.value = null;
      return;
    }

    if (userPermissionsData.value.includes('*')) {
      result.value = true;
      return;
    }

    result.value = false;
  });

  return result;
};
