import { ref, onMounted } from 'vue';

interface UseAntProgressAnimateOptions {
  targetValue: number;
  duration?: number;
  delay?: number;
}
/**
 * Ant Design Progress 组件动画 Hook
 * 
 * 用于为 Ant Design 的 Progress 组件提供平滑的数值动画效果
 * 支持圆形进度条和线性进度条的数值动画
 * 
 * @example
 * ```vue
 * <template>
 *   <AProgress 
 *     type="circle" 
 *     :percent="animatedValue" 
 *     :size="190"
 *   />
 * </template>
 * 
 * <script setup>
 * const { animatedValue } = useAntProgressAnimate({
 *   targetValue: 75.5,
 *   duration: 800
 * });
 * </script>
 * ```
 */

export function useAntProgressAnimate(options: UseAntProgressAnimateOptions) {
  const { targetValue, duration = 600, delay = 0 } = options;
  
  const animatedValue = ref(0);

  onMounted(() => {
    // 延迟开始动画
    setTimeout(() => {
      const startTime = Date.now();
      const startValue = 0;
      const endValue = targetValue;
      
      const animate = () => {
        const currentTime = Date.now();
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        // 匀速增长
        const currentValue = startValue + (endValue - startValue) * progress;
        
        animatedValue.value = Math.round(currentValue * 10) / 10;
        
        if (progress < 1) {
          requestAnimationFrame(animate);
        }
      };
      
      requestAnimationFrame(animate);
    }, delay);
  });

  return {
    animatedValue
  };
} 