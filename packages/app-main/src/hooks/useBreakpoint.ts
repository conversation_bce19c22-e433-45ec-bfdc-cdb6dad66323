import { ref, onMounted, onUnmounted, type Ref } from 'vue'

export interface Breakpoint {
  name: string
  width: number
}

export interface BreakpointResult {
  currentBreakpoint: string
  isMobile: boolean
  isTablet: boolean
  isDesktop: boolean
  isLargeDesktop: boolean
  isCustom: (breakpointName: string) => boolean
  isLessThan: (breakpointName: string) => boolean
  width: Ref<number>
}

export function useBreakpoint(customBreakpoints: Breakpoint[] = []): BreakpointResult {
  const width = ref(0)
  const currentBreakpoint = ref('')

  // 默认断点
  const defaultBreakpoints: Breakpoint[] = [
    { name: 'mobile', width: 768 },
    { name: 'tablet', width: 1024 },
    { name: 'desktop', width: 1366 },
    { name: '1440', width: 1440 },
    { name: '1600', width: 1600 },
    { name: '1920', width: 1920 }
  ]

  // 合并自定义断点和默认断点
  const allBreakpoints = [...defaultBreakpoints, ...customBreakpoints].sort((a, b) => a.width - b.width)

  const updateBreakpoint = () => {
    width.value = window.innerWidth
    
    // 找到当前宽度对应的断点
    let breakpoint = allBreakpoints[0]
    for (let i = allBreakpoints.length - 1; i >= 0; i--) {
      if (width.value >= allBreakpoints[i].width) {
        breakpoint = allBreakpoints[i]
        break
      }
    }
    
    currentBreakpoint.value = breakpoint.name
  }

  const isCustom = (breakpointName: string): boolean => {
    return currentBreakpoint.value === breakpointName
  }

  const isLessThan = (breakpointName: string): boolean => {
    const breakpoint = allBreakpoints.find(bp => bp.name === breakpointName);
    return breakpoint ? width.value < breakpoint.width : false;
  }

  onMounted(() => {
    updateBreakpoint()
    window.addEventListener('resize', updateBreakpoint)
  })

  onUnmounted(() => {
    window.removeEventListener('resize', updateBreakpoint)
  })

  return {
    currentBreakpoint: currentBreakpoint.value,
    isMobile: width.value < 768,
    isTablet: width.value >= 768 && width.value < 1024,
    isDesktop: width.value >= 1024 && width.value < 1366,
    isLargeDesktop: width.value >= 1366,
    isCustom,
    isLessThan,
    width
  }
} 