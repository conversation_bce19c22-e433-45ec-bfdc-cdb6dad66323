import type { antIconName } from '@/components/AntIconRender/AntIconRender';

import request, { type ResponseWrap } from '../requests/request';

export interface MenuTreeItem {
  resourceId: string;
  appId: null;
  name: string;
  code: null | string;
  permission: string;
  icon: antIconName;
  path: string;
  sort: number;
  visible: true;
  type: number;
  parentId: string;
  isLink: boolean;
  children?: MenuTreeItem[];
}

export interface TokenInfo {
  prefix: string;
  token: string;
  tokenName: string;
}

export default {
  /** 获取演示用户应用资源树 */
  getAppResourceTree: (programmeId: string) =>
    request.get<ResponseWrap<MenuTreeItem[]>>('/web/v1/zion/getAppResourceTree', {
      params: { programmeId },
    }),

  /** 获取演示用户token信息 */
  token: (programmeId: string) =>
    request.get<ResponseWrap<TokenInfo>>('/web/v1/zion/token', {
      params: { programmeId },
    }),
};
