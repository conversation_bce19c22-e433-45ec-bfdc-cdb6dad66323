import request, { type ResponseWrap as PlatformResponseWrap } from '../requests/request';

// 审批模式
export enum APPROVE_MODE {
  COUNTER = 'counterSignature', // 会签
  EDIHER = 'eitherSignature', // 或签
}
export enum APPROVE_MODE_STRS {
  COUNTER = '会签',
  EDIHER = '或签',
}
export const ApproveModes: { [key: string]: string } = {
  [APPROVE_MODE.COUNTER]: APPROVE_MODE_STRS.COUNTER,
  [APPROVE_MODE.EDIHER]: APPROVE_MODE_STRS.EDIHER,
};
export const ApproveModeColors: { [key: string]: string } = {
  [APPROVE_MODE.COUNTER]:
    'linear-gradient(44deg, rgba(0, 196, 162, 1) 0%, rgba(17, 239, 200, 1) 100%)',
  [APPROVE_MODE.EDIHER]:
    'linear-gradient(44deg, rgba(37, 129, 255, 1) 0%, rgba(74, 191, 255, 1) 100%)',
};

// 审批业务类型
export enum APPROVE_TYPE {
  RESOURCE = 'resource', // 资源申请审批
  PROCESS = 'process', // 阶段推进审批
  CROPPING = 'programmeAgileCropping', // 项目过程裁剪审批
}
export enum APPROVE_TYPE_STRS {
  RESOURCE = '资源申请',
  PROCESS = '阶段推进',
  CROPPING = '过程裁剪',
}
export const ApproveTypes: { [key: string]: string } = {
  [APPROVE_TYPE.RESOURCE]: APPROVE_TYPE_STRS.RESOURCE,
  [APPROVE_TYPE.PROCESS]: APPROVE_TYPE_STRS.PROCESS,
  [APPROVE_TYPE.CROPPING]: APPROVE_TYPE_STRS.CROPPING,
};
export const ApproveTypeColors: { [key: string]: string } = {
  [APPROVE_TYPE.RESOURCE]: 'green',
  [APPROVE_TYPE.PROCESS]: 'blue',
  [APPROVE_TYPE.CROPPING]: 'pink',
};
export const ApproveTypeList = Object.entries(APPROVE_TYPE).map(([key, value]) => ({
  value: value,
  label: APPROVE_TYPE_STRS[key as keyof typeof APPROVE_TYPE_STRS],
  color: ApproveTypeColors[value as keyof typeof ApproveTypeColors],
}));

// 审批状态
export enum APPROVE_STATUS {
  INIT = 'init',
  PROCESSING = 'processing',
  REJECT = 'reject',
  PASS = 'pass',
}
export enum APPROVE_STATUS_STRS {
  INIT = '待审批',
  PROCESSING = '审批中',
  REJECT = '审批驳回',
  PASS = '审批通过',

  REFUSE = '拒绝',
  AGREE = '同意',
}

export const ApproveFlowStatus: any = {
  [APPROVE_STATUS.INIT]: 'wait',
  [APPROVE_STATUS.PROCESSING]: 'process',
  [APPROVE_STATUS.REJECT]: 'error',
  [APPROVE_STATUS.PASS]: 'finish',
};
export const ApproveStatus: { [key: string]: string } = {
  [APPROVE_STATUS.INIT]: APPROVE_STATUS_STRS.INIT,
  [APPROVE_STATUS.PROCESSING]: APPROVE_STATUS_STRS.PROCESSING,
  [APPROVE_STATUS.REJECT]: APPROVE_STATUS_STRS.REJECT,
  [APPROVE_STATUS.PASS]: APPROVE_STATUS_STRS.PASS,
};
export const ApproveStatusTag: { [key: string]: string } = {
  [APPROVE_STATUS.INIT]: APPROVE_STATUS_STRS.INIT,
  [APPROVE_STATUS.PROCESSING]: APPROVE_STATUS_STRS.PROCESSING,
  [APPROVE_STATUS.REJECT]: APPROVE_STATUS_STRS.REFUSE,
  [APPROVE_STATUS.PASS]: APPROVE_STATUS_STRS.AGREE,
};
export const ApproveStatusColors: { [key: string]: string } = {
  [APPROVE_STATUS.INIT]: 'warning',
  [APPROVE_STATUS.PROCESSING]: 'processing',
  [APPROVE_STATUS.REJECT]: 'error',
  [APPROVE_STATUS.PASS]: 'success',
};
export const ApproveStatusDotColors: { [key: string]: string } = {
  [APPROVE_STATUS.INIT]: 'var(--ant-colorWarning)',
  [APPROVE_STATUS.PROCESSING]: 'var(--ant-colorPrimary)',
  [APPROVE_STATUS.REJECT]: 'var(--ant-colorError)',
  [APPROVE_STATUS.PASS]: 'var(--ant-colorSuccess)',
};
export const ApproveStatusList = Object.entries(APPROVE_STATUS).map(([key, value]) => ({
  value: value,
  label: APPROVE_STATUS_STRS[key as keyof typeof APPROVE_STATUS_STRS],
  color: ApproveStatusColors[value as keyof typeof ApproveStatusColors],
}));

export interface FlowDetailParams {
  approvalOpinion?: any; // 审批意见;
  approvalState?: `${APPROVE_STATUS}`; // 审批状态
  approvalTime?: any; // 审批时间;
  approverId?: any; // 审批人id;
  approverName: any; // 审批人;
  id?: any; // 审批记录id;
}

export interface ListParams {
  pageIndex?: number;
  pageSize?: number;
  bizName?: any; // 审批名称
  approvalState?: any; // 审批状态
  bizType?: any; // 审批业务类型
}

export interface ListResponseItem {
  id?: any; // 主键id
  bizId?: any; // 审批业务id
  bizName?: any; // 审批业务名称
  bizParams?: any; // 审批业务参数
  bizType?: any; // 审批业务类型
  createTime?: any; // 提交时间
  hasPermission?: any; // 是否有权限
  submitter?: any; // 提交人id
  submitterName?: any; // 提交人姓名
  updateTime?: any; // 处理时间
  [key: string]: any;
}

export interface ApproveParams {
  id?: any; // 主键id
  approvalState?: any; // 审批状态
  approvalOpinion?: string; // 审批意见
  [key: string]: any;
}

export default {
  /** 列表 */
  list: (params: ListParams) =>
    request.post<PlatformResponseWrap<ListResponseItem[]>>('/web/v1/common/approval/page', params),

  /** 详情 */
  detail: (id: string) =>
    request.post<PlatformResponseWrap<ListResponseItem>>(`/web/v1/common/approval/detail//${id}`),

  /** 审批 */
  approve: (params: ApproveParams) => {
    return request.put<PlatformResponseWrap<any>>('/web/v1/common/approval', params);
  },
};
