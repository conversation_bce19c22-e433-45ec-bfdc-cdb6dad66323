import { APPROVE_STATUS } from '@/apis/code-generate/approval';

import request, { type ResponseWrap as PlatformResponseWrap } from '../requests/request';

// 资源状态
export enum RESOURCE_STATUS {
  NOT = 'not',
  OPEN = 'open',
  RELEASE = 'release',
}
export enum RESOURCE_STATUS_STRS {
  NOT = '未开通',
  OPEN = '已开通',
  RELEASE = '已释放',
}
export const ResourceStatus: { [key: string]: string } = {
  [RESOURCE_STATUS.NOT]: RESOURCE_STATUS_STRS.NOT,
  [RESOURCE_STATUS.OPEN]: RESOURCE_STATUS_STRS.OPEN,
  [RESOURCE_STATUS.RELEASE]: RESOURCE_STATUS_STRS.RELEASE,
};
export const ResourceStatusColors: { [key: string]: string } = {
  [RESOURCE_STATUS.NOT]: 'error',
  [RESOURCE_STATUS.OPEN]: 'processing',
  [RESOURCE_STATUS.RELEASE]: 'default',
};
export const ResourceStatusList = Object.entries(RESOURCE_STATUS).map(([key, value]) => ({
  value: value,
  label: RESOURCE_STATUS_STRS[key as keyof typeof RESOURCE_STATUS_STRS],
  color: ResourceStatusColors[value as keyof typeof ResourceStatusColors],
}));

export interface FlowItemDetail {
  approvalOpinion?: any; // 审批意见;
  approvalState?: `${APPROVE_STATUS}`; // 审批状态
  approvalTime?: any; // 审批时间;
  approverId?: any; // 审批人id;
  approverName: any; // 审批人;
  id?: any; // 审批记录id;
}
export interface FlowDetailParams {
  approvalMode?: any; // 审批模式;
  approvalRecordList?: FlowItemDetail[]; // 审批列表;
}

export interface ListParams {
  pageIndex?: number;
  pageSize?: number;
  department?: any; // 部门名称
  approvalState?: any; // 审批状态
  programmeName?: any; // 项目名称
}

export interface ListResponseItem {
  id?: any; // 主键id
  department?: string; // 部门名称
  approvalState?: any; // 审批状态
  programmeName?: any; // 项目名称
  programmeTag?: any; // 项目标识
  submitter?: any; // 申请人
  createTime?: any; // 申请时间
  [key: string]: any;
}

export interface EditParams extends ListResponseItem {
  [key: string]: any;
}

export interface DetailResponse extends ListResponseItem {
  [key: string]: any;
}

export interface ApproveParams {
  id?: any; // 主键id
  approvalState?: any; // 审批状态
  approvalOpinion?: string; // 审批意见
  [key: string]: any;
}

export default {
  /** 列表 */
  list: (params: ListParams) =>
    request.post<PlatformResponseWrap<ListResponseItem[]>>(
      '/web/v1/resource/apply/approval/page',
      params,
    ),

  /** 明细 */
  detail: (id: string) =>
    request.get<PlatformResponseWrap<ListResponseItem>>(`/web/v1/resource/apply/approval/${id}`),

  /** 明细 */
  approve: (params: ApproveParams) => {
    return request.put<PlatformResponseWrap<any>>('/web/v1/common/approval', params);
  },

  /** 明细 */
  approveList: (id: string) =>
    request.get<PlatformResponseWrap<FlowDetailParams>>(
      `/web/v1/resource/apply/approval/record/${id}`,
    ),

  /** 资源申请状态变更 */
  resourceStatusChange: (params: { bizId: string; openState: RESOURCE_STATUS }) => {
    return request.put<PlatformResponseWrap<any>>(
      `/web/v1/resource/apply/approval/record/state?bizId=${params.bizId}&openState=${params.openState}`,
    );
  },

  /** 资源(中间件)列表 */
  resourceList: () => request.get<PlatformResponseWrap<any>>('/web/v1/resource/list'),
  /** 根据项目id获取资源申请 */
  resourceByProgrammeId: (programmeId: string) => request.get<PlatformResponseWrap<any>>(`/web/v1/resource/apply/approval/byProgrammeId/${programmeId}`),
  /** 资源申请 */
  resourceApply: (params: any) =>
    request.post<PlatformResponseWrap<any>>('/web/v1/resource/apply/approval', params),
};
