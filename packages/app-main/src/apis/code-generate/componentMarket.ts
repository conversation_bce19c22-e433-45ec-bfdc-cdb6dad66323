import type { AxiosProgressEvent } from 'axios';

import request, { type ResponseWrap } from '../requests/request';

export interface ListItem {
  id: string;
  name: string;
  type: string;
  typeName: string;
  description: string;
  intro: string;
  installCount: number;
  version: string;
  isOfficial: boolean;
  tags: string[];
  isInstalled: boolean;
  imgUrl: string[];
  icon: string;
  fileSize: string;
  classify: string;
  publishTime: string;
  createTime: string;
  updateTime: string;
  manager: string;
  contact: string;
  localInstallCount: number;
  index?: number;
  [propName: string]: any;
}

interface PageQueryParams {
  groupBy?: string;
  name?: string;
  needTotalCount?: boolean;
  orderBy?: string;
  orderDirection?: string;
  pageIndex: number;
  pageSize: number;
  programmeId: string;
  type?: string;
}

interface IncrementalMergeParams {
  codeType: string;
  componentId?: string;
  incrementType: string;
  programmeId: string;
}

export default {
  /** 详情查询 */
  detail: ({ id, programmeId }: { id: string; programmeId: string }) =>
    request.get<ResponseWrap<ListItem>>(`/web/v1/component/market/detail/${id}`, {
      params: { programmeId },
    }),

  /** 组件分页查询 */
  pageQuery: (params: PageQueryParams) =>
    request.post<ResponseWrap<ListItem[]>>('/web/v1/component/market/pageQuery', params),

  /** 组件类型枚举查询 */
  typeEnum: () =>
    request.get<ResponseWrap<{ key: string; value: string }[]>>(
      '/web/v1/component/market/type/enum',
    ),

  /** 安装组件 */
  install: ({ id, programmeId }: { id: string; programmeId: string }) =>
    request.put<
      ResponseWrap<{
        errCode: string;
        errMessage: string;
        success: boolean;
      }>
    >(`/web/v1/component/market/install/${id}`, undefined, { params: { programmeId } }),

  /** 卸载组件 */
  uninstall: ({ id, programmeId }: { id: string; programmeId: string }) =>
    request.delete<
      ResponseWrap<{
        errCode: string;
        errMessage: string;
        success: boolean;
      }>
    >(`/web/v1/component/market/uninstall/${id}`, { params: { programmeId } }),

  /** 增量组件代码生成 */
  incremental: (
    params: { componentId: string; exportType: string; programmeId: string },
    onDownloadProgress: (progressEvent: AxiosProgressEvent) => void,
  ) =>
    request.post('/web/v1/component/market/install/incremental', params, {
      responseType: 'blob',
      onDownloadProgress,
    }),

  /** 新增增量代码合并记录 */
  incrementalMerge: (params: IncrementalMergeParams) =>
    request.post('/web/v1/component/market/incremental/merge', params),

  /** 获取代码合并本地客户端接口签名 */
  signSecretKey: () =>
    request.get<ResponseWrap<string>>('/web/v1/component/market/incremental/sign/secretKey'),
};
