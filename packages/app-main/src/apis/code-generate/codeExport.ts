import request, { type ResponseWrap } from '../requests/request';

export interface ListItem {
  /** 组件名列表 */
  componentNameList?: string[];
  /** 创建时间 */
  createTime?: string;
  /** 导出类型 web-前端代码,backend-后端代码 */
  exportType?: string;
  /** 主键 */
  id: string;
  /** 底座版本 */
  platformVersion?: string;
  /** 项目工程名 */
  projectName?: string;
  /** 导出状态(init-初始化，codeGenerating-底座代码生成中，codeAssembling-平台生成代码组装中，success-成功，failed-失败) */
  status?: string;
  /** 创建人 */
  createUser?: string;
  /** 创建人名称 */
  createUserName?: string;
  /** 错误信息 */
  msg?: string;
}

interface PageQueryParams {
  /** 导出类型 web-前端代码,backend-后端代码 */
  exportType?: string;
  /** 分组依据 */
  groupBy?: string;
  /** 是否需要总数 */
  needTotalCount?: boolean;
  /** 排序字段 */
  orderBy?: string;
  /** 排序方向 */
  orderDirection?: string;
  /** 页码索引 */
  pageIndex: number;
  /** 页大小 */
  pageSize: number;
  /** 项目id */
  programmeId: string;
  /** 代码工程名称 */
  projectName?: string;
  /** 导出状态(init-初始化，codeGenerating-底座代码生成中，codeAssembling-平台生成代码组装中，success-成功，failed-失败) */
  status?: string;
}

interface ExportParams {
  /** 研发一体化平台生成前端代码附件id */
  attachmentId?: string;
  /** 导出代码类型web-前端代码，backend-后端代码 */
  exportType: string;
  /** 项目id */
  programmeId: string;
}

export default {
  /** 详情查询 */
  detail: (id: string) =>
    request.get<ResponseWrap<ListItem>>(`/web/v1/component/codeExport/detail/${id}`),
  /** 删除 */
  delete: (id: string) =>
    request.delete<
      ResponseWrap<{
        errCode: string;
        errMessage: string;
        success: boolean;
      }>
    >(`/web/v1/component/codeExport/delete/${id}`),
  /** 组件分页查询 */
  pageQuery: (params: PageQueryParams) =>
    request.post<ResponseWrap<ListItem[]>>('/web/v1/component/codeExport/pageQuery', params),
  /** 下载 */
  download: (id: string) =>
    request.get<Blob>(`/web/v1/component/codeExport/download/${id}`, { responseType: 'blob' }),
  /** 导出 */
  export: (params: ExportParams) =>
    request.post<
      ResponseWrap<{
        errCode: string;
        errMessage: string;
        success: boolean;
      }>
    >('/web/v1/component/codeExport/export', params),
  // 模型、编排增量代码生成
  backCodeGenerate: (params: {
    apiType: 'dataModel' | 'logicSchema';
    apis: string[];
    programmeId: string;
  }) =>
    request.post('/web/v1/codegen/increment', params, {
      responseType: 'blob',
    }),
};
