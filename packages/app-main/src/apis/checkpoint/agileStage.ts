import axiosInstance, { type ResponseWrap } from '../requests/request';

/***************管控流程****************/
export interface GuideInfo {
  btn_name: string;
  btn_url: string;
}
export type RuleLogic = '==' | '!=' | '>' | '<' | '>=' | '<=';
export type RuleConnection = '&&' | '||' | undefined;
export interface CheckRuleGroupRule {
  ruleCategory: string;
  ruleLogic: RuleLogic | undefined;
  ruleLogicValue: string;
  ruleConnection: RuleConnection;
}
export interface CheckRuleGroup {
  group: string;
  ruleConnection: RuleConnection;
  rules: CheckRuleGroupRule[];
}

export interface AgileStageItem {
  id?: string;
  itemCode?: string;
  itemGuideInfo?: string; // 检查项快速指引JSON数据，格式如GuideInfo
  itemName: string;
  itemOrder: number;
  itemRules: string; // 检查规则
  itemRulesDesc?: string; // 检查规则描述
  stageId?: number; // 阶段id
  checkResult?: number; // 检查结果
  checkResultMsg?: string; // 检查结果描述
  supervised?: boolean; // 是否督办
  isSprintItem?: boolean; // 是否为检查项
}
export interface CheckRuleItem extends Omit<AgileStageItem, 'itemGuideInfo' | 'itemRules'> {
  itemGuideInfo: GuideInfo[];
  itemRules: CheckRuleGroup[];
}
export interface AgileStage {
  id?: string;

  agileStageItemVOList?: AgileStageItem[]; // 管控流程下的检查项列表
  agileStageItemDTOList?: AgileStageItem[]; // 管控流程下的检查项列表
  stageCode?: string; // 阶段编码
  stageDesc: string; // 阶段描述
  stageName: string; // 阶段名称
  stageOrder: number; // 阶段排序
  stageStatus: 1 | 0; // 阶段状态（1：启用，0：禁用，默认1启用）
  croppingCheckedItems?: string[]; // 勾选的id
  stageColor?: string; // 阶段颜色
  stageIcon?: string; // 阶段图标
}

// 查询管控流程列表
export function getAgileStageList(data?: Partial<AgileStage>) {
  return axiosInstance.post<ResponseWrap<AgileStage[]>>('/web/v1/agile/agileStage/list', data);
}
// 分页查询管控流程列表
export function getAgileStagePageList(
  params: Partial<AgileStage> & { pageIndex: number; pageSize: number },
) {
  return axiosInstance.get<ResponseWrap<{ list: AgileStage[]; total: number }>>(
    '/web/v1/agile/agileStage/page',
    { params },
  );
}
// 全量查询管控流程列表
export function getAgileStageListAll(params: Partial<AgileStage>) {
  return axiosInstance.post<ResponseWrap<AgileStage[]>>(`/web/v1/agile/agileStage/list`, params);
}
// 新增管控流程
export function addAgileStage(data: Partial<AgileStage>) {
  return axiosInstance.post('/web/v1/agile/agileStage', data);
}
// 更新管控流程
export function updateAgileStage(data: Partial<AgileStage>) {
  return axiosInstance.put('/web/v1/agile/agileStage', data);
}
// 删除管控流程
export function deleteAgileStage(id: string) {
  return axiosInstance.delete(`/web/v1/agile/agileStage/${id}`);
}
// 批量删除管控流程
export function deleteAgileStageBatch(ids: string) {
  return axiosInstance.delete(`/web/v1/agile/agileStage/batch/${ids}`);
}
// 获取管控流程详情
export function getAgileStageDetail(id: string) {
  return axiosInstance.get<ResponseWrap<AgileStage>>(`/web/v1/agile/agileStage/getById`, {
    params: { id },
  });
}
// 启用禁用管控流程
export function enableAgileStage(id: string, status: 1 | 0) {
  return axiosInstance.get(
    `/web/v1/agile/agileStage/updateStageStatus?stageId=${id}&status=${status}`,
  );
}
// 查询项目质量管控规范检查结果
export function getProjectQualityCheckResults(programmeId: string, sprintId?: string) {
  return axiosInstance.get<ResponseWrap<AgileStage[]>>(
    `/web/v1/agile/agileStage/getProgrammeCheckResult/${programmeId}`,
    {
      params: { sprintId },
    },
  );
}

// 查询裁剪项目质量管控规范检查结果
export function getProjectClipQualityCheckResults(programmeId: string) {
  return axiosInstance.get<ResponseWrap<AgileStage[]>>(
    `/web/v1/agile/agileStage/getProgrammeStage`,
    {
      params: { programmeId },
    },
  );
}

// 获取规则下拉选项
export interface CheckRulesOptions {
  ruleCategoryVOList: {
    label: string;
    value: string;
  }[];
  ruleLogicConnectionList: {
    label: string;
    value: string;
  }[];
  ruleLogicVOList: {
    label: string;
    value: string;
  }[];
}
export function getCheckRules() {
  return axiosInstance.get<ResponseWrap<CheckRulesOptions>>(
    '/web/v1/agile/agileStage/getCheckRules',
  );
}

// 提交裁剪
export function submitCropping(data: {
  programmeId: string;
  checkItemIds: string[];
  reason: string;
}) {
  return axiosInstance.post('/web/v1/programme/agile/cropping', data);
}
// 获取项目检查预警分页列表
export interface ProjectCheckWarning {
  programmeName: string;
  programmeId: string;
  supervised: 1 | 0;
  warningGuideInfo: string;
  warningInfo: string;
}
// 获取预警
export function getProjectCheckWarningPageList(params: {
  programmeIds: string;
  pageIndex: number;
  pageSize: number;
}) {
  return axiosInstance.get<ResponseWrap<ProjectCheckWarning[]>>(
    '/web/v1/agile/agileStage/programmeWarningPage',
    { params },
  );
}
export interface ProjectUpCloud {
  programmeId: string;
  programmeName: string;
  srdProcessConformity: number; // 合规状态
  srdProcessNotConformityItems: string; // 不合规项
  srdProjectId: string; // 研发云项目id
}
// 获取项目上云信息
export function getProjectUpCloudList(params: {
  programmeIds: string;
  pageIndex: number;
  pageSize: number;
}) {
  return axiosInstance.get<ResponseWrap<any[]>>('/web/v1/agile/agileStage/getSrdInfoList', {
    params,
  });
}
