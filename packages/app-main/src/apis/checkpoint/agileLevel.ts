import axiosInstance, { type ResponseWrap } from '../requests/request';

/***************管控级别****************/
export interface AgileLevel {
  id?: string;
  levelCode: string;
  levelDesc: string;
  levelName: string;
  levelRuleIds: string;
  levelRuleNames?: string[];
  levelStatus: 1 | 0; // 1:启用，0：禁用（默认1）
  programmeId?: number;
}

// 查询管控级别列表
export function getAgileLevelList(params?: Partial<AgileLevel>) {
  return axiosInstance.get<ResponseWrap<AgileLevel[]>>('/web/v1/agile/agileLevel/list', {
    params,
  });
}
// 分页查询管控级别列表
export function getAgileLevelPageList(
  params: Partial<AgileLevel> & { pageIndex: number; pageSize: number },
) {
  return axiosInstance.get<ResponseWrap<{ list: AgileLevel[]; total: number }>>(
    '/web/v1/agile/agileLevel/page',
    { params },
  );
}
// 新增管控级别
export function addAgileLevel(data: Partial<AgileLevel>) {
  return axiosInstance.post('/web/v1/agile/agileLevel', data);
}
// 更新管控级别
export function updateAgileLevel(data: Partial<AgileLevel>) {
  return axiosInstance.put('/web/v1/agile/agileLevel', data);
}
// 删除管控级别
export function deleteAgileLevel(id: string) {
  return axiosInstance.delete(`/web/v1/agile/agileLevel/${id}`);
}
// 批量删除管控级别
export function deleteAgileLevelBatch(ids: string) {
  return axiosInstance.delete(`/web/v1/agile/agileLevel/batch/${ids}`);
}
// 获取管控级别详情
export function getAgileLevelDetail(id: string) {
  return axiosInstance.get<ResponseWrap<AgileLevel>>(`/web/v1/agile/agileLevel/getById`, {
    params: { id },
  });
}
// 裁剪管控级别
export function cropAgileLevel(data: Partial<AgileLevel>) {
  return axiosInstance.post<ResponseWrap<AgileLevel>>(
    '/web/v1/agile/agileLevel/agileCropping',
    data,
  );
}
