import axiosInstance from './requests/request';
import { type ResponseWrap as AttachmentResponseWrap } from './requests/request';

interface InfoResponse {
  enable: boolean;
  publicKey: string;
  privateKey: null;
  ignoreEnvs: string[];
  algorithm: 'AES' | 'SM4' | 'RSA';
  ignoreUrls: string[];
  includeUrls: string[];
  key: string;
  offSet: string;
}

export default {
  /** 获取加密相关信息 */
  info: () =>
    axiosInstance.get<AttachmentResponseWrap<InfoResponse>>('/api/v1/encrypt/info', {
      baseURL: import.meta.env.VITE_API_PREFIX,
      timeout: 30000,
    }),
};
