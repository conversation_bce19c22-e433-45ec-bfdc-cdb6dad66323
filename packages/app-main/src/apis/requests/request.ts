import to from 'await-to-js';
import axios from 'axios';

import encryptApi from '@/apis/encrypt';
import api from '@/apis/index';
import { enableZionAxiosInterceptor } from '@/share';

type Nullable<T> = { [P in keyof T]: T[P] | null };
export type ResponseWrap<T> = Nullable<
  Partial<{
    success: boolean;
    errCode: string;
    errMessage: string;
    totalCount?: number;
    pageSize?: number;
    pageIndex?: number;
    data: T;
    empty?: boolean;
    notEmpty?: boolean;
    totalPages?: number;
  }>
>;

const axiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_PREFIX,
  timeout: 30000,
});

export const asyncGetSSOLoginUrl = async () => {
  const [err, res] = await to(
    api.getSsoAuthUrl({
      clientLoginUrl: encodeURIComponent(window.location.href),
    }),
  );

  if (err) return;

  return res.data.data;
}

enableZionAxiosInterceptor({
  axiosInstance,
  asyncGetSSOLoginUrl,
  asyncGetEncryptInfo: async () => {
    const [err, res] = await to(encryptApi.info());

    if (err) return;

    return res.data.data;
  },
});

export default axiosInstance;
