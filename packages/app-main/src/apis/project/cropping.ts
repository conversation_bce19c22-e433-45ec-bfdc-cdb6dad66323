import request, { type ResponseWrap } from '../requests/request';
import type { Approver } from './project';
import type { AUDIT_ACTION } from '@/dicts/audit';

const prefix = '/web/v1/programme';
type ApprovalRecord = {
  action: AUDIT_ACTION;
  approvalId: string;
  approveTime: string;
  approver: Approver;
  id: string;
  opinion: string;
};
export interface CroppingDetail {
  checkItemIds: string[];
  reason: string;
  createUserName: string;
  approveRecords: ApprovalRecord[];
}

export interface CroppingApprovalParams {
  action: 'agree' | 'reject';
  approvalId: string;
  opinion: string;
}

/**
 * 获取裁剪详情
 */
export function fetchCroppingDetail(params: { programmeId?: string; approvalTaskId?: string }) {
  return request.get<ResponseWrap<CroppingDetail>>(`${prefix}/agile/cropping/detail`, { params });
}

/**
 * 裁剪审批
 */
export function submitCroppingApproval(params: CroppingApprovalParams) {
  return request.post<ResponseWrap<any>>(`${prefix}/agile/cropping/approving`, params);
}
