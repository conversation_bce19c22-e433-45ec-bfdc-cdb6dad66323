import type { AUDIT_ACTION } from '@/dicts/audit';
import type { ProjectStage } from '@/dicts/project';

import request, { type ResponseWrap } from '../requests/request';

const prefix = '/web/v1/programme';
export type Approver = { id: string; nickName: string; roleName: string; username: string };
export type ProgressStat = {
  category: 0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10; // 指标ID(版本-1/迭代-2/需求-3/任务-4/漏洞-5/缺陷-6/过程符合度-7/研发进度-8/代码库数-9/CI/CD执行数-10 等）【主键】
  total: number;
  completed: number;
};
export interface Project {
  id?: string;
  programmeCode: string;
  programmeName: string;
  base64Icon: string;
  basePackage: string;
  description: string;
  developerIds: string[];
  ownerId: string;
  tenantId: string;
  progress: ProgressStat[];
  stage: {
    stageCode?: string;
    stageName: string;
    stageId: string;
    stageColor: string;
    stageIcon: string;
  };
  developers?: {
    id: string;
    avatar: string;
    username: string;
    nickName: string;
  }[];
  owner?: {
    id: string;
    avatar: string;
    username: string;
    nickName: string;
  };
  tenant?: {
    tenantId: string;
  };
  createTime?: string;
  programmeIcon?: {
    base64Img: string;
  };
  sysAppKey?: string | null;
  [prop: string]: any;
}
// 推进阶段审批详情

export interface StageAuditDetail {
  approveRecords: {
    action: AUDIT_ACTION;
    approvalId: string;
    approveTime: string;
    approver: Approver;
    id: string;
    opinion: string;
  }[]; // 项目推进审批记录
  approvers: Approver[]; // 审核人
  currentStage: string;
  nextStage: string;
  status?: 1 | 2 | 3;
}

export function getProjectList(params: {
  programmeCode?: string;
  programmeName?: string;
  pageIndex?: number;
  pageSize?: number;
}) {
  return request.get<ResponseWrap<Project[]>>(`${prefix}/query`, {
    params,
  });
}
export function getAllProjectList() {
  return request.get<ResponseWrap<Project[]>>(`${prefix}/dict`);
}
export function getProjectDetailById(id: string) {
  return request.get<ResponseWrap<Project>>(`${prefix}/${id}`);
}
export function getProjectDetailByCode(code: string) {
  return request.get<ResponseWrap<Project>>(`${prefix}/`, {
    params: { programmeCode: code },
  });
}
export function addProject(data: Project) {
  return request.post<ResponseWrap<Project>>(`${prefix}/`, data);
}
export function updateProject(data: Project) {
  return request.put<ResponseWrap<Project>>(`${prefix}/`, data);
}
export function deleteProject(id: Project['id']) {
  return request.delete<ResponseWrap<Project>>(`${prefix}/${id}`);
}
export function getProjectIconById(id: string) {
  return request.get<ResponseWrap<{ base64Img: string; programmeId: string }>>(
    `${prefix}/${id}/icon`,
  );
}

/**
 * 阶段推进
 */
export function advanceProject(data: {
  approvers: string[];
  currentStage: ProjectStage;
  nextStage: ProjectStage;
  programmeId: string;
  sprintId?: string;
}) {
  return request.post(`${prefix}/advance`, data);
}

// 开始研发（这里包含步骤1，步骤2）
export function setDevConfig(data: {
  componentIds?: string[];
  zionVersion?: string;
  programmeId: string;
}) {
  return request.post(`${prefix}/devconfig/init`, data);
}
// 开始研发状态修改
export function startingDev(params: { programmeId: string }) {
  return request.put<ResponseWrap<any>>(`${prefix}/development/starting`, params);
}

// 获取阶段推进详情
export function fetchStageDetail(params: {
  programmeId?: string;
  advanceId?: string;
  sprintId?: string;
}) {
  return request.get<ResponseWrap<StageAuditDetail>>(`${prefix}/advance`, { params });
}

// 阶段推进审批
export function submitStageApproval(params: {
  action: 'agree' | 'reject';
  approvalId: string;
  opinion: string;
}) {
  return request.post<ResponseWrap<any>>(`${prefix}/advance/approve`, params);
}

// 获取当前用户参与的项目列表
export function getAuthProjectList(params?: object) {
  return request.get<ResponseWrap<StageAuditDetail>>(`${prefix}/dict`, { params: params || {} });
}
export interface Sprint {
  id: string;
  currentStageId: string;
  endTime: string;
  programmeId: string;
  sprintId: string;
  sprintName: string;
  startTime: string;
}
// 获取迭代列表
export function getSprintList(programmeId: string) {
  return request.get<ResponseWrap<Sprint[]>>(`${prefix}/${programmeId}/sprint`);
}
