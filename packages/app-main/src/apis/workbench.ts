import request, { type ResponseWrap } from './requests/request';

// 获取当前SM下所有成员的AI使用情况
export interface UserAiCountOfSM {
  assistantId: string;
  assistantName: string;
  usageCount: number;
  userCount: number;
}
export function getUserAiCountOfSM(params: { startTime: string; endTime: string }) {
  return request.get<ResponseWrap<UserAiCountOfSM[]>>('/web/v1/workbench/sm/ai/usage/count', {
    params,
  });
}
// 根据项目 id获取项目下成员的研发贡献
export interface DevContributionOfProject {
  effectiveCodeCount: number;
  fixBugsCount: number;
  id: number;
  pipelineCount: number;
  tasksCount: number;
  userAvatar: string;
  userName: string;
}
export function getDevContributionOfProject(params: {
  programmeId: string;
  pageIndex: number;
  pageSize: number;
  userId?: string;
}) {
  return request.get<ResponseWrap<DevContributionOfProject[]>>('/web/v1/workbench/sm/contribute', {
    params,
  });
}
// 获取所有项目下所有成员研发贡献
export interface DevContributionOfAllProject {
  effectiveCodeCount: number;
  fixBugsCount: number;
  id: number;
  pipelineCount: number;
  tasksCount: number;
  userAvatar: string;
  userName: string;
}
export function getDevContributionOfAllProject() {
  return request.get<ResponseWrap<DevContributionOfAllProject[]>>(
    '/web/v1/workbench/sm/contribute/all',
  );
}
// 获取项目的质量情况
export interface ProjectQualityOfSM {
  programmeId: number;
  programmeName: string;
  qualityScore: number;
  statisticsTime: string;
}
export function getProjectQualityOfSM(programmeId: string) {
  return request.get<ResponseWrap<ProjectQualityOfSM[]>>(
    `/web/v1/workbench/sm/quality/${programmeId}`,
  );
}
// 获取当前下待办
export interface Todo {
  todoMatterId: number;
  todoMatterName: string;
  todoMatterNum: number;
}
export function getTodos() {
  return request.get<ResponseWrap<Todo[]>>('/web/v1/workbench/sm/todo');
}
// 获取项目预警列表
