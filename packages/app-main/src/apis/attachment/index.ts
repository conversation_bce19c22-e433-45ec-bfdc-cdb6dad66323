import type { RcFile } from 'ant-design-vue/es/vc-upload/interface';

import attachmentInstance from '../requests/request';

export interface AttachmentUploadParams {
  appId: string;
  file: string | RcFile | Blob;
  tenantId?: string;
  dirId: string | number;
}
export interface AttachmentUploadRes {
  ext: string;
  filename: string;
  id: number | string;
  originalFilename: string;
  size: number | string;
  url: string;
}

export default {
  //附件下载
  downloadById: (attachmentId: number | string) =>
    attachmentInstance.get<Blob>(
      '/web/v1/component/codeExport/download/attachment/' + attachmentId,
      {
        responseType: 'blob',
        timeout: 0,
      },
    ),
};
