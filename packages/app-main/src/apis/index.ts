import type { RcFile } from 'ant-design-vue/es/vc-upload/interface';
import type { antIconName } from 'app-main/src/components/AntIconRender/AntIconRender';

import axiosInstance, { type ResponseWrap } from './requests/request';

// import type { AxiosPromise } from 'axios';

interface GetSsoAuthUrlParams {
  clientLoginUrl: string;
}

interface LoginByTicketParams {
  ticket: string;
  tenantId?: string;
}
interface LoginByTicketResponse {
  prefix?: string;
  token: string;
  tokenName: string;
}

export interface ListParams {
  pageSize: number;
  pageIndex: number;
  filename?: string;
  tenantId: string | undefined;
  appId: string | undefined;
  url?: string;
  originalFilename?: string;
  dirId?: string | number;
}

export interface ListItem {
  appId?: string;
  ext?: string;
  filename?: string;
  id?: number | string;
  originalFilename?: string;
  path?: string;
  platform?: string;
  size?: string;
  tenantId?: string;
  url?: string;
  createTime?: string;
}
export interface AttachmentUploadParams {
  appId: string;
  file: string | RcFile | Blob;
  tenantId: string;
  dirId: string | number;
}
export interface AttachmentUploadRes {
  ext: string;
  filename: string;
  id: number | string;
  originalFilename: string;
  size: number | string;
  url: string;
}
export interface createDirParams {
  appId?: number | string;
  dirName: string;
  tenantId?: number | string;
  pid?: number | string;
}

interface UploadByChunkParams {
  file: File;
  chunkCount: number;
  chunkIndex: number;
  md5: string;
  originalMd5: string;
  totalSize: number;
  appId?: string;
  tenantId?: string;
  dirId?: string | number;
}
export interface MenuTreeItem {
  resourceId: string;
  appId: null;
  name: string;
  code: null;
  permission: string;
  icon: antIconName;
  path: string;
  sort: number;
  visible: true;
  type: number;
  parentId: string;
  isLink: false;
  children: MenuTreeItem[];
}

export interface QuickLinkItem {
  name: string;
  icon: string;
  url: string;
}
export interface UserInfoFull {
  avatar: string;
  deptName: string;
  nickName: string;
  position: string | null;
  roles: string;
  userId: string;
  username: string;
}
export default {
  /** 查询单点登录页回调地址 */
  getSsoAuthUrl: (params: GetSsoAuthUrlParams) =>
    axiosInstance.get<ResponseWrap<string>>('/sso/getSsoAuthUrl', { params }),

  /** 根据ticket进行登录 */
  loginByTicket: (params: LoginByTicketParams) =>
    axiosInstance.get<ResponseWrap<LoginByTicketResponse>>('/sso/loginByTicket', { params }),

  /** 获取登录用户租户列表 */
  getTenantList: () =>
    axiosInstance.get<ResponseWrap<{ tenantId: string; tenantName: string }[]>>('/sso/list/tenant'),

  /** 获取租户下应用 */
  getAppByTenantId: (tenantId: string | undefined) =>
    axiosInstance.get<ResponseWrap<{ appId: string; appName: string }[]>>(
      '/sso/list/app/' + tenantId,
    ),
  /** 单点登出 */
  logout: () => axiosInstance.get<ResponseWrap<boolean>>('/sso/logout'),
  /** 获取用户信息 */
  getCurrentUser: () => axiosInstance.get<ResponseWrap<any>>('/security/user/getUserInfo'),
  /** 获取菜单树 */
  tree: () => axiosInstance.get<ResponseWrap<MenuTreeItem[]>>('/security/user/getMenuTree'),
  /** 获取快捷导航链接 */
  getQuickLink: () => axiosInstance.get<ResponseWrap<QuickLinkItem[]>>('/api/quicklink/list'),
  // 获取用户部门、角色等信息
  getUserAllInfo: () =>
    axiosInstance.get<ResponseWrap<UserInfoFull>>('/web/v1/zion/getUserAllInfo'),
  // 获取用户权限标识
  getUserPermissions: () =>
    axiosInstance.get<ResponseWrap<string[]>>('/web/v1/zion/getUserPermissions'),
};
