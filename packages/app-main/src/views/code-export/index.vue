<script setup lang="tsx">
  import { useToggle } from '@vueuse/core';
  import { type TableColumnsType } from 'ant-design-vue';
  import dayjs from 'dayjs';
  import { reactive, ref } from 'vue';
  import type { ComponentExposed } from 'vue-component-type-helpers';

  import codeExportApi, { type ListItem } from '@/apis/code-generate/codeExport';
  import ServerPagination from '@/components/ServerPagination/index.vue';
  import TableList from '@/components/TableList/index.vue';

  import DeleteButton from './components/DeleteButton.vue';
  import DetailButton from './components/DetailButton.vue';
  import DownloadButton from './components/DownloadButton.vue';
  import ExportsButton from './components/ExportsButton.vue';
  import ExportTypeTag from './components/ExportTypeTag.vue';
  import StatusTag from './components/StatusTag.vue';

  const { appId } = useAppidAndTenantid();

  const formState = reactive<{
    projectName: string;
    exportType?: string;
    status?: string;
  }>({
    projectName: '',
    exportType: undefined,
    status: undefined,
  });
  const [listLoading, toggleListLoading] = useToggle();
  const list = ref<ListItem[]>([]);

  // 添加排序状态
  const sortState = reactive<{
    orderBy?: string;
    orderDirection?: string;
  }>({
    orderBy: undefined,
    orderDirection: undefined,
  });

  const request = ({ pageIndex, pageSize }: { pageIndex: number; pageSize: number }) =>
    codeExportApi.pageQuery({
      ...formState,
      ...sortState, // 添加排序参数
      pageIndex,
      pageSize,
      programmeId: appId.value,
    });
  const serverPaginationRef = ref<ComponentExposed<typeof ServerPagination>>();

  // 处理表格排序变化
  const handleTableChange = (pagination: any, filters: any, sorter: any) => {
    if (sorter && sorter.order) {
      sortState.orderBy = sorter.field;
      sortState.orderDirection = sorter.order === 'ascend' ? 'ASC' : 'DESC';
    } else {
      // 如果没有排序，清空排序状态
      sortState.orderBy = undefined;
      sortState.orderDirection = undefined;
    }
    // 重新获取数据
    serverPaginationRef.value?.fetchData();
  };

  const columns: TableColumnsType<ListItem> = [
    {
      title: '代码工程名称',
      dataIndex: 'projectName',
      key: 'projectName',
      align: 'left',
    },
    {
      title: '代码类型',
      dataIndex: 'exportType',
      key: 'exportType',
      align: 'center',
      customRender: ({ text }) => (text ? <ExportTypeTag type={text} /> : '-'),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      align: 'center',
      width: 180,
      customRender: ({ record, text, index }) =>
        text ? (
          <StatusTag
            record={record}
            onUpdate:record={(val) => {
              list.value[index] = val;
            }}
          />
        ) : (
          '-'
        ),
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      align: 'center',
      width: 200,
      customRender: ({ text }) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-'),
      sorter: true, // 添加排序功能
    },
    {
      title: '创建人',
      dataIndex: 'createUserName',
      key: 'createUserName',
      align: 'center',
      width: 180,
      customRender: ({ text }) => text || '-',
    },
    {
      title: '操作',
      key: 'action',
      align: 'center',
      width: 220,
    },
  ];
</script>

<template>
  <div>
    <TableList
      bordered
      :filter-form-state="formState"
      :loading="listLoading"
      @query="serverPaginationRef?.fetchDataButResetPage"
    >
      <template #filterForm>
        <AFormItem
          label="代码工程名称"
          name="projectName"
        >
          <AInput
            v-model:value.trim="formState.projectName"
            :disabled="listLoading"
            placeholder="请输入代码工程名称"
            allow-clear
          />
        </AFormItem>

        <AFormItem
          label="代码类型"
          name="exportType"
        >
          <ASelect
            v-model:value="formState.exportType as string | undefined"
            :disabled="listLoading"
            placeholder="请选择代码类型"
            allow-clear
            @change="serverPaginationRef?.fetchDataButResetPage"
          >
            <ASelectOption value="web">前端代码</ASelectOption>
            <ASelectOption value="backend">后端代码</ASelectOption>
          </ASelect>
        </AFormItem>

        <AFormItem
          label="状态"
          name="status"
        >
          <ASelect
            v-model:value="formState.status as string | undefined"
            :disabled="listLoading"
            placeholder="请选择状态"
            allow-clear
            @change="serverPaginationRef?.fetchDataButResetPage"
          >
            <ASelectOption value="init">初始化</ASelectOption>
            <ASelectOption value="codeGenerating">底座代码生成中</ASelectOption>
            <ASelectOption value="codeAssembling">平台生成代码组装中</ASelectOption>
            <ASelectOption value="success">成功</ASelectOption>
            <ASelectOption value="failed">失败</ASelectOption>
          </ASelect>
        </AFormItem>
      </template>

      <template #tableActions>
        <ExportsButton @export-success="serverPaginationRef?.fetchDataButResetPage" />
      </template>

      <template #table>
        <ATable
          row-key="id"
          size="middle"
          :data-source="list"
          :columns="columns"
          :pagination="false"
          @change="handleTableChange"
        >
          <template #headerCell="{ column }">
            <div
              v-if="column.key === 'projectName'"
              class="min-w-[120px]"
            >
              {{ column.title }}
            </div>
          </template>
          <template #bodyCell="{ column, record, index }">
            <div v-if="column.key === 'action'">
              <DetailButton
                :record="record as ListItem"
                @update:record="
                  (val) => {
                    list[index] = val;
                  }
                "
              />
              <DownloadButton :record="record as ListItem" />
              <DeleteButton
                :record="record as ListItem"
                @delete="serverPaginationRef?.fetchData"
              />
            </div>
          </template>
        </ATable>
      </template>

      <template #pagination>
        <ServerPagination
          ref="serverPaginationRef"
          :request="request"
          @loading-change="(val) => toggleListLoading(val)"
          @list-change="(val) => (list = val || [])"
        />
      </template>
    </TableList>
  </div>
</template>
