<script setup lang="ts">
  import {
    CloudUploadOutlined,
    DownloadOutlined,
    DownOutlined,
    NotificationOutlined,
    QuestionCircleOutlined,
  } from '@ant-design/icons-vue';
  import { type FormInstance, type MenuProps, message } from 'ant-design-vue';
  import to from 'await-to-js';
  import * as R from 'remeda';

  import codeExportApi from '@/apis/code-generate/codeExport';

  const { appId } = useAppidAndTenantid();

  const exportTargets = [
    {
      key: 'local',
      icon: DownloadOutlined,
      label: '导出本地',
    },
    {
      key: 'srdCloud',
      icon: CloudUploadOutlined,
      label: '推送研发云',
    },
  ];

  const emits = defineEmits<{
    (e: 'exportSuccess'): void;
  }>();

  const formRef = ref<FormInstance>();
  const defaultFormState: any = {
    programmeId: appId.value, // 应用id
    attachmentId: undefined, // 研发一体化平台生成前端代码附件id
    account: undefined, // 研发云Git账号
    password: undefined, // 研发云Git密码
    exportType: 'web', // 导出类型
    exportMode: exportTargets[0].key, // 导出模式
    exportBizModuleOnly: false, // 是否仅导出业务模块
  };
  const formState = ref(R.clone(defaultFormState));
  const formParam = computed(() => {
    const params = R.clone(formState.value);
    if (!isLocal.value) {
      params.srdGitInfo = JSON.stringify({
        account: params.account,
        password: params.password,
      });
    }
    delete params.account;
    delete params.password;
    return params;
  });

  const isLocal = computed(() => formState.value.exportMode === exportTargets[0].key);
  const isModalOpen = ref(false);
  const submitLoading = ref(false);
  const modalKey = ref(0);

  const modalTitle = computed(() => {
    return formState.value.exportMode === exportTargets[1].key
      ? exportTargets[1].label
      : exportTargets[0].label;
  });
  const handleMenuClick: MenuProps['onClick'] = ({ key }) => {
    formState.value.exportMode = key as string;
    modalKey.value += 1;
    isModalOpen.value = true;
  };

  const cancelModal = () => {
    formState.value = R.clone(defaultFormState);
    isModalOpen.value = false;
  };

  const isLoading = ref(false);
  const handleLocalExport = async () => {
    isLoading.value = true;
    submitLoading.value = true;

    const [err, res] = await to(codeExportApi.export(formParam.value));
    isLoading.value = false;
    submitLoading.value = false;

    if (err || !res.data.success) {
      return;
    }

    message.success('导出成功，请等待导出任务完成后，点击下载按钮下载源码');
    emits('exportSuccess');
    cancelModal();
  };
  const handleModalOk = async () => {
    if (formState.value.exportMode === 'srdCloud') {
      message.info('建设中....');
      return;
    }
    if (!formRef.value) {
      message.error('表单实例不存在，请联系管理员');
      return;
    }

    const [validateErr] = await to(formRef.value?.validate());
    if (validateErr) {
      return;
    }
    handleLocalExport();
  };
</script>

<template>
  <ADropdown
    :disabled="isLoading"
    trigger="click"
  >
    <AButton
      type="primary"
      :loading="isLoading"
    >
      导出源码
      <DownOutlined />
    </AButton>
    <template #overlay>
      <AMenu @click="handleMenuClick">
        <AMenuItem
          v-for="target in exportTargets"
          :key="target.key"
        >
          <component
            :is="target.icon"
            class="mr-[2px]"
          ></component>
          {{ target.label }}
        </AMenuItem>
      </AMenu>
    </template>
  </ADropdown>

  <AModal
    :key="modalKey"
    v-model:open="isModalOpen"
    :title="modalTitle"
    width="540px"
    :confirm-loading="submitLoading"
    ok-text="确定"
    cancel-text="取消"
    @ok="handleModalOk"
  >
    <ASpin :spinning="submitLoading">
      <AForm
        ref="formRef"
        :model="formState"
        autocomplete="off"
        label-align="left"
        :label-col="{ style: { width: '140px' } }"
        class="mt-[12px]"
      >
        <ARow>
          <ACol
            v-if="!isLocal"
            :span="24"
          >
            <AFormItem
              label="研发云Git账号"
              name="account"
              :rules="[{ required: true }]"
            >
              <AInput
                v-model:value.trim="formState.account"
                placeholder="请输入"
                :disabled="submitLoading"
              />
            </AFormItem>
          </ACol>
          <ACol
            v-if="!isLocal"
            :span="24"
          >
            <AFormItem
              label="研发云Git密码"
              name="password"
              :rules="[{ required: true }]"
            >
              <AInputPassword
                v-model:value.trim="formState.password"
                placeholder="请输入"
                :disabled="submitLoading"
              />
            </AFormItem>
          </ACol>
          <ACol :span="24">
            <AFormItem
              label=" &nbsp&nbsp导出前/后端"
              name="exportType"
              :rules="[{ required: false }]"
            >
              <ARadioGroup
                v-model:value="formState.exportType"
                button-style="solid"
              >
                <ARadio value="web">前端</ARadio>
                <ARadio value="backend">后端</ARadio>
              </ARadioGroup>
            </AFormItem>
          </ACol>
          <ACol
            v-if="formState.exportType === 'backend'"
            :span="24"
          >
            <AFormItem
              label=" &nbsp&nbsp仅导出业务模块"
              name="exportBizModuleOnly"
              :rules="[{ required: false }]"
            >
              <template #tooltip>
                <APopover
                  title=""
                  placement="topLeft"
                  :overlay-inner-style="{ padding: 0, maxWidth: '320px' }"
                >
                  <template #content>
                    <AAlert
                      type="warning"
                      show-icon
                      message="如果仅导出业务模块，则不会生成底座相关代码，只导出与底座集成好的单个业务模块结构的示例代码。"
                    >
                      <template #icon>
                        <NotificationOutlined />
                      </template>
                    </AAlert>
                  </template>
                  <QuestionCircleOutlined class="ml-[3px]" />
                </APopover>
              </template>
              <ARadioGroup
                v-model:value="formState.exportBizModuleOnly"
                button-style="solid"
              >
                <ARadio :value="false">否</ARadio>
                <ARadio :value="true">是</ARadio>
              </ARadioGroup>
            </AFormItem>
          </ACol>
        </ARow>
      </AForm>
    </ASpin>
  </AModal>
</template>
