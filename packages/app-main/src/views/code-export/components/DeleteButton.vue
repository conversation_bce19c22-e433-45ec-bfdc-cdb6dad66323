<script setup lang="ts">
  import { message } from 'ant-design-vue';
  import { to } from 'await-to-js';

  import codeExportApi, { type ListItem } from '@/apis/code-generate/codeExport';

  const props = defineProps<{
    record: ListItem;
  }>();
  const emit = defineEmits<{
    (e: 'delete'): void;
  }>();

  const canDelete = computed(
    () => props.record.status && new Set(['success', 'failed']).has(props.record.status),
  );

  const handleDelete = async () => {
    const [err, res] = await to(codeExportApi.delete(props.record.id));

    if (err || !res.data?.success) {
      return;
    }

    message.success('删除成功');
    emit('delete');
  };
</script>

<template>
  <APopconfirm
    :title="`是否要删除 [${record.projectName}] 代码工程？`"
    ok-text="删除"
    cancel-text="取消"
    destroy-tooltip-on-hide
    :disabled="!canDelete"
    :ok-button-props="{ danger: true }"
    placement="bottomRight"
    @confirm="handleDelete"
  >
    <AButton
      type="link"
      size="small"
      danger
      :disabled="!canDelete"
    >
      删除
    </AButton>
  </APopconfirm>
</template>
