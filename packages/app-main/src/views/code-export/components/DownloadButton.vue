<script setup lang="tsx">
  import { parse } from '@tinyhttp/content-disposition';
  import { message, Modal } from 'ant-design-vue';
  import { to } from 'await-to-js';
  import { saveAs } from 'file-saver';

  import codeExportApi, { type ListItem } from '@/apis/code-generate/codeExport';

  const props = defineProps<{
    record: ListItem;
  }>();

  const canDownload = computed(
    () => props.record.status && new Set(['success']).has(props.record.status),
  );

  const isDownloading = ref(false);
  const handleDownload = async () => {
    isDownloading.value = true;
    const [err, res] = await to(codeExportApi.download(props.record.id));
    isDownloading.value = false;

    if (err || !res.data) {
      message.error('下载失败');
      return;
    }

    let filename = props.record.projectName;
    try {
      filename = parse(res.headers['content-disposition']).parameters.filename as string;
    } catch (error) {
      console.error(error);
    }

    saveAs(res.data, filename);

    Modal.success({
      title: '下载完成',
      content: '请留意浏览器的下载提示',
    });
  };
</script>

<template>
  <AButton
    type="link"
    size="small"
    :loading="isDownloading"
    :disabled="!canDownload"
    @click="handleDownload"
  >
    下载
  </AButton>
</template>
