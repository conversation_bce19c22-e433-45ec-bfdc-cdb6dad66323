<script setup lang="tsx">
  interface Props {
    type?: string;
  }

  const props = defineProps<Props>();
</script>

<template>
  <ATag
    v-if="type === 'web'"
    :bordered="false"
    color="cyan"
    class="mr-0"
  >
    前端
  </ATag>
  <ATag
    v-else-if="type === 'backend'"
    :bordered="false"
    color="purple"
    class="mr-0"
  >
    后端
  </ATag>
  <ATag
    v-else
    color="default"
    class="mr-0"
  >
    {{ props.type }}
  </ATag>
</template>
