<script setup lang="ts">
  import { useAsyncState } from '@vueuse/core';
  import dayjs from 'dayjs';

  import codeExportApi, { type ListItem } from '@/apis/code-generate/codeExport';

  import ExportTypeTag from './ExportTypeTag.vue';
  import StatusTag from './StatusTag.vue';

  const props = defineProps<{
    record: ListItem;
  }>();
  const emit = defineEmits<{
    (e: 'update:record', val: ListItem): void;
  }>();

  const isDetailDrawerOpen = ref(false);
  const openDetailDrawer = () => {
    isDetailDrawerOpen.value = true;
    executeDetail();
  };

  const {
    state: detailState,
    execute: executeDetail,
    isLoading: isDetailLoading,
  } = useAsyncState(
    async () => {
      const res = await codeExportApi.detail(props.record.id);
      const data = res.data.data;

      if (data) {
        emit('update:record', data);
      }

      return data;
    },
    null,
    { immediate: false },
  );
</script>

<template>
  <AButton
    type="link"
    size="small"
    @click="openDetailDrawer"
  >
    详情
  </AButton>

  <ADrawer
    v-model:open="isDetailDrawerOpen"
    title="详细信息"
    placement="right"
    :width="612"
  >
    <ASpin :spinning="isDetailLoading">
      <ADescriptions
        bordered
        :column="1"
        :label-style="{ width: '140px', textAlign: 'right' }"
      >
        <ADescriptionsItem label="代码工程名称">
          {{ detailState?.projectName || '-' }}
        </ADescriptionsItem>
        <ADescriptionsItem label="代码类型">
          <ExportTypeTag
            v-if="detailState?.exportType"
            :type="detailState?.exportType"
          />
          <template v-else>-</template>
        </ADescriptionsItem>
        <ADescriptionsItem label="底座版本">
          {{ detailState?.platformVersion || '-' }}
        </ADescriptionsItem>
        <ADescriptionsItem label="安装组件">
          {{ detailState?.componentNameList?.join(', ') || '-' }}
        </ADescriptionsItem>
        <ADescriptionsItem label="状态">
          <div
            v-if="detailState?.status === 'failed' && detailState?.msg?.trim()"
            class="-mx-[22px] -my-[14px]"
          >
            <AAlert
              banner
              show-icon
              type="error"
            >
              <template #message>
                <div>
                  <span class="opacity-40">导出源码失败</span>
                </div>
              </template>
              <template #description>
                <div>
                  <span class="opacity-40">失败原因：</span>
                  <span>{{ detailState?.msg }}</span>
                </div>
              </template>
            </AAlert>
          </div>
          <StatusTag
            v-else-if="detailState?.status"
            :record="detailState"
            @update:record="executeDetail()"
          />
          <template v-else>-</template>
        </ADescriptionsItem>
        <ADescriptionsItem label="创建人">
          {{ detailState?.createUserName || '-' }}
        </ADescriptionsItem>
        <ADescriptionsItem label="创建时间">
          {{
            detailState?.createTime
              ? dayjs(detailState?.createTime).format('YYYY-MM-DD HH:mm:ss')
              : '-'
          }}
        </ADescriptionsItem>
      </ADescriptions>
    </ASpin>
  </ADrawer>
</template>
