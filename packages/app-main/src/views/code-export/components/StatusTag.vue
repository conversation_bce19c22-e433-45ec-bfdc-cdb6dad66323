<script setup lang="ts">
  import { CheckCircleOutlined, CloseCircleOutlined, SyncOutlined } from '@ant-design/icons-vue';
  import { useTimeoutFn } from '@vueuse/core';
  import { to } from 'await-to-js';

  import codeExportApi, { type ListItem } from '@/apis/code-generate/codeExport';

  const props = defineProps<{
    record: ListItem;
  }>();
  const emit = defineEmits<{
    (e: 'update:record', val: ListItem): void;
  }>();

  const shouldUpdate = (status?: string) => {
    return Boolean(status && new Set(['init', 'codeGenerating', 'codeAssembling']).has(status));
  };

  const { stop, start } = useTimeoutFn(async () => {
    if (!shouldUpdate(props.record.status)) {
      stop();
      return;
    }

    const [err, res] = await to(codeExportApi.detail(props.record.id));

    if (err || !res.data.data) {
      stop();
      return;
    }

    if (props.record.status !== res.data.data.status) {
      emit('update:record', res.data.data);
    }

    if (shouldUpdate(res.data.data.status)) {
      start();
    }
  }, 1000);
</script>

<template>
  <ATag
    v-if="record.status === 'init'"
    color="default"
    class="mr-0"
  >
    <template #icon>
      <SyncOutlined :spin="true" />
    </template>
    初始化
  </ATag>
  <ATag
    v-else-if="record.status === 'codeGenerating'"
    color="magenta"
    class="mr-0"
  >
    <template #icon>
      <SyncOutlined :spin="true" />
    </template>
    生成中
  </ATag>
  <ATag
    v-else-if="record.status === 'codeAssembling'"
    color="volcano"
    class="mr-0"
  >
    <template #icon>
      <SyncOutlined :spin="true" />
    </template>
    组装中
  </ATag>
  <ATag
    v-else-if="record.status === 'success'"
    color="success"
    class="mr-0"
  >
    <template #icon>
      <CheckCircleOutlined />
    </template>
    成功
  </ATag>
  <ATag
    v-else-if="record.status === 'failed'"
    color="error"
    class="mr-0"
  >
    <template #icon>
      <CloseCircleOutlined />
    </template>
    失败
  </ATag>
  <ATag
    v-else
    color="default"
    class="mr-0"
  >
    {{ props.record.status }}
  </ATag>
</template>
