<script setup lang="ts">
  import { DownOutlined } from '@ant-design/icons-vue';
  import { message } from 'ant-design-vue';
  import to from 'await-to-js';

  import codeExportApi from '@/apis/code-generate/codeExport';

  const emits = defineEmits<{
    (e: 'exportSuccess'): void;
  }>();

  const { appId } = useAppidAndTenantid();

  const isLoading = ref(false);

  const handleExport = async (exportType: string) => {
    isLoading.value = true;

    let attachmentId: string | undefined = undefined;

    const [err, res] = await to(
      codeExportApi.export({
        exportType,
        programmeId: appId.value,
        // 研发一体化平台生成前端代码附件id
        attachmentId,
      }),
    );
    isLoading.value = false;

    if (err || !res.data.success) {
      return;
    }

    message.success('导出成功，请等待导出任务完成后，点击下载按钮下载源码');
    emits('exportSuccess');
  };
</script>

<template>
  <ADropdown
    :disabled="isLoading"
    trigger="click"
  >
    <AButton
      type="primary"
      :loading="isLoading"
    >
      导出源码
      <DownOutlined />
    </AButton>
    <template #overlay>
      <AMenu>
        <AMenuItem
          key="web"
          @click="handleExport('web')"
        >
          前端代码
        </AMenuItem>
        <AMenuItem
          key="backend"
          @click="handleExport('backend')"
        >
          后端代码
        </AMenuItem>
      </AMenu>
    </template>
  </ADropdown>
</template>
