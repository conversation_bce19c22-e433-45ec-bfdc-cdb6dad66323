<script setup lang="ts">
  import { useElementSize } from '@vueuse/core';
  import { computed, reactive, ref, watch } from 'vue';
  import type { ComponentExposed } from 'vue-component-type-helpers';

  import codeGeneratecomponentMarketApi, {
    type ListItem,
  } from '@/apis/code-generate/componentMarket';
  import ServerPagination from '@/components/ServerPagination/index.vue';
  import TableList from '@/components/TableList/index.vue';

  import ComponentTypeSelect from './components/ComponentTypeSelect.vue';
  import ItemCard from './components/ItemCard.vue';

  const { appId } = useAppidAndTenantid();

  const el = ref<HTMLElement>();
  const { width } = useElementSize(el);
  const cols = computed(() => Math.floor(width.value / 380));
  watch(
    cols,
    (val) => {
      if (val >= 1) {
        listPageSizeStep.value = val * 2;
      }
    },
    { immediate: true },
  );
  interface FormState {
    name: string;
    type: string | undefined;
  }
  const formState = reactive<FormState>({
    name: '',
    type: undefined,
  });
  const serverPaginationRef = ref<ComponentExposed<typeof ServerPagination>>();
  const fetchDataButResetPage = () => {
    serverPaginationRef.value?.fetchDataButResetPage();
  };

  const listLoading = ref(false);
  const listPageSizeStep = ref(0);
  const list = ref<ListItem[]>();
  const formatList = computed(() => {
    return (
      list.value?.map((item) => {
        const prefixName =
          item.name?.slice(0, 2).toUpperCase() === 'AI' ? 'AI' : item.name[0] || '组';
        return { ...item, prefixName };
      }) || []
    );
  });
</script>

<template>
  <div class="min-h-full">
    <TableList
      :filter-form-state="formState"
      :loading="listLoading"
      @query="fetchDataButResetPage"
    >
      <template #filterForm>
        <AFormItem
          label="组件名称"
          name="name"
        >
          <AInput
            v-model:value.trim="formState.name"
            :disabled="listLoading"
            placeholder="请输入组件名称"
          />
        </AFormItem>

        <AFormItem
          label="组件类型"
          name="type"
        >
          <ComponentTypeSelect
            v-model:value="formState.type"
            :disabled="listLoading"
            placeholder="请选择组件类型"
            @update:value="fetchDataButResetPage"
          />
        </AFormItem>
      </template>

      <template #table>
        <div
          ref="el"
          class="transition-all-container grid gap-x-20px gap-y-20px"
          :style="{
            gridTemplateColumns: `repeat(${cols}, minmax(0, 1fr))`,
          }"
        >
          <ItemCard
            v-for="(item, index) in formatList"
            :key="item.id"
            :programme-id="appId"
            :item-data="{ ...item, index }"
            @update:item-data="
              (val) => {
                if (list) {
                  list[index] = val;
                }
              }
            "
          />
        </div>
        <AEmpty v-if="!list || !list.length" />
      </template>
      <template #pagination>
        <ServerPagination
          ref="serverPaginationRef"
          :page-size-step="listPageSizeStep"
          :request="
            ({ pageIndex, pageSize }) =>
              codeGeneratecomponentMarketApi.pageQuery({
                pageIndex,
                pageSize,
                programmeId: appId,
                name: formState.name,
                type: formState.type,
              })
          "
          @loading-change="(val) => (listLoading = val)"
          @list-change="(val) => (list = val)"
        />
      </template>
    </TableList>
  </div>
</template>
