<script setup lang="ts">
  import { BulbOutlined } from '@ant-design/icons-vue';
  import { fetchEventSource } from '@microsoft/fetch-event-source';
  import { message } from 'ant-design-vue';
  import { onUnmounted } from 'vue';

  const props = defineProps<{
    disabled?: boolean;
    currentFilePath: string;
    platformCode: string;
    localCode: string;
    language: string;
  }>();

  const emit = defineEmits<{
    (e: 'update:platformCode', code: string): void;
    (e: 'finishMerge'): void;
  }>();

  const hasAIMerged = ref(false);

  const isPending = ref(false);
  const isLoading = ref(false);
  const showDrawer = ref(false);

  const mergedCode = ref('');
  const controller = ref<AbortController | null>(null);

  const originalPlatformCode = ref('');

  const handleMerge = () => {
    mergedCode.value = '';
    showDrawer.value = true;
    // 创建新的控制器并存储引用以便后续可以取消
    controller.value = new AbortController();

    isPending.value = true;
    isLoading.value = true;
    const authKey = localStorage.getItem('zion-auth-key');
    const authValue = localStorage.getItem('zion-auth-value');
    if (!authKey || !authValue) {
      console.warn('AiMerge: 未获取到登录信息');
      return;
    }

    originalPlatformCode.value = props.platformCode;
    emit('update:platformCode', mergedCode.value);
    hasAIMerged.value = true;
    fetchEventSource(
      `${import.meta.env.VITE_API_GENERATE_BASE_URL.replace(/\/$/, '')}/web/v1/component/market/incremental/ai/streamingMerge`,
      {
        openWhenHidden: true,
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          [authKey]: authValue,
        },
        signal: controller.value.signal,
        body: JSON.stringify({
          incrementContent: props.platformCode,
          localContent: props.localCode,
        }),
        // 打开连接时验证响应
        async onopen(response) {
          if (response.status !== 200) {
            throw new Error(`服务器返回错误: ${response.status}`);
          }
          if (response.ok && response.headers.get('content-type')?.includes('text/event-stream')) {
            return;
          }
          throw new Error('无效的内容类型');
        },

        // 处理消息
        onmessage(event?: { data?: string }) {
          isPending.value = false;
          const { data } = event || {};
          if (!data) {
            return;
          }

          const answer = JSON.parse(data).answer as string | undefined;

          if (answer) {
            mergedCode.value += answer;
          }

          emit('update:platformCode', mergedCode.value);
        },

        // 连接关闭
        onclose() {
          isLoading.value = false;
          message.success('合并成功');
          emit('finishMerge');
          console.log('连接已关闭');
        },

        // 错误处理
        onerror(err) {
          console.error('连接错误', err);
          throw err;
        },
      },
    )
      .catch((error) => {
        message.error('连接错误');
        console.error('请求失败', error);
      })
      .finally(() => {
        controller.value = null;
        isPending.value = false;
        isLoading.value = false;
      });
  };

  // 提供取消方法
  const cancelMerge = () => {
    if (controller.value) {
      controller.value.abort();
      controller.value = null;
    }
  };

  // 组件卸载时清理
  onUnmounted(() => {
    cancelMerge();
  });

  const handleCancelMerge = () => {
    hasAIMerged.value = false;
    mergedCode.value = '';
    emit('update:platformCode', originalPlatformCode.value);
    emit('finishMerge');
  };
</script>

<template>
  <AButton
    v-if="!hasAIMerged || isPending || isLoading"
    type="text"
    size="small"
    :disabled="props.disabled"
    :loading="isPending || isLoading"
    @click="handleMerge"
  >
    <template #icon>
      <BulbOutlined />
    </template>
    <span class="gradient-text font-bold">智能合并</span>
  </AButton>
  <AButton
    v-else
    type="text"
    size="small"
    :disabled="props.disabled"
    @click="handleCancelMerge"
  >
    <template #icon>
      <BulbOutlined />
    </template>
    <span class="gradient-text font-bold">撤销智能合并</span>
  </AButton>
</template>

<style scoped lang="less">
  .ant-btn:not([disabled]):deep(.anticon) {
    color: var(--ant-purple-5);
  }
  .ant-btn:not([disabled]):deep(.gradient-text) {
    background-image: linear-gradient(to right, var(--ant-purple-5), var(--ant-geekblue-5));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
  }
</style>
