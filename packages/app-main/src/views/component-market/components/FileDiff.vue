<script setup lang="ts">
  import {
    ArrowRightOutlined,
    CompressOutlined,
    ExpandOutlined,
    FileUnknownOutlined,
  } from '@ant-design/icons-vue';
  import { DiffEditor as MonacoDiffEditor, loader } from '@guolao/vue-monaco-editor';
  import { useToggle } from '@vueuse/core';
  import { message, theme } from 'ant-design-vue';
  import to from 'await-to-js';
  import type { AxiosInstance } from 'axios';
  import * as monaco from 'monaco-editor';
  import * as R from 'remeda';
  import { computed, ref } from 'vue';

  import { useCurrentDark } from '@/stores/currentDark';

  import type { DiffResultItem } from '../IncrementalDiff.vue';
  import { isTextPath } from '../text-extensions';
  import AiMerge from './AiMerge.vue';

  loader.config({ monaco });

  type Opionts = Omit<monaco.editor.IStandaloneEditorConstructionOptions, 'language'>;

  const { token } = theme.useToken();

  const defaultOptions: Opionts = {
    tabSize: 2,
    automaticLayout: true,
    formatOnType: true,
    formatOnPaste: true,
    minimap: {
      renderCharacters: false,
      showSlider: 'always',
      size: 'proportional',
    },
  };

  const props = withDefaults(
    defineProps<{
      file?: DiffResultItem;
      request: AxiosInstance;
      options?: Opionts;
    }>(),
    {
      file: () => ({
        fileName: '__empty.txt',
        filePath: '__empty.txt',
        fileType: 'file',
        projectCode: '__projectCode',
        size: 0,
        status: 'add',
        storePath: '__storePath/__empty.txt',
      }),
      value: '',
      options: () => ({}),
    },
  );

  const emit = defineEmits<{
    (
      e: 'update:file',
      fileInfo?: { file: DiffResultItem; content: string; status: 'add' | 'update' | 'deleted' },
    ): void;
  }>();

  const localCode = ref('');
  const platformCode = ref('');
  const editorKey = ref(0);

  // 滚动编辑器左侧区域到最下面
  const scrollToBottom = () => {
    if (diffEditor.value) {
      const originalEditor = diffEditor.value.getOriginalEditor();
      const model = originalEditor.getModel();
      if (model) {
        const lineCount = model.getLineCount();
        originalEditor.revealLine(lineCount);
      }
    }
  };
  const getDiffContent = async () => {
    if (props.file.fileName === '__empty.txt') return;

    const [err, res] = await to(
      props.request.post('/compare/content', {
        fileName: props.file.fileName,
        filePath: props.file.filePath,
        projectCode: props.file.projectCode,
        storePath: props.file.storePath,
      }),
    );

    if (err) {
      return;
    }

    const { localCode: resLocalCode, platformCode: resPlatformCode } = res.data.data;

    diffEditor.value
      ?.getModifiedEditor()
      .getModel()
      ?.setEOL(monaco.editor.EndOfLineSequence[resLocalCode.includes('\r\n') ? 'CRLF' : 'LF']);

    localCode.value = resLocalCode;
    platformCode.value = resPlatformCode;
    editorKey.value++;
  };
  watch(() => props.file, getDiffContent, { immediate: true, deep: true });
  const handleUpdatePlatformCode = (code: string) => {
    platformCode.value = code;
    if (platformCode.value.trim()) {
      scrollToBottom();
    }
  };

  const isTextFile = computed(() => isTextPath(props.file.filePath));

  const allLanguages = monaco.languages.getLanguages();
  const language = computed(() => {
    const filename = props.file.filePath.split('/').pop();

    if (!filename) return 'plaintext';

    const fileExtension = `.${filename.split('.').pop()}`;

    if (filename === 'Dockerfile') {
      return 'dockerfile';
    }

    if (filename === '.env' || filename.startsWith('.env.')) {
      return 'properties';
    }

    if (filename === 'nginx.conf' || filename === 'nginx.conf.template') {
      return 'nginx';
    }

    for (const item of allLanguages) {
      if (item.extensions?.includes(fileExtension)) {
        return item.id;
      }
    }

    return (
      {
        '.vue': 'html',
      }[fileExtension] || 'plaintext'
    );
  });

  const slots = defineSlots<{
    hilightLanguage?: void;
  }>();

  const allOptions = computed(() => R.merge(defaultOptions, props.options));

  const currentDark = useCurrentDark();

  const isFullscreen = ref(false);
  const toggleFullscreen = useToggle(isFullscreen);

  // 全部替换函数
  const replaceAllContent = () => {
    // 将本地代码替换为平台代码
    localCode.value = platformCode.value;
  };

  // 计算实际使用的主题
  const editorTheme = computed(() => {
    // 优先使用 options 中的主题设置
    if (props.options?.theme) {
      return props.options.theme;
    }
    // 其次使用系统主题
    return currentDark.isDark ? 'vs-dark' : 'vs';
  });

  // 计算工具栏的主题相关样式
  const toolbarThemeClass = computed(() => ({
    'bg-[#1e1e1e] border-[#424242] text-[#ccc]': editorTheme.value === 'vs-dark',
    'bg-[var(--ant-colorBgContainer)] border-[var(--ant-colorBorderSecondary)] text-[var(--ant-colorTextTertiary)]':
      editorTheme.value === 'vs',
  }));

  // 计算按钮的主题相关样式
  const buttonThemeClass = computed(() => ({
    'text-[#ccc]': editorTheme.value === 'vs-dark',
    'text-[var(--ant-colorTextTertiary)]': editorTheme.value === 'vs',
  }));

  const diffEditor = shallowRef();
  const handleMount = (diffEditorInstance: any) => (diffEditor.value = diffEditorInstance);
  const isOverwriting = ref(false);
  const overwriteByPlatformCode = async () => {
    isOverwriting.value = true;
    const [err] = await to(
      props.request.post('/merge', {
        fileName: props.file.fileName,
        filePath: props.file.filePath,
        overwrite: true,
        projectCode: props.file.projectCode,
        storePath: props.file.storePath,
      }),
    );
    await new Promise((resolve) => setTimeout(resolve, 500));
    isOverwriting.value = false;

    if (err) {
      console.error('覆盖文件失败:', err);
      return;
    }

    // 覆盖平台版本时，直接将此文件标记为已删除（从差异列表中移除）
    // 因为本地文件已经和平台文件完全一致，不再有差异
    message.success('覆盖文件成功');
    emit('update:file', {
      file: props.file,
      content: platformCode.value,
      status: 'deleted',
    });

    // 在触发事件后再更新内容，避免影响状态判断
    getDiffContent();
  };

  const isUpdating = ref(false);
  const updateByCurrentCode = async () => {
    const mergeContent = diffEditor.value?.getModifiedEditor().getValue();

    if (typeof mergeContent !== 'string') {
      message.error('获取当前编辑内容失败');
      return;
    }

    isUpdating.value = true;
    const [err] = await to(
      props.request.post('/merge', {
        fileName: props.file.fileName,
        filePath: props.file.filePath,
        overwrite: false,
        projectCode: props.file.projectCode,
        storePath: props.file.storePath,
        mergeContent,
      }),
    );
    await new Promise((resolve) => setTimeout(resolve, 500));
    isUpdating.value = false;

    if (err) {
      console.error('覆盖文件失败:', err);
      return;
    }

    getDiffContent();
    message.success('更新文件成功');
    const compareMergeContent = mergeContent.replace(/\r\n/g, '\n');
    const comparePlatformContent = platformCode.value.replace(/\r\n/g, '\n');
    emit('update:file', {
      file: props.file,
      content: mergeContent,
      status: compareMergeContent === comparePlatformContent ? 'deleted' : 'update',
    });
  };

  const handleFinishMerge = () => {
    // 编辑器滚动到顶部
    if (diffEditor.value) {
      const editor = diffEditor.value.getModifiedEditor();
      editor.setScrollPosition({ scrollTop: 0, scrollLeft: 0 });
    }
  };
</script>

<template>
  <div class="h-full flex">
    <div class="h-full flex flex-1 flex-col">
      <div
        v-if="isTextFile"
        class="relative flex-1"
        :class="{
          'fullscreen-container': isFullscreen,
        }"
      >
        <div class="absolute left-0 top-0 h-full w-full">
          <div
            class="h-full flex flex-col"
            :style="{ borderRadius: isFullscreen ? 0 : `${token.borderRadius}px` }"
          >
            <div
              class="flex items-center justify-between border-b border-b-solid p-1 pl-3"
              :class="toolbarThemeClass"
            >
              <div
                v-if="slots.hilightLanguage"
                class="min-w-0 flex-1"
              >
                <slot name="hilightLanguage" />
              </div>
              <div
                v-else
                class="min-w-0 flex-1 truncate space-x-2"
                :class="buttonThemeClass"
              >
                <AiMerge
                  :key="props.file.filePath"
                  :disabled="props.file.status === 'add'"
                  :current-file-path="props.file.filePath"
                  :platform-code="platformCode"
                  :local-code="localCode"
                  :language="language"
                  @update:platform-code="handleUpdatePlatformCode"
                  @finish-merge="handleFinishMerge"
                />
                <span>{{ props.file.filePath }}</span>
              </div>

              <div class="flex items-center">
                <AButton
                  type="text"
                  size="small"
                  :class="buttonThemeClass"
                  @click="replaceAllContent()"
                >
                  <template #icon>
                    <ArrowRightOutlined />
                  </template>
                  全部替换
                </AButton>
                <AButton
                  type="text"
                  size="small"
                  :class="buttonThemeClass"
                  @click="toggleFullscreen()"
                >
                  <template #icon>
                    <CompressOutlined v-if="isFullscreen" />
                    <ExpandOutlined v-else />
                  </template>
                  {{ isFullscreen ? '退出全屏' : '全屏' }}
                </AButton>
              </div>
            </div>
            <div class="min-h-0 flex-1">
              <MonacoDiffEditor
                :theme="editorTheme"
                :options="allOptions"
                :language="language"
                :original="platformCode"
                :modified="localCode"
                @mount="handleMount"
              ></MonacoDiffEditor>
            </div>
          </div>
        </div>
      </div>
      <div
        v-else
        class="flex flex-1"
      >
        <div class="h-full flex flex-1 items-center justify-center px-30 text-center opacity-50">
          <div class="space-y-2">
            <div class="flex items-center justify-center space-x-1">
              <FileUnknownOutlined class="text-xl" />
              <span>二进制文件</span>
            </div>
            <div>{{ props.file.filePath }}</div>
            <div
              v-if="props.file.filePath.endsWith('package-lock.json')"
              class="text-left"
            >
              <AAlert
                banner
                type="info"
                :show-icon="false"
              >
                <template #message>
                  建议不做任何处理，等组件安装完成后，手动运行
                  <span class="font-bold">npm install</span>
                  命令，该文件会自动更新。
                </template>
              </AAlert>
            </div>
          </div>
        </div>
        <ADivider
          class="h-full"
          type="vertical"
        />
        <div class="h-full flex flex-1 items-center justify-center px-30 text-center opacity-50">
          <div class="space-y-2">
            <div class="flex items-center justify-center space-x-1">
              <FileUnknownOutlined class="text-xl" />
              <span>二进制文件</span>
            </div>
            <div>{{ props.file.filePath }}</div>
          </div>
        </div>
      </div>

      <div class="flex border-t border-[var(--ant-colorBorderSecondary)] border-t-solid py-4">
        <div class="w-1/2 px-30 text-center">
          <AButton
            type="primary"
            ghost
            block
            :loading="isOverwriting"
            @click="overwriteByPlatformCode()"
          >
            <template #icon>
              <EditOutlined />
            </template>
            接受远程内容
          </AButton>
        </div>
        <div class="w-1/2 px-30 text-center">
          <AButton
            type="primary"
            ghost
            block
            :loading="isUpdating"
            @click="updateByCurrentCode()"
          >
            <template #icon>
              <EditOutlined />
            </template>
            接受合并后的内容
          </AButton>
        </div>
      </div>
    </div>

    <ADivider
      class="mx-0 h-full"
      type="vertical"
    />
  </div>
</template>
