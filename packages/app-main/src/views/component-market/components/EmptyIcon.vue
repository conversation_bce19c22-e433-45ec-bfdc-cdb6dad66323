<template>
  <svg
    width="198px"
    height="128px"
    viewBox="0 0 198 128"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    xmlns:xlink="http://www.w3.org/1999/xlink"
  >
    <title>编组 25</title>
    <defs>
      <rect
        id="path-1"
        x="68.8695652"
        y="17.2173913"
        width="111.913043"
        height="94.6956522"
      ></rect>
      <mask
        id="mask-2"
        maskContentUnits="userSpaceOnUse"
        maskUnits="objectBoundingBox"
        x="0"
        y="0"
        width="111.913043"
        height="94.6956522"
        fill="white"
      >
        <use xlink:href="#path-1"></use>
      </mask>
      <path
        id="path-3"
        d="M6.0742157,9.31327312 L8.09942939,13.5761924 C8.21322145,13.8156639 8.10750649,14.1001383 7.86376816,14.2118295 L6.09725919,15.0202499 C5.8532833,15.1319411 5.563458,15.028411 5.44966593,14.7889395 L3.37836527,10.4290191 L0.831703609,12.9286633 C0.641416669,13.1154371 0.333061553,13.1154371 0.142774613,12.9288965 L0.142774595,12.9288965 C0.0513553805,12.839163 0,12.7174592 0,12.5905584 L0,0.478243487 C0,0.214055349 0.218081661,0 0.487239093,0 C0.608395572,0 0.725275939,0.0443033947 0.81507427,0.124282681 L9.88399303,8.21664749 C10.0830698,8.39432742 10.0977986,8.69675639 9.91677655,8.89215767 L9.91677658,8.89215765 C9.83315746,8.98237941 9.71758796,9.03784633 9.59369263,9.04721953 L6.07421571,9.31327307 L6.0742157,9.31327312 Z"
      ></path>
      <filter
        id="filter-4"
        x="-44.8%"
        y="-16.6%"
        width="189.6%"
        height="159.7%"
        filterUnits="objectBoundingBox"
      >
        <feMorphology
          radius="0.5"
          operator="dilate"
          in="SourceAlpha"
          result="shadowSpreadOuter1"
        ></feMorphology>
        <feOffset
          dx="0"
          dy="2"
          in="shadowSpreadOuter1"
          result="shadowOffsetOuter1"
        ></feOffset>
        <feGaussianBlur
          stdDeviation="1"
          in="shadowOffsetOuter1"
          result="shadowBlurOuter1"
        ></feGaussianBlur>
        <feComposite
          in="shadowBlurOuter1"
          in2="SourceAlpha"
          operator="out"
          result="shadowBlurOuter1"
        ></feComposite>
        <feColorMatrix
          values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.312745848 0"
          type="matrix"
          in="shadowBlurOuter1"
        ></feColorMatrix>
      </filter>
    </defs>
    <g
      id="页面-1"
      stroke="none"
      stroke-width="1"
      fill="none"
      fill-rule="evenodd"
    >
      <g
        id="3任务文件变更"
        transform="translate(-1017.000000, -460.000000)"
      >
        <g
          id="编组-7"
          transform="translate(28.000000, 169.000000)"
        >
          <g
            id="编组-25"
            transform="translate(989.000000, 291.000000)"
          >
            <rect
              id="矩形"
              :stroke="'var(--ant-colorBorderSecondary)'"
              :fill="'var(--ant-colorBgContainer)'"
              x="0.5"
              y="0.5"
              width="197"
              height="126.695652"
              rx="6"
            ></rect>
            <rect
              id="矩形"
              :fill="'var(--ant-colorBgContainer)'"
              x="8.60869565"
              y="11.4782609"
              width="48.7826087"
              height="106.173913"
            ></rect>
            <rect
              id="矩形"
              :fill="'var(--ant-colorPrimary)'"
              x="12.9130435"
              y="17.2173913"
              width="40.173913"
              height="7.17391304"
              rx="1"
            ></rect>
            <rect
              id="矩形备份-8"
              :fill="'var(--ant-colorBgLayout)'"
              x="12.9130435"
              y="37.3043478"
              width="20.173913"
              height="7.17391304"
              rx="1"
            ></rect>
            <rect
              id="矩形备份-10"
              :fill="'var(--ant-colorBgLayout)'"
              x="12.9130435"
              y="57.3043478"
              width="20.173913"
              height="7.17391304"
              rx="1"
            ></rect>
            <rect
              id="矩形备份-5"
              :fill="'var(--ant-colorBgLayout)'"
              x="12.9130435"
              y="27.2608696"
              width="40.173913"
              height="7.17391304"
              rx="1"
            ></rect>
            <rect
              id="矩形备份-9"
              :fill="'var(--ant-colorBgLayout)'"
              x="12.9130435"
              y="47.3478261"
              width="40.173913"
              height="7.17391304"
              rx="1"
            ></rect>
            <rect
              id="矩形备份-11"
              :fill="'var(--ant-colorBgLayout)'"
              x="12.9130435"
              y="67.3478261"
              width="40.173913"
              height="7.17391304"
              rx="1"
            ></rect>
            <rect
              id="矩形"
              :fill="'var(--ant-colorBgContainer)'"
              x="61.6956522"
              y="11.4782609"
              width="126.26087"
              height="106.173913"
            ></rect>
            <use
              id="矩形"
              stroke-opacity="0.46875"
              :stroke="'var(--ant-colorBorder)'"
              mask="url(#mask-2)"
              stroke-width="2"
              :fill="'var(--ant-colorPrimaryBg)'"
              opacity="0.529228743"
              stroke-dasharray="3,2"
              xlink:href="#path-1"
            ></use>
            <g
              id="鼠标箭头"
              transform="translate(48.782609, 21.521739)"
              fill-rule="nonzero"
            >
              <g id="路径">
                <use
                  fill="black"
                  fill-opacity="1"
                  filter="url(#filter-4)"
                  xlink:href="#path-3"
                ></use>
                <use
                  :stroke="'var(--ant-colorWhite)'"
                  stroke-width="1"
                  :fill="'var(--ant-colorText)'"
                  xlink:href="#path-3"
                ></use>
              </g>
            </g>
          </g>
        </g>
      </g>
    </g>
  </svg>
</template>
