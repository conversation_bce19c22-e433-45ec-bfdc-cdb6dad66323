<script setup lang="ts">
  import { useAsyncState, useVModel } from '@vueuse/core';

  import codeGeneratecomponentMarketApi from '@/apis/code-generate/componentMarket';

  const props = defineProps<{
    value?: string;
    disabled?: boolean;
    placeholder?: string;
  }>();

  const valueModel = useVModel(props, 'value');
  const { state: options } = useAsyncState(
    async () => {
      const res = await codeGeneratecomponentMarketApi.typeEnum();
      return (res.data.data ?? []).map((item) => ({
        label: item.value,
        value: item.key,
      }));
    },
    [],
    {
      immediate: true,
    },
  );
</script>

<template>
  <ASelect
    v-model:value="valueModel"
    :allow-clear="true"
    :disabled="props.disabled"
    :placeholder="props.placeholder"
    :options="options"
  />
</template>
