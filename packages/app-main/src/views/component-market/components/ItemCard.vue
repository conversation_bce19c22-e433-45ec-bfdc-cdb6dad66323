<script setup lang="ts">
  import { CodeOutlined, Html5Outlined } from '@ant-design/icons-vue';
  import { useAsyncState } from '@vueuse/core';
  import { message } from 'ant-design-vue';
  import type { MenuInfo } from 'ant-design-vue/es/menu/src/interface';
  import to from 'await-to-js';
  import dayjs from 'dayjs';
  import { sample } from 'lodash-es';
  import { MdPreview } from 'md-editor-v3';
  import { computed } from 'vue';

  import codeGeneratecomponentMarketApi, {
    type ListItem,
  } from '@/apis/code-generate/componentMarket';
  import GfSvg from '@/assets/images/gf.svg';
  import ImgPreview from '@/components/Upload/ImgPreview.vue';
  import { useCurrentDark } from '@/stores/currentDark';

  import 'md-editor-v3/lib/preview.css';

  /** 是否处于暗黑模式 */
  const currentDark = useCurrentDark();

  const props = defineProps<{
    itemData: ListItem;
    programmeId: string;
  }>();
  const emit = defineEmits<{
    (e: 'update:itemData', val: ListItem): void;
  }>();

  const router = useRouter();

  const {
    state: detailState,
    execute: executeDetail,
    isLoading: isDetailLoading,
  } = useAsyncState(
    async () => {
      const res = await codeGeneratecomponentMarketApi.detail({
        id: props.itemData.id,
        programmeId: props.programmeId,
      });
      const data = res.data.data;

      if (data) {
        emit('update:itemData', data);
      }

      return data;
    },
    null,
    { immediate: false },
  );

  const detail = computed(() => Object.assign({}, props.itemData, detailState.value));

  const isDetailDrawerOpen = ref(false);
  const openDetailDrawer = () => {
    isDetailDrawerOpen.value = true;
    executeDetail();
  };

  /** 触发更新菜单事件 */
  const emitUpdateMenu = () => {
    const event = new CustomEvent('updateMenu');
    window.dispatchEvent(event);
  };

  const isInstalling = ref(false);
  const installComponent = async () => {
    isInstalling.value = true;
    const [err, res] = await to(
      codeGeneratecomponentMarketApi.install({
        id: props.itemData.id,
        programmeId: props.programmeId,
      }),
    );
    isInstalling.value = false;

    if (err || !res.data.success) {
      return;
    }

    executeDetail();
    emitUpdateMenu();
    message.success(`${props.itemData.name} 安装成功`);
  };

  const isUninstallConfirming = ref(false);
  const isOpenUninstallConfirm = ref(false);
  const uninstallComponent = async () => {
    isUninstallConfirming.value = true;
    const [err, res] = await to(
      codeGeneratecomponentMarketApi.uninstall({
        id: props.itemData.id,
        programmeId: props.programmeId,
      }),
    );
    isUninstallConfirming.value = false;

    if (err || !res.data.success) {
      return;
    }

    executeDetail();
    emitUpdateMenu();
    message.success(`${props.itemData.name} 移除成功`);
    isDetailDrawerOpen.value = false;
  };

  const handleIncrementalDiff = (exportType: 'web' | 'backend') => {
    const { href } = router.resolve({
      name: 'component-market-incremental-diff',
      query: {
        componentId: props.itemData.id,
        exportType,
        programmeId: props.programmeId,
      },
    });
    window.open(href);
  };

  const handleInstallMenuClick = (info: MenuInfo) => {
    if (info.key === 'web') {
      handleIncrementalDiff('web');
    } else if (info.key === 'backend') {
      handleIncrementalDiff('backend');
    }
  };

  const nameBgColor = [
    {
      background: 'linear-gradient(224deg, rgba(166, 123, 252, 1) 0%, rgba(118, 75, 247, 1) 100%)',
    },
    {
      background: 'linear-gradient(44deg, rgba(37, 129, 255, 1) 0%, rgba(74, 191, 255, 1) 100%)',
    },
    {
      background: 'linear-gradient(44deg, rgba(0, 196, 162, 1) 0%, rgba(17, 239, 200, 1) 100%)',
    },
  ];
  const getNameBgColor = (index?: number) => {
    const _index = index || 0;
    const __index = _index % nameBgColor.length;
    return nameBgColor[__index];
  };
  // 返回按钮文本
  const getInstallBtnText = (isInstalled?: boolean) => {
    if (isInstalling.value || isUninstallConfirming.value) {
      return isUninstallConfirming.value ? '卸载中...' : '安装中...';
    }
    return isInstalled ? '已安装' : '立即安装';
  };
</script>

<template>
  <ACard
    class="group relative aspect-ratio-[1.5/1] w-full border border-[#e5e6eb] rounded-8px border-dashed bg-white shadow-none transition-all duration-300 hover:shadow-[0_4px_16px_rgba(0,0,0,0.1)]"
    :bordered="true"
    hoverable
    :body-style="{
      padding: '20px 24px 20px 24px',
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'space-around',
    }"
    @click="openDetailDrawer"
  >
    <div class="relative mb-20px flex items-start justify-between">
      <div
        class="h-50px w-50px flex items-center justify-center rounded-xl shadow-[0_4px_12px_0_rgba(90,109,255,0.10)]"
        :style="getNameBgColor(props.itemData?.index)"
      >
        <span class="text-24px font-600 text-white">
          {{ props.itemData?.prefixName || props.itemData?.name[0] || '--' }}
        </span>
      </div>
      <ATooltip
        v-if="props.itemData.isOfficial"
        title="官方"
      >
        <!-- <span
          class="absolute right-[-24px] top-[-20px] z-2 h-22px flex items-center rounded-bl-lg rounded-tr-lg from-[#ffb400] to-[#ff9900] bg-gradient-to-r px-2.5 py-0.5 text-12px font-500 leading-18px tracking-wider text-white shadow-[0_2px_8px_rgba(255,180,0,0.08)]">
          官方
        </span> -->
        <span class="absolute right-[0] top-[-20px] z-10 text-12px font-500 text-[#fff]">官方</span>
        <img
          class="absolute right-[-4px] top-[-22px] z-1 h-30px w-32px"
          :src="GfSvg"
        />
      </ATooltip>
    </div>
    <div class="mb-1 text-16px font-600 text-[#1d2129]">
      {{ props.itemData.name || '未命名组件' }}
    </div>
    <div class="mb-3 overflow-hidden text-ellipsis whitespace-nowrap text-13px text-[#86909c]">
      {{ props.itemData.description || '暂无组件简介' }}
    </div>
    <div class="mb-20px">
      <APopover
        v-if="props.itemData?.tags?.length"
        placement="right"
      >
        <template #content>
          <div class="max-w-320px">
            <ATag
              v-for="tag in props.itemData.tags"
              :key="tag"
              class="mr-2 rounded-4px border-none bg-[#f4f6fa] px-1.75 text-12px text-[#5a6dff]"
            >
              {{ tag }}
            </ATag>
          </div>
        </template>
        <div class="overflow-hidden text-ellipsis whitespace-nowrap">
          <ATag
            v-for="tag in props.itemData.tags"
            :key="tag"
            class="mr-2 rounded-4px border-none bg-[#f4f6fa] px-1.75 text-12px text-[#5a6dff]"
          >
            {{ tag }}
          </ATag>
        </div>
      </APopover>
      <ATag
        v-else
        class="mr-2 rounded-4px border-none bg-[#f4f6fa] px-1.75 text-12px text-[#5a6dff]"
      >
        {{ props.itemData.name || '未命名组件' }}
      </ATag>
    </div>
    <div class="flex items-center justify-between">
      <div class="flex items-center text-13px text-[#c9cdd4]">
        <CloudDownloadOutlined style="margin-right: 4px" />
        <span>{{ `${props.itemData.installCount ?? 0}人安装` }}</span>
      </div>
      <AButton
        type="primary"
        shape="round"
        class="h-34px rounded-2xl border-none bg-[rgba(49,115,255,0.1)] px-6 text-14px font-500 text-[#3173ff] shadow-none transition-colors duration-200 focus:bg-[#e5eaff] hover:bg-[#e5eaff] hover:text-[#3d5afe]"
        :class="{
          'opacity-60 text-[#a5a7b8] bg-[rgba(169, 169, 169, 0.1)] hover:important-bg-[#e5eaff] hover:important-text-[#a5a7b8] hover:opacity-90':
            props.itemData.isInstalled,
        }"
        :disabled="isInstalling || isUninstallConfirming"
        :title="props.itemData.isInstalled ? '组件已安装 请勿重复安装' : ''"
        @click.stop="installComponent"
      >
        {{ getInstallBtnText(props.itemData.isInstalled) }}
      </AButton>
    </div>
  </ACard>

  <ADrawer
    v-model:open="isDetailDrawerOpen"
    title="详细信息"
    placement="right"
    :width="612"
    :body-style="{ padding: '0px' }"
  >
    <ASpin :spinning="isDetailLoading">
      <div class="relative p-6 space-y-4">
        <div class="flex items-end justify-between">
          <div class="w-[calc(100%-120px)] flex items-start">
            <div class="h-[78px] w-[78px]">
              <div
                class="h-full w-full flex items-center justify-center overflow-hidden rounded-[var(--ant-borderRadiusLG)]"
              >
                <!-- <ImgPreview :id="detail.icon" img-class="object-cover" /> -->
                <div
                  class="h-full w-full flex items-center justify-center rounded-18px shadow-[0_4px_12px_0_rgba(90,109,255,0.10)]"
                  :style="getNameBgColor(detail?.index)"
                >
                  <span class="text-36px font-600 text-white">
                    {{ detail?.prefixName || detail?.name[0] || '--' }}
                  </span>
                </div>
              </div>
            </div>

            <div class="w-[calc(100%-78px)] flex-1 px-[14px] space-y-2">
              <div
                class="mt-5px flex items-start text-base font-600 leading-5 text-[var(--ant-colorText)] space-x-2"
              >
                <div
                  v-if="detail.isOfficial"
                  class="rounded-[var(--ant-borderRadiusXS)] from-[#ffb349] to-[#ff8329] bg-gradient-to-r px-[4px] text-sm font-normal text-white"
                >
                  官方
                </div>
                <div class="flex-1">{{ detail.name }}</div>
              </div>
              <div class="mb-20px">
                <APopover
                  v-if="detail.tags?.length"
                  placement="right"
                >
                  <template #content>
                    <div class="max-w-320px">
                      <ATag
                        v-for="tag in detail.tags"
                        :key="tag"
                        class="mr-2 rounded-4px border-none bg-[#f4f6fa] px-1.75 text-12px text-[#5a6dff]"
                      >
                        {{ tag }}
                      </ATag>
                    </div>
                  </template>
                  <div class="overflow-hidden text-ellipsis whitespace-nowrap">
                    <ATag
                      v-for="tag in detail.tags"
                      :key="tag"
                      class="mr-2 rounded-4px border-none bg-[#f4f6fa] px-1.75 text-12px text-[#5a6dff]"
                    >
                      {{ tag }}
                    </ATag>
                  </div>
                </APopover>
                <ATag
                  v-else
                  class="mr-2 rounded-4px border-none bg-[#f4f6fa] px-1.75 text-12px text-[#5a6dff]"
                >
                  {{ detail.name || '未命名组件' }}
                </ATag>
              </div>
              <div class="text-sm font-400 leading-5 text-[var(--ant-colorTextTertiary)]">
                <span>{{ detail.installCount ?? 0 }}人安装</span>
                <ADivider type="vertical" />
                <span>组件类型：{{ detail.typeName }}</span>
              </div>
            </div>
          </div>
          <div>
            <ADropdownButton
              class="my-dropdown-btn"
              :class="{ 'my-dropdown-btn-danger': detail.isInstalled }"
              :loading="isInstalling || isUninstallConfirming"
              trigger="click"
              :danger="detail.isInstalled"
              @click.stop="detail.isInstalled ? uninstallComponent() : installComponent()"
            >
              {{ detail.isInstalled ? '卸载组件' : '立即安装' }}
              <template #overlay>
                <AMenu @click="handleInstallMenuClick">
                  <AMenuItem
                    key="web"
                    :disabled="props.itemData.type === 'backend'"
                  >
                    <span class="text-[var(--ant-cyan)]">
                      <Html5Outlined />
                      安装前端到本地
                    </span>
                  </AMenuItem>
                  <AMenuItem
                    key="backend"
                    :disabled="props.itemData.type === 'web'"
                  >
                    <span class="text-[var(--ant-purple)]">
                      <CodeOutlined />
                      安装后端到本地
                    </span>
                  </AMenuItem>
                </AMenu>
              </template>
              <template #icon>
                <DownOutlined />
              </template>
            </ADropdownButton>
          </div>
        </div>

        <div>
          <ADivider />
        </div>

        <div>
          <div class="block-title">基本信息</div>
          <div class="block-content">
            <div class="text-[var(--ant-colorTextSecondary)]">
              {{ detail.intro || '无' }}
            </div>
          </div>
        </div>

        <div>
          <div class="block-title">详细参数</div>
          <div class="block-content">
            <ADescriptions
              :column="1"
              size="small"
              :colon="false"
              :label-style="{
                width: '96px',
              }"
            >
              <ADescriptionsItem label="负责人">{{ detail.manager || '-' }}</ADescriptionsItem>
              <ADescriptionsItem label="联系方式">{{ detail.contact || '-' }}</ADescriptionsItem>
              <ADescriptionsItem label="版本">{{ detail.version || '-' }}</ADescriptionsItem>
              <ADescriptionsItem label="大小">{{ detail.fileSize || '-' }}kb</ADescriptionsItem>
              <ADescriptionsItem label="最后更新">
                {{
                  detail.updateTime ? dayjs(detail.updateTime).format('YYYY-MM-DD HH:mm:ss') : '-'
                }}
              </ADescriptionsItem>
              <ADescriptionsItem label="发布时间">
                {{
                  detail.createTime ? dayjs(detail.createTime).format('YYYY-MM-DD HH:mm:ss') : '-'
                }}
              </ADescriptionsItem>
              <ADescriptionsItem label="总安装次数">
                {{ (detail.installCount ?? 0) + (detail.localInstallCount ?? 0) }}人
                <span class="opacity-40">
                  （{{ detail.installCount ?? 0 }}人安装 +
                  {{ detail.localInstallCount ?? 0 }}人增量安装）
                </span>
              </ADescriptionsItem>
            </ADescriptions>
          </div>
        </div>

        <div>
          <div class="block-title">功能预览</div>
          <div class="block-content mx-auto my-0 w-[calc(100%-12px)]">
            <div v-if="!detail.imgUrl?.length">无</div>
            <ACarousel
              v-else
              :autoplay="detail.imgUrl?.length > 1"
            >
              <div
                v-for="imgId in detail.imgUrl"
                :key="imgId"
              >
                <div class="min-h-288px w-full flex items-center justify-center">
                  <ImgPreview
                    :id="imgId"
                    can-preview
                    img-class="object-cover"
                  />
                </div>
              </div>
            </ACarousel>
          </div>
        </div>

        <div>
          <div class="block-title">完整介绍</div>
          <div class="block-content">
            <div>
              <MdPreview
                :theme="currentDark.isDark ? 'dark' : 'light'"
                :model-value="detail.description || '无'"
                :code-foldable="false"
              />
            </div>
          </div>
        </div>
      </div>
    </ASpin>
  </ADrawer>
</template>

<style lang="less" scoped>
  .ant-card {
    background: linear-gradient(180deg, rgba(211, 236, 255, 0.7) 0%, rgba(255, 255, 255, 1) 100%);
    background-size: 100% 30px;
    background-repeat: no-repeat;
    border: 1px solid rgba(223, 227, 235, 0.6);
    border-top: 1px solid rgba(211, 236, 255, 0.7);
  }

  .my-dropdown-btn {
    :deep(.ant-btn) {
      border: solid 1px var(--ant-blue);

      &:not(:last-child) {
        border-start-start-radius: 16px;
        border-end-start-radius: 16px;
        color: var(--ant-blue);
      }

      &:last-child {
        border-start-end-radius: 16px;
        border-end-end-radius: 16px;
        color: var(--ant-blue);
      }
    }

    &.my-dropdown-btn-danger {
      :deep(.ant-btn) {
        border: solid 1px var(--ant-red);

        &:not(:last-child) {
          color: var(--ant-red);
        }

        &:last-child {
          color: var(--ant-red);
        }
      }
    }
  }

  .block-title {
    position: relative;
    padding-left: 10px;
    margin-bottom: 12px;
    height: 20px;
    font-size: 14px;
    font-weight: 600;
    color: var(--ant-colorText);
    line-height: 20px;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 2px;
      height: 14px;
      background-image: linear-gradient(
        to bottom,
        var(--ant-colorPrimaryActive),
        var(--ant-colorPrimary)
      );
      clip-path: polygon(0 0, 100% 10%, 100% 90%, 0 100%);
    }
  }

  .block-content {
    color: var(--ant-colorText);

    &:deep(.ant-descriptions-small .ant-descriptions-row > td) {
      padding-bottom: 6px;
    }

    &:deep(.ant-descriptions .ant-descriptions-item-label) {
      color: var(--ant-colorTextTertiary);
    }

    &:deep(.ant-descriptions .ant-descriptions-item-content) {
      color: var(--ant-colorText);
    }

    &:deep(.md-editor) {
      border-radius: var(--ant-borderRadiusLG);
      background-color: var(--ant-colorBgLayout);

      .md-editor-preview h1,
      .md-editor-preview h2,
      .md-editor-preview h3,
      .md-editor-preview h4,
      .md-editor-preview h5,
      .md-editor-preview h6 {
        margin: 14px 0;
      }
    }

    &:deep(.md-editor-preview-wrapper) {
      opacity: 0.8;
      padding: 20px;
    }

    &:deep(.md-editor .md-editor-preview) {
      & > *:first-child {
        margin-top: 0;
      }

      & > *:last-child {
        margin-bottom: 0;
      }
    }

    &:deep(.ant-carousel .slick-dots-bottom) {
      padding: 12px 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.2);
    }
  }
</style>
