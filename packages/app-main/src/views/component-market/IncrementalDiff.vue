<!-- eslint-disable import/newline-after-import -->
<script setup lang="tsx">
  import {
    CheckCircleOutlined,
    EditOutlined,
    FileOutlined,
    FolderOpenOutlined,
    FolderOutlined,
  } from '@ant-design/icons-vue';
  import { useElementBounding, useStorage, useWindowSize } from '@vueuse/core';
  // @ts-ignore
  import { useNProgress } from '@vueuse/integrations/useNProgress';
  import { type FormInstance, message, Modal } from 'ant-design-vue';
  import type { DataNode, EventDataNode } from 'ant-design-vue/es/tree';
  import type { Key } from 'ant-design-vue/es/vc-tree/interface';
  import to from 'await-to-js';
  import axios from 'axios';
  import Split from 'split.js';
  import { z } from 'zod';

  import attachmentApi from '@/apis/attachment';
  import codeGeneratecomponentMarketApi from '@/apis/code-generate/componentMarket';

  import BeautyLoader from './components/BeautyLoader.vue';
  import DoneIcon from './components/DoneIcon.vue';
  import EmptyIcon from './components/EmptyIcon.vue';
  import FileDiff from './components/FileDiff.vue';

  const route = useRoute();

  const { height: windowHeight } = useWindowSize();
  const containerRef = ref<HTMLElement>();
  const { top: containerTop } = useElementBounding(containerRef);
  const containerHeight = computed(() =>
    Math.max(windowHeight.value - containerTop.value - 16, 500),
  );

  const getStorageKey = (key: string) => {
    if (!routeQuery.value.success) {
      return key;
    }

    return [
      routeQuery.value.data.componentId,
      routeQuery.value.data.exportType,
      routeQuery.value.data.programmeId,
      routeQuery.value.data.attachmentId,
      routeQuery.value.data.file,
      key,
    ].join('-');
  };
  const clearStorageKeys = () => {
    if (!routeQuery.value.success) {
      return;
    }

    // 清除localStorage中的数据
    const storageKeys = Object.keys(localStorage).filter((key) =>
      key.startsWith(getStorageKey('')),
    );
    storageKeys.forEach((key) => localStorage.removeItem(key));

    // 清除IndexedDB中的数据
    const blobId = getStorageKey('componentBlob');
    deleteBlobFromIndexedDB(blobId).catch((error) => {
      console.error('清除IndexedDB数据失败:', error);
    });
  };

  const RouteQuerySchema = z
    .object({
      attachmentId: z.string().optional(),
      componentId: z.string().optional(),
      exportType: z.enum(['web', 'backend']),
      programmeId: z.string(),
      file: z.string().optional(),
    })
    .catchall(z.unknown());
  const routeQuery = computed(() => RouteQuerySchema.safeParse(route.query));

  let secretKey: string | null = null; // 改为显式声明为可空类型
  const request = computed(() => {
    const instance = axios.create({
      baseURL: `http://localhost:${confirmFormState.value.port}/zion/integration/increment/v1`,
    });

    instance.interceptors.request.use(async (config) => {
      if (!secretKey) {
        try {
          const [err, res] = await to(codeGeneratecomponentMarketApi.signSecretKey());
          if (err || !res.data.data) {
            message.error('获取签名失败');
            throw new Error('Failed to obtain secret key'); // 抛出错误而不是继续处理
          }
          secretKey = res.data.data;
        } catch (error) {
          console.error('密钥获取失败:', error);
          throw error; // 阻止请求继续执行
        }
      }

      config.headers['Merge-Sign'] = secretKey;
      return config;
    });

    // 添加响应拦截器处理可能的密钥失效情况
    instance.interceptors.response.use(
      (response) => response,
      async (error) => {
        if (error.response && error.response.status === 401) {
          // 如果密钥失效，清空并尝试重新获取
          secretKey = null;
        }
        return Promise.reject(error);
      },
    );

    return instance;
  });

  const currentStep = useStorage(getStorageKey('currentStep'), 0);
  const goNextStep = () => {
    currentStep.value++;
  };

  const confirmFormRef = ref<FormInstance>();
  const confirmFormState = useStorage(getStorageKey('formState'), {
    port: routeQuery.value.data?.exportType === 'web' ? 3000 : 8081,
    rootDir: '',
  });
  const isGettingWorkspaceRoot = ref(false);
  const getWorkspaceRoot = async ({ skipError = false }: { skipError?: boolean } = {}) => {
    isGettingWorkspaceRoot.value = true;
    const [err, res] = await to(request.value.get('workspace/root'));
    isGettingWorkspaceRoot.value = false;

    if (err) {
      !skipError &&
        message.error(`请检查 http://localhost:${confirmFormState.value.port} 是否正常启动`);
      return;
    }

    confirmFormRef.value?.clearValidate();
    confirmFormState.value.rootDir = res.data.data;
  };
  onMounted(() => {
    if (!confirmFormState.value.rootDir) {
      getWorkspaceRoot({ skipError: true });
    }

    // 页面加载后立即缓存blob文件
    if (routeQuery.value.success && routeQuery.value.data?.file) {
      cacheBlobFile();
    }
  });

  const cacheBlobFile = async () => {
    if (!routeQuery.value.success || !routeQuery.value.data?.file) {
      return;
    }

    try {
      // 为当前文件创建唯一ID
      const blobId = getStorageKey('componentBlob');

      // 首先尝试从IndexedDB获取
      const savedBlob = await getBlobFromIndexedDB(blobId);

      if (savedBlob) {
        // 从IndexedDB成功获取到已保存的文件
        console.log('从IndexedDB获取到已保存的文件');
        const file = new File([savedBlob], 'component.zip', {
          type: savedBlob.type || 'application/zip',
        });
        componentFile = file;

        // 通知打开此页面的窗口，文件已成功缓存，可以释放blob URL
        if (window.opener) {
          window.opener.postMessage({ type: 'incremental-diff-ready' }, '*');
          console.log('已通知原窗口可以释放blob URL');
        }
      } else {
        // 否则从原始Blob URL获取文件
        try {
          const fileUrl = routeQuery.value.data.file;
          console.log('从Blob URL获取文件:', fileUrl);
          const response = await fetch(fileUrl);
          if (!response.ok) {
            throw new Error(`获取文件失败: ${response.status} ${response.statusText}`);
          }

          const blob = await response.blob();
          const file = new File([blob], 'component.zip', {
            type: blob.type || 'application/zip',
          });
          componentFile = file;

          // 保存到IndexedDB以便页面刷新后使用
          await saveBlobToIndexedDB(blobId, blob);
          console.log('成功将文件保存到IndexedDB');

          // 通知打开此页面的窗口，文件已成功缓存，可以释放blob URL
          if (window.opener) {
            window.opener.postMessage({ type: 'incremental-diff-ready' }, '*');
            console.log('已通知原窗口可以释放blob URL');
          }
        } catch (error) {
          console.error('获取Blob URL文件失败:', error);
          // 这里只提示不抛出错误，让流程继续
          message.warning('Blob URL已过期，请重新上传文件');
        }
      }
    } catch (error) {
      console.error('文件处理失败:', error);
      // 这里只提示不抛出错误，让流程继续
      message.warning('缓存文件处理失败，可能需要重新上传');
    }
  };

  const handleConfirmService = async () => {
    if (!confirmFormRef.value) {
      throw new Error('表单未初始化');
    }

    const [validateErr] = await to(confirmFormRef.value.validate());
    if (validateErr) {
      return;
    }

    const [pingErr] = await to(request.value.get('workspace/root'));
    if (pingErr) {
      message.error(
        `服务不可用，请检查 http://localhost:${confirmFormState.value.port} 是否正常启动`,
      );
      return;
    }

    goNextStep();
  };

  export interface DiffResultItem {
    fileName: string;
    filePath: string;
    fileType: 'file' | 'directory';
    projectCode: string;
    size: number;
    status: 'add' | 'update';
    storePath: string;
  }
  const diffResult = ref<DiffResultItem[]>([]);

  /**
   * 将文件路径列表转换为树形结构
   * @param paths 路径数组
   * @returns 树形结构数据
   */
   
  const pathsToTree = (paths: typeof diffResult.value) => {
    // 创建根节点
    const root: Record<string, any> = {
      key: 'root',
      title: '根目录',
      value: 'root',
      children: [],
      rawData: null,
      isLeaf: false,
      status: 'add', // 初始状态设为add
    };

    // 用于快速查找已存在的节点
    const nodeMap: Record<string, any> = {
      root: root,
    };

    // 处理每一个路径
    paths.forEach((item) => {
      let currentStatus = item.status;
      // 拆分路径为各个部分
      const parts = item.filePath.split('/').filter(Boolean);
      let currentPath = '';
      let parentId = 'root';

      // 遍历路径的每一部分
      parts.forEach((part, index) => {
        currentPath = currentPath ? `${currentPath}/${part}` : part;
        const nodeId = currentPath;

        // 检查节点是否已经存在
        if (!nodeMap[nodeId]) {
          const isLastPart = index === parts.length - 1;
          const isFile = isLastPart && item.fileType === 'file';

          // 创建新节点
          const node = {
            key: nodeId,
            title: part,
            value: nodeId,
            children: [],
            rawData: isLastPart ? item : null,
            isLeaf: isFile,
            selectable: true,
            status: isLastPart ? currentStatus : 'add', // 最后节点使用当前状态
          };

          // 向上传播状态
          if (currentStatus === 'update') {
            let parent = nodeMap[parentId];
            while (parent && parent.status !== 'update') {
              parent.status = 'update';
              parent = nodeMap[parent.value.split('/').slice(0, -1).join('/') || 'root'];
            }
          }

          nodeMap[parentId].children.push(node);
          nodeMap[nodeId] = node;
        } else if (index === parts.length - 1) {
          // 更新最后节点的状态
          nodeMap[nodeId].status = currentStatus;
          nodeMap[nodeId].rawData = item;

          // 向上传播状态
          if (currentStatus === 'update') {
            let parent = nodeMap[parentId];
            while (parent && parent.status !== 'update') {
              parent.status = 'update';
              parent = nodeMap[parent.value.split('/').slice(0, -1).join('/') || 'root'];
            }
          }
        }

        parentId = nodeId;
      });
    });

    return root.children;
  };

  const treeData = ref<any[]>([]);
  const expandedKeys = ref<Key[]>([]);
  const autoExpandParent = ref(true);

  const handleExpandChange = (keys: Key[]) => {
    expandedKeys.value = keys;
    autoExpandParent.value = false;
  };

  const {
    progress: initProgress,
    start: startInitProgress,
    done: doneInitProgress,
  } = useNProgress(null, {
    trickleSpeed: 200,
    showSpinner: false,
    parent: '#invisible-nprogress-container',
  });

  const incrementalWaitProgress = ref(0);
  const incrementalDownloadProgress = ref(0);
  const compareUploadProgress = ref(0);
  const compareWaitProgress = ref(0);
  watch(initProgress, (newVal, oldVal) => {
    const diff = newVal - oldVal;

    if (diff <= 0) return;

    if (incrementalWaitProgress.value < 1) {
      incrementalWaitProgress.value += diff;
    } else {
      compareWaitProgress.value += diff;
    }
  });
  const totalInitProgress = computed(() => {
    const total =
      incrementalWaitProgress.value * 0.35 +
      incrementalDownloadProgress.value * 0.05 +
      compareUploadProgress.value * 0.05 +
      compareWaitProgress.value * 0.55;

    return parseInt(String(Math.min(total, 1) * 10000)) / 100;
  });

  let componentFile: any;
  const isInitializingDiff = ref(false);
  const initDiff = async () => {
    if (!routeQuery.value.success) {
      throw new Error('参数错误');
    }

    startInitProgress.value = null;
    incrementalWaitProgress.value = 0;
    incrementalDownloadProgress.value = 0;
    compareUploadProgress.value = 0;
    compareWaitProgress.value = 0;

    if (routeQuery.value.data.componentId) {
      isInitializingDiff.value = true;
      await nextTick();
      startInitProgress();
      const [downloadErr, downloadRes] = await to(
        codeGeneratecomponentMarketApi.incremental(
          {
            componentId: routeQuery.value.data.componentId,
            exportType: routeQuery.value.data.exportType,
            programmeId: routeQuery.value.data.programmeId,
          },
          ({ progress }) => {
            incrementalDownloadProgress.value = progress ?? 0;
          },
        ),
      );
      doneInitProgress();
      await nextTick();
      incrementalWaitProgress.value = 1;
      isInitializingDiff.value = false;
      if (downloadErr) {
        message.error('获取安装文件失败');
        return;
      }
      componentFile = downloadRes.data;
    }

    if (routeQuery.value.data.attachmentId) {
      isInitializingDiff.value = true;
      await nextTick();
      startInitProgress();
      const [downloadErr, downloadRes] = await to(
        attachmentApi.downloadById(routeQuery.value.data.attachmentId, {
          onDownloadProgress: ({ progress }) => {
            incrementalDownloadProgress.value = progress ?? 0;
          },
        }),
      );
      doneInitProgress();
      await nextTick();
      incrementalWaitProgress.value = 1;
      isInitializingDiff.value = false;

      if (downloadErr) {
        message.error('获取附件失败');
        return;
      }

      componentFile = downloadRes.data;
    }

    if (routeQuery.value.data.file) {
      isInitializingDiff.value = true;
      await nextTick();
      startInitProgress();

      // 文件可能已经在onMounted中缓存
      if (!componentFile) {
        // 如果onMounted中没有成功缓存，再尝试一次
        try {
          await cacheBlobFile();
        } catch (error) {
          console.error('文件加载失败:', error);
        }
      }

      incrementalDownloadProgress.value = componentFile ? 1 : 0;

      doneInitProgress();
      await nextTick();
      incrementalWaitProgress.value = 1;
      isInitializingDiff.value = false;
    }

    if (!componentFile) {
      message.error('获取安装文件失败，请联系管理员');
      return;
    }

    isInitializingDiff.value = true;
    await nextTick();
    startInitProgress();
    const [compareErr, compareRes] = await to(
      request.value.post(
        'folder/compare',
        {
          projectCode: routeQuery.value.data.programmeId,
          storePath: confirmFormState.value.rootDir,
          file: componentFile,
        },
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
          onUploadProgress: ({ progress }) => {
            console.log({ progress });
            compareUploadProgress.value = progress ?? 0;
          },
        },
      ),
    );
    doneInitProgress();
    await nextTick();
    incrementalWaitProgress.value = 1;
    incrementalDownloadProgress.value = 1;
    compareUploadProgress.value = 1;
    compareWaitProgress.value = 1;
    await nextTick();
    setTimeout(async () => {
      isInitializingDiff.value = false;

      await nextTick();
      enableSplit();
    }, 300);

    if (compareErr) {
      message.error('获取差异信息失败');
      return;
    }

    diffResult.value = compareRes.data.data;
    // 将文件路径转换为树形结构
    treeData.value = pathsToTree(diffResult.value);

    // 自动展开所有status为update的目录
    const findUpdateNodes = (nodes: any[]): string[] => {
      let keys: string[] = [];
      nodes.forEach((node) => {
        if (node.status === 'update' && !node.isLeaf) {
          keys.push(node.key);
        }
        if (node.children && node.children.length > 0) {
          keys = [...keys, ...findUpdateNodes(node.children)];
        }
      });
      return keys;
    };

    // 只在第一次加载时自动展开目录，保留用户已展开的状态
    if (expandedKeys.value.length === 0) {
      expandedKeys.value = findUpdateNodes(treeData.value);
      autoExpandParent.value = true;
    }
  };
  watch(
    currentStep,
    (newVal) => {
      if (newVal === 2) {
        initDiff();
      }
    },
    { immediate: true },
  );

  const isUpdatingDiff = ref(false);
  const updateDiffResult = async (withLoading = false) => {
    if (!routeQuery.value.success) {
      throw new Error('参数错误');
    }

    if (withLoading) {
      isUpdatingDiff.value = true;
    }

    const [compareErr, compareRes] = await to(
      request.value.post(
        'folder/compare',
        {
          projectCode: routeQuery.value.data.programmeId,
          storePath: confirmFormState.value.rootDir,
          file: componentFile,
        },
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        },
      ),
    );
    isUpdatingDiff.value = false;

    if (compareErr) {
      message.error('获取差异信息失败');
      return;
    }

    diffResult.value = compareRes.data.data;
    // 将文件路径转换为树形结构
    treeData.value = pathsToTree(diffResult.value);

    // 自动展开所有status为update的目录
    const findUpdateNodes = (nodes: any[]): string[] => {
      let keys: string[] = [];
      nodes.forEach((node) => {
        if (node.status === 'update' && !node.isLeaf) {
          keys.push(node.key);
        }
        if (node.children && node.children.length > 0) {
          keys = [...keys, ...findUpdateNodes(node.children)];
        }
      });
      return keys;
    };

    // 只在第一次加载时自动展开目录，保留用户已展开的状态
    if (expandedKeys.value.length === 0) {
      expandedKeys.value = findUpdateNodes(treeData.value);
      autoExpandParent.value = true;
    }
  };

  const handleUpdateFile = async (fileInfo?: {
    file: DiffResultItem;
    content: string;
    status: 'add' | 'update' | 'deleted';
  }) => {
    if (!routeQuery.value.success) {
      throw new Error('参数错误');
    }

    // 如果有文件信息，先在前端快速处理文件变更
    if (fileInfo) {
      // 深拷贝当前文件数据
      const updatedDiffResult = [...diffResult.value];

      // 根据状态处理文件
      if (fileInfo.status === 'deleted') {
        // 如果文件内容相同，从列表中移除
        const index = updatedDiffResult.findIndex(
          (item) => item.filePath === fileInfo.file.filePath,
        );
        if (index !== -1) {
          updatedDiffResult.splice(index, 1);
        }
      } else {
        // 如果是新增或修改，更新状态
        const index = updatedDiffResult.findIndex(
          (item) => item.filePath === fileInfo.file.filePath,
        );
        if (index !== -1) {
          updatedDiffResult[index].status = fileInfo.status;
        }
      }

      // 更新diffResult和树形结构
      diffResult.value = updatedDiffResult;
      treeData.value = pathsToTree(updatedDiffResult);

      // 如果没有文件了，清空当前选中的文件
      if (updatedDiffResult.length === 0) {
        currentFile.value = null;
      } else if (currentFile.value && fileInfo.status === 'deleted') {
        // 如果当前文件被删除，取消选择
        currentFile.value = null;
      }
    }

    // 然后通过API获取最新状态，保证数据正确
    updateDiffResult();
  };

  const currentFile = ref<DiffResultItem | null>(null);
  const handleSelectTree: (
    selectedKeys: Key[],
    info: {
      event: 'select';
      selected: boolean;
      node: EventDataNode;
      selectedNodes: DataNode[];
      nativeEvent: MouseEvent;
    },
  ) => void = (keys, info) => {
    const rawData = info?.node?.rawData as DiffResultItem | undefined;
    if (!rawData) {
      return;
    }

    currentFile.value = rawData;
  };

  // 计算文件差异统计信息
  const diffStats = computed(() => {
    if (!diffResult.value || diffResult.value.length === 0) {
      return { total: 0, added: 0, updated: 0 };
    }

    const total = diffResult.value.length;
    const added = diffResult.value.filter((item) => item.status === 'add').length;
    const updated = diffResult.value.filter((item) => item.status === 'update').length;

    return { total, added, updated };
  });

  const isUpdatingAllAddFiles = ref(false);
  const updateAllAddFiles = async () => {
    isUpdatingAllAddFiles.value = true;
    const [err] = await to(
      request.value.post(
        'merge/batch',
        diffResult.value
          .filter((item) => item.status === 'add')
          .map((item) => ({
            fileName: item.fileName,
            filePath: item.filePath,
            overwrite: true,
            projectCode: item.projectCode,
            storePath: item.storePath,
          })),
      ),
    );
    await new Promise((resolve) => setTimeout(resolve, 500));
    isUpdatingAllAddFiles.value = false;

    if (err) {
      console.error('覆盖文件失败:', err);
      return;
    }

    message.success('更新文件成功');
    updateDiffResult(true);
  };

  const isUpdatingAllFiles = ref(false);
  const updateAllFiles = async () => {
    isUpdatingAllFiles.value = true;
    const [err] = await to(
      request.value.post(
        'merge/batch',
        diffResult.value.map((item) => ({
          fileName: item.fileName,
          filePath: item.filePath,
          overwrite: true,
          projectCode: item.projectCode,
          storePath: item.storePath,
        })),
      ),
    );
    await new Promise((resolve) => setTimeout(resolve, 500));
    isUpdatingAllFiles.value = false;

    if (err) {
      console.error('覆盖文件失败:', err);
      return;
    }

    message.success('更新全部文件成功');
    updateDiffResult(true);
  };

  const handleFinishInstall = () => {
    Modal.confirm({
      title: '提示',
      content: () => (
        <div class="py-2">
          <div class="mb-4 font-medium">确认完成安装将执行以下操作：</div>
          <div class="mb-4 space-y-1">
            <div class="flex items-start">
              <span class="mr-2 text-[var(--ant-colorPrimary)]">1.</span>
              <span>记录当前安装状态到系统</span>
            </div>
            <div class="flex items-start">
              <span class="mr-2 text-[var(--ant-colorPrimary)]">2.</span>
              <span>只有手动确认过的文件变更被写入到本地</span>
            </div>
          </div>
          <div class="mb-2 font-medium text-[var(--ant-colorWarning)]">请注意：</div>
          <div class="space-y-1">
            <div class="flex items-start">
              <span class="mr-2 text-[var(--ant-colorTextSecondary)]">·</span>
              <span>安装完成后，您可能需要重启应用或执行构建</span>
            </div>
            <div class="flex items-start">
              <span class="mr-2 text-[var(--ant-colorTextSecondary)]">·</span>
              <span>如发现问题，请检查项目依赖是否完整</span>
            </div>
            <div class="flex items-start">
              <span class="mr-2 text-[var(--ant-colorTextSecondary)]">·</span>
              <span>建议立即提交变更到版本控制系统以便跟踪</span>
            </div>
          </div>
        </div>
      ),
      onOk: async () => {
        if (!routeQuery.value.success) {
          throw new Error('参数错误');
        }

        const [err] = await to(
          codeGeneratecomponentMarketApi.incrementalMerge({
            codeType: routeQuery.value.data.componentId ? 'component' : 'integrationCode',
            componentId: routeQuery.value.data.componentId,
            incrementType: routeQuery.value.data.exportType,
            programmeId: routeQuery.value.data.programmeId,
          }),
        );

        if (err) {
          return;
        }

        message.success('已完成本次安装');
        goNextStep();
      },
    });
  };

  const handleClosePage = () => {
    clearStorageKeys();
    window.close();
  };

  const WebNotice = () => (
    <a-typography-paragraph>
      <pre lang="bash">
        <div class="text-[var(--ant-colorSuccessTextActive)]"># 1. 进入主应用目录</div>
        <div>cd packages/app-main</div>
        <br />
        <div class="text-[var(--ant-colorSuccessTextActive)]"># 2. 启动服务 （默认端口：3000）</div>
        <div>npm run dev-integration</div>
      </pre>
    </a-typography-paragraph>
  );
  const BackendNotice = () => (
    <a-typography-paragraph>
      <pre lang="bash">
        <div class="text-[var(--ant-colorSuccessTextActive)]">
          # 本地服务启动：在yaml中添加以下配置
        </div>
        <div>zion:</div>
        <div class="pl-4">dev-mode:</div>
        <div class="pl-8">enable: true</div>
        <br />
        <div class="text-[var(--ant-colorSuccessTextActive)]">
          # 启动本地底座服务会自动加载本地文件读写服务，端口为底座服务端口（默认端口：8081）
        </div>
        <br />
        <div class="text-[var(--ant-colorError)]">注意：生产环境严禁使用该配置</div>
      </pre>
    </a-typography-paragraph>
  );

  // 目录树拖拽功能
  const dirRef = ref<HTMLDivElement | null>(null);
  const editorRef = ref<HTMLDivElement | null>(null);
  let splitInstance: Split.Instance | null = null;
  const enableSplit = () => {
    if (dirRef.value && editorRef.value) {
      if (splitInstance) {
        splitInstance.destroy();
      }

      splitInstance = Split([dirRef.value, editorRef.value], {
        sizes: [0, 100],
        minSize: [320, 0],
        expandToMin: true,
        gutterSize: 4,
        direction: 'horizontal',
      });
    }
  };

  // IndexedDB工具函数
  const DB_NAME = 'componentInstallDB';
  const STORE_NAME = 'blobStore';
  const DB_VERSION = 1;

  // 打开数据库连接
  const openDatabase = (): Promise<IDBDatabase> => {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(DB_NAME, DB_VERSION);

      request.onerror = (event: Event) => {
        console.error('IndexedDB打开失败:', event);
        reject('无法打开IndexedDB数据库');
      };

      request.onsuccess = () => {
        const db = request.result;
        resolve(db);
      };

      request.onupgradeneeded = () => {
        const db = request.result;
        // 如果数据库不存在，创建对象存储
        if (!db.objectStoreNames.contains(STORE_NAME)) {
          db.createObjectStore(STORE_NAME, { keyPath: 'id' });
        }
      };
    });
  };

  // 保存Blob数据到IndexedDB
  const saveBlobToIndexedDB = async (id: string, blob: Blob): Promise<boolean> => {
    try {
      const db = await openDatabase();
      return new Promise((resolve, reject) => {
        const transaction = db.transaction([STORE_NAME], 'readwrite');
        const store = transaction.objectStore(STORE_NAME);

        // 保存Blob对象以及元数据
        const request = store.put({ id, blob, timestamp: Date.now() });

        request.onsuccess = () => resolve(true);
        request.onerror = (event: Event) => {
          console.error('保存到IndexedDB失败:', event);
          reject('保存文件失败');
        };

        transaction.oncomplete = () => db.close();
      });
    } catch (error) {
      console.error('IndexedDB操作失败:', error);
      return false;
    }
  };

  // 从IndexedDB获取Blob数据
  const getBlobFromIndexedDB = async (id: string): Promise<Blob | null> => {
    try {
      const db = await openDatabase();
      return new Promise((resolve, reject) => {
        const transaction = db.transaction([STORE_NAME], 'readonly');
        const store = transaction.objectStore(STORE_NAME);

        const request = store.get(id);

        request.onsuccess = () => {
          if (request.result) {
            resolve(request.result.blob);
          } else {
            resolve(null);
          }
        };

        request.onerror = (event: Event) => {
          console.error('从IndexedDB获取失败:', event);
          reject('读取文件失败');
        };

        transaction.oncomplete = () => db.close();
      });
    } catch (error) {
      console.error('IndexedDB操作失败:', error);
      return null;
    }
  };

  // 从IndexedDB删除数据
  const deleteBlobFromIndexedDB = async (id: string): Promise<boolean> => {
    try {
      const db = await openDatabase();
      return new Promise((resolve, reject) => {
        const transaction = db.transaction([STORE_NAME], 'readwrite');
        const store = transaction.objectStore(STORE_NAME);

        const request = store.delete(id);

        request.onsuccess = () => resolve(true);
        request.onerror = (event: Event) => {
          console.error('从IndexedDB删除失败:', event);
          reject('删除文件失败');
        };

        transaction.oncomplete = () => db.close();
      });
    } catch (error) {
      console.error('IndexedDB操作失败:', error);
      return false;
    }
  };
</script>

<template>
  <ACard
    v-if="routeQuery.success"
    :body-style="{ padding: '0' }"
  >
    <div class="pt-4">
      <div class="pointer-events-none mx-auto max-w-4xl">
        <ASteps :current="currentStep">
          <AStep>
            <template #icon>
              <span class="text-lg">01 阅读操作协议</span>
            </template>
          </AStep>
          <AStep>
            <template #icon>
              <span class="text-lg">02 验证服务状态</span>
            </template>
          </AStep>
          <AStep>
            <template #icon>
              <span class="text-lg">03 确认安装文件</span>
            </template>
          </AStep>
          <AStep>
            <template #icon>
              <span class="text-right text-lg">04 完成安装</span>
            </template>
          </AStep>
        </ASteps>
      </div>
      <ADivider class="my-0 mt-4" />
      <div
        ref="containerRef"
        :style="{ height: `${containerHeight}px`, overflow: 'auto' }"
      >
        <!-- 阅读操作协议 -->
        <div
          v-if="currentStep === 0"
          class="mx-auto h-full max-w-4xl pt-6 space-y-4"
        >
          <div class="text-center">
            <ATypographyTitle :level="3">源码安装功能使用指南</ATypographyTitle>
          </div>
          <div
            ref="protocolContainerRef"
            class="relative h-[70%] overflow-auto rounded-[var(--ant-borderRadius)] bg-[var(--ant-colorBgLayout)] p-4"
          >
            <div class="pt-2">
              <ATypography>
                <ATypographyTitle :level="4">一、本地服务启动</ATypographyTitle>
                <ATypographyParagraph>
                  <ol>
                    <li>
                      您需要启动本地开发服务，以便系统获取工作区信息并执行文件操作。
                      <div class="my-2">
                        <WebNotice v-if="routeQuery.data.exportType === 'web'" />
                        <BackendNotice v-if="routeQuery.data.exportType === 'backend'" />
                      </div>
                    </li>
                    <li>
                      <ATypographyText strong>文件操作授权</ATypographyText>
                      ：安装过程中，系统会在您确认后对选定的工作目录进行文件读取和写入操作。
                    </li>
                  </ol>
                </ATypographyParagraph>

                <ATypographyTitle :level="4">二、操作建议</ATypographyTitle>
                <ATypographyParagraph>
                  <ol>
                    <li>
                      <ATypographyText strong>保持干净的工作区</ATypographyText>
                      ：建议在干净的Git工作区中操作，这样如需回退变更会更加方便。
                    </li>
                    <li>
                      <ATypographyText strong>提前备份</ATypographyText>
                      ：对于重要项目，建议提前将代码提交到版本控制系统。
                    </li>
                    <li>
                      <ATypographyText strong>检查环境</ATypographyText>
                      ：确保您的开发环境正常运行，并且本地服务已正确启动。
                    </li>
                  </ol>
                </ATypographyParagraph>
              </ATypography>
            </div>
          </div>
          <div class="pt-3 text-center">
            <AButton
              type="primary"
              @click="goNextStep"
            >
              我已了解，开始安装
            </AButton>
          </div>
        </div>

        <!-- 验证服务状态 -->
        <div
          v-if="currentStep === 1"
          class="mx-auto max-w-4xl pt-10vh"
        >
          <div class="rounded-[var(--ant-borderRadius)] bg-[var(--ant-colorBgLayout)] py-14">
            <div class="mx-auto w-2xl">
              <AForm
                ref="confirmFormRef"
                :model="confirmFormState"
                :label-col="{ style: 'width: 100px' }"
              >
                <AFormItem
                  label="服务端口"
                  name="port"
                  :rules="[
                    {
                      required: true,
                      message: '请输入服务端口',
                      type: 'number',
                      min: 1,
                      max: 65535,
                    },
                  ]"
                >
                  <AInputNumber
                    v-model:value="confirmFormState.port"
                    :min="1"
                    :max="65535"
                    :step="1000"
                    :precision="0"
                    addon-before="http://localhost:"
                    placeholder="请输入服务端口"
                    class="w-full"
                  />
                  <div>
                    <WebNotice v-if="routeQuery.data.exportType === 'web'" />
                    <BackendNotice v-if="routeQuery.data.exportType === 'backend'" />
                  </div>
                </AFormItem>
                <AFormItem
                  label="工作目录"
                  name="rootDir"
                  help="请输入您的当前工作区根目录（即项目git仓库根目录）"
                  :rules="[{ required: true, message: '请输入工作目录' }]"
                >
                  <div class="flex space-x-2">
                    <AInput
                      v-model:value.trim="confirmFormState.rootDir"
                      :disabled="isGettingWorkspaceRoot"
                      class="flex-1"
                    />
                    <AButton
                      :loading="isGettingWorkspaceRoot"
                      @click="getWorkspaceRoot()"
                    >
                      自动获取
                    </AButton>
                  </div>
                </AFormItem>

                <AFormItem
                  label=" "
                  :colon="false"
                >
                  <AButton
                    type="primary"
                    @click="handleConfirmService"
                  >
                    下一步
                  </AButton>
                </AFormItem>
              </AForm>
              <!-- <div>
                <WebNotice v-if="routeQuery.data.exportType === 'web'" />
                <BackendNotice v-if="routeQuery.data.exportType === 'backend'" />
              </div> -->
            </div>
          </div>
        </div>

        <!-- 确认安装文件 -->
        <div
          v-if="currentStep === 2"
          class="h-full"
        >
          <div
            v-if="isInitializingDiff"
            class="h-full w-full flex flex-col items-center justify-center space-y-2"
          >
            <div class="translate-y-[-72px]">
              <BeautyLoader />
            </div>
            <div class="relative z-10 flex items-center space-x-2">
              <AProgress
                type="circle"
                :percent="totalInitProgress"
                :size="20"
              />
              <div class="text-[var(--ant-colorPrimary)]">正在获取安装文件...</div>
            </div>
          </div>
          <div
            v-else
            class="split-container h-full flex"
          >
            <div
              ref="dirRef"
              class="w-[320px] flex flex-col px-2"
            >
              <div class="max-w-[308px] p-4">
                <AButton
                  type="primary"
                  ghost
                  block
                  :loading="isUpdatingAllFiles"
                  @click="updateAllFiles"
                >
                  <template #icon>
                    <EditOutlined />
                  </template>
                  接受全部文件
                </AButton>
                <AButton
                  type="primary"
                  ghost
                  block
                  class="mt-2"
                  :loading="isUpdatingAllAddFiles"
                  @click="updateAllAddFiles"
                >
                  <template #icon>
                    <EditOutlined />
                  </template>
                  接受新增文件
                </AButton>
              </div>
              <ADivider class="my-0" />
              <div class="file-tree w-full flex-1 overflow-auto p-4 px-2">
                <ASpin :spinning="isUpdatingDiff">
                  <ADirectoryTree
                    v-model:expanded-keys="expandedKeys"
                    :tree-data="treeData"
                    :show-line="false"
                    class="min-w-full w-max"
                    :show-icon="true"
                    :auto-expand-parent="autoExpandParent"
                    @select="handleSelectTree"
                    @expand="handleExpandChange"
                  >
                    <template #icon="{ data }">
                      <span v-if="!data.isLeaf">
                        <span
                          v-if="data.status === 'update'"
                          class="text-[var(--ant-colorWarning)]"
                        >
                          <FolderOpenOutlined v-if="expandedKeys.includes(data.key)" />
                          <FolderOutlined v-else />
                        </span>
                        <span
                          v-else-if="data.status === 'add'"
                          class="text-[var(--ant-colorSuccess)]"
                        >
                          <FolderOpenOutlined v-if="expandedKeys.includes(data.key)" />
                          <FolderOutlined v-else />
                        </span>
                        <span v-else>
                          <FolderOpenOutlined v-if="expandedKeys.includes(data.key)" />
                          <FolderOutlined v-else />
                        </span>
                      </span>
                      <span
                        v-else-if="data.status === 'update'"
                        class="text-[var(--ant-colorWarning)]"
                      >
                        M
                      </span>
                      <span
                        v-else-if="data.status === 'add'"
                        class="text-[var(--ant-colorSuccess)]"
                      >
                        U
                      </span>
                      <FileOutlined v-else />
                    </template>
                    <template #title="{ data }">
                      <span
                        class="text-[var(--ant-colorSuccess)]"
                        :class="{ '!text-[var(--ant-colorWarning)]': data.status === 'update' }"
                      >
                        {{ data.title }}
                      </span>
                    </template>
                  </ADirectoryTree>
                </ASpin>
              </div>
            </div>
            <div
              ref="editorRef"
              class="flex flex-1 flex-col"
            >
              <div class="flex items-center justify-between px-4 py-4">
                <div
                  v-if="!isInitializingDiff && diffResult.length > 0"
                  class="flex items-center space-x-4"
                >
                  <span>全部{{ diffStats.total }}个文件</span>
                  <span class="text-[var(--ant-colorSuccess)]">新增{{ diffStats.added }}</span>
                  <span class="text-[var(--ant-colorWarning)]">修改{{ diffStats.updated }}</span>
                </div>
                <div
                  v-else-if="!isInitializingDiff && diffResult.length === 0"
                  class="text-[var(--ant-colorTextSecondary)]"
                >
                  没有发现差异文件，当前项目已是最新版本
                </div>
                <AButton
                  type="primary"
                  @click="handleFinishInstall"
                >
                  <template #icon>
                    <CheckCircleOutlined />
                  </template>
                  完成本次安装
                </AButton>
              </div>
              <ADivider class="my-0" />
              <div class="flex-1">
                <div
                  v-show="!currentFile"
                  class="h-full flex items-center justify-center"
                >
                  <AEmpty class="mb-20vh">
                    <template #description>
                      <div
                        v-if="diffResult.length === 0"
                        class="pt-4"
                      >
                        当前没有需要安装的文件
                      </div>
                      <div
                        v-else
                        class="pt-4"
                      >
                        请先从左侧选择一个文件
                      </div>
                    </template>
                    <template #image>
                      <EmptyIcon />
                    </template>
                  </AEmpty>
                </div>
                <div
                  v-show="currentFile"
                  class="h-full"
                >
                  <FileDiff
                    :request="request"
                    :file="currentFile || undefined"
                    @update:file="handleUpdateFile"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 完成安装 -->
        <div
          v-if="currentStep === 3"
          class="h-full flex items-center justify-center pb-20vh"
        >
          <AResult title="已完成本次安装">
            <template #icon>
              <DoneIcon />
            </template>
            <template #extra>
              <AButton
                key="console"
                type="primary"
                @click="handleClosePage"
              >
                关闭页面
              </AButton>
            </template>
          </AResult>
        </div>
      </div>
    </div>
  </ACard>
  <ACard
    v-else
    title="参数错误"
  >
    <AResult
      status="500"
      title="500"
      sub-title="页面参数错误，请检查参数是否正确"
    ></AResult>
  </ACard>

  <div
    id="invisible-nprogress-container"
    class="hidden"
  ></div>
</template>

<style lang="less" scoped>
  :deep(.file-tree) {
    .ant-tree.ant-tree-directory .ant-tree-treenode-selected:hover::before,
    .ant-tree.ant-tree-directory .ant-tree-treenode-selected::before {
      background-color: var(--ant-colorPrimaryBgHover);
    }

    .ant-tree.ant-tree-directory .ant-tree-treenode-selected .ant-tree-switcher {
      color: unset;
    }

    .ant-tree.ant-tree-directory .ant-tree-treenode .ant-tree-node-content-wrapper {
      white-space: nowrap;
    }
  }

  :deep(.ant-steps-item-icon) {
    margin-inline-end: 0;
  }

  :deep(.ant-steps) {
    .ant-steps-item-finish.ant-steps-item-custom .ant-steps-item-icon > .ant-steps-icon {
      color: var(--ant-colorTextSecondary);
    }
  }

  .split-container:deep(.gutter) {
    height: 100%;
    background-color: var(--ant-colorBorderSecondary);
    background-repeat: no-repeat;
    background-position: 50%;
    border: 1px solid var(--ant-colorBorder);

    &:hover {
      background-color: var(--ant-colorPrimary);
    }

    &.gutter-horizontal {
      background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAeCAYAAADkftS9AAAAIklEQVQoU2M4c+bMfxAGAgYYmwGrIIiDjrELjpo5aiZeMwF+yNnOs5KSvgAAAABJRU5ErkJggg==');
      cursor: col-resize;
      border-left: 0 none;
      border-right: 0 none;

      &:hover {
        background-image: none;
        border-color: var(--ant-colorPrimary);
      }
    }
  }
</style>
