<script setup lang="ts">
  import { useAsyncState } from '@vueuse/core';
  import { Base64 } from 'js-base64';
  import { computed } from 'vue';
  import { useRoute } from 'vue-router';
  import WujieVue3 from 'wujie-vue3';

  import codeGenerateZionApi from '@/apis/code-generate/zion';

  import MicroApp from './components/MicroApp.vue';

  const { bus } = WujieVue3;
  const route = useRoute();
  const { appId } = useAppidAndTenantid();
  const url = computed(() => Base64.decode(decodeURIComponent(route.params.url as string)));

  const { state, isLoading } = useAsyncState(async () => {
    const res = await codeGenerateZionApi.token(appId.value);

    const tokenInfo = res.data.data;

    if (tokenInfo) {
      const { prefix, token, tokenName } = tokenInfo;
      window.sessionStorage.setItem('zion-auth-key', tokenName);
      window.sessionStorage.setItem('zion-auth-value', prefix ? `${prefix} ${token}` : token);
    }

    return tokenInfo;
  }, null);
</script>

<template>
  <div
    v-if="isLoading"
    class="h-[200px] flex items-center justify-center"
  >
    <ASpin />
  </div>
  <MicroApp
    v-else-if="state?.token"
    :full-url="url"
  />
</template>
