<script setup lang="tsx">
  import { theme } from 'ant-design-vue';
  import { Base64 } from 'js-base64';
  import { joinURL } from 'ufo';
  import { computed, ref } from 'vue';
  import { type RouteLocationRaw, useRoute, useRouter } from 'vue-router';
  import { DocElementRectPlugin, EventTargetPlugin } from 'wujie-polyfill';
  import WujieVue from 'wujie-vue3';

  import { useCurrentRouteTab } from '@/stores/currentRouteTab';

  const props = defineProps<{
    baseUrl?: string;
    fullUrl?: string;
  }>();

  const currentRouteTab = useCurrentRouteTab();

  function updateTabTitle(value: undefined | string | Record<string, any>) {
    currentRouteTab.routeTab = value;
  }

  const { useToken } = theme;
  const toggle = ref(true);
  const { token } = useToken();
  const { bus } = WujieVue;
  const route = useRoute();
  const router = useRouter();
  const url = computed(() => {
    let url = props.baseUrl;

    // 如果 url 不是以 http 开头，则拼接当前路由的 fullPath
    if (!url?.startsWith('http')) {
      url = joinURL(window.location.origin, url || '');
    }

    return joinURL(url, route.fullPath.replace(/^\/micro-app\/.*?-center/, '')).replace(
      /\?=.*/,
      '',
    );
  });

  const childBaseUrl = computed(() => {
    const pathMatch = route.params.pathMatch;
    let childBasePath = '';

    if (Array.isArray(pathMatch)) {
      const regex = new RegExp(pathMatch.join('/') + '(?!.*' + pathMatch.join('/') + ')', 'g');
      childBasePath = route.path.replace(regex, '');
    } else {
      childBasePath = route.path;
    }

    return joinURL(window.location.origin, childBasePath);
  });

  const showMainLayout = computed(() => route.query.mainLayout !== 'none');

  const wujieUrl = computed(() => props.fullUrl ?? url.value);
  const wujieName = computed(() =>
    encodeURIComponent(
      Base64.encode(route.name !== 'micro-app--dynamic' ? (route.name as string) : wujieUrl.value),
    ),
  );

  // 主应用监听事件(是否有其他方法刷新？？)
  bus.$on('microAppReload', function () {
    toggle.value = false;
    setTimeout(() => {
      toggle.value = true;
    }, 200);
  });

  type JumpOptions =
    | {
        type: 'jump-self';
        href: string;
        openBlank: boolean;
      }
    | {
        type: 'jump-main';
        to: RouteLocationRaw;
        openBlank: boolean;
      };
  // 暴露子应用跳转方法(暂不处理保活情况)
  const jump = (options: JumpOptions) => {
    if (options.type === 'jump-self') {
      // 子应用跳转子应用自身
      const url = new URL(wujieUrl.value).origin + options.href;
      const microAppDynamicHref = router.resolve({
        name: 'micro-app--dynamic',
        params: { url: encodeURIComponent(Base64.encode(url)) },
        query: {
          mainLayout: url.indexOf('mainLayout=none') > -1 ? 'none' : undefined,
        },
      }).href;

      if (options.openBlank) {
        window.open(window.location.origin + microAppDynamicHref, '_blank');
      } else {
        router.push(microAppDynamicHref);
      }
    } else if (options.type === 'jump-main') {
      // 子应用跳主应用
      if (options.openBlank) {
        window.open(window.location.origin + router.resolve(options.to).href, '_blank');
      } else {
        router.push(options.to);
      }
    }
  };
  // Wujie没有完整的类型定义？暂时需要使用 any
  const WujieVueAsAny = WujieVue as any;

  // TODO: 改造子应用使其进入单例模式
  const MicroApp = () => (
    <WujieVueAsAny
      width="100%"
      height="100%"
      url={wujieUrl.value}
      sync
      alive={false}
      name={wujieName.value}
      plugins={[EventTargetPlugin(), DocElementRectPlugin()]}
      props={{
        baseUrlInWujie: childBaseUrl.value,
        fullUrlInWujie: new URL(wujieUrl.value).origin,
        jump,
        updateTabTitle,
      }}
    ></WujieVueAsAny>
  );
</script>

<template>
  <div
    v-if="showMainLayout"
    class="h-full"
  >
    <div
      class="overflow-auto"
      :style="{
        height: `calc(100% + ${2 * token.padding}px)`,
        marginLeft: `-${token.padding}px`,
        marginRight: `-${token.padding}px`,
        marginTop: `-${token.padding}px`,
        marginBottom: `-${token.padding}px`,
        padding: `${token.padding}px`,
      }"
    >
      <MicroApp v-if="toggle" />
    </div>
  </div>
  <MicroApp v-else-if="!showMainLayout && toggle" />
</template>
