<script setup lang="ts">
import { EditOutlined, PlusOutlined } from '@ant-design/icons-vue';
import { message, Modal } from 'ant-design-vue';
import { cloneDeep } from 'lodash-es';
import { h, ref } from 'vue';
import { to } from 'await-to-js';
import Api, { RESOURCE_STATUS } from '@/apis/code-generate/resources';
import { useRoute, useRouter } from 'vue-router';
import { startingDev } from '@/apis/project/project';


import { useStartDevFormStore } from '@/stores/startDevForm';

import ApplyResourceTrigger from './components/ApplyResourceTrigger.vue';
import ApplyResource from './components/ApplyResource.vue';

const router = useRouter();
const route = useRoute();
const submitLoading = ref(false);
const startDevFormStore = useStartDevFormStore();
const isApplyResourceVisible = ref(false);
const formState = ref({ programmeId: '', ...defaultStep3Form() });
const hasApplyed = ref(false); // 是否已经申请过资源了
const rules = {
  // middleware: [{ required: true, message: '请选择中间件资源' }],
};
const backStep = () => {
  router.push(`/project/${route.params.id}/start-dev/step2`);
};
// 请求参数
const formParam = computed(() => {
  const serverResource = resourceList.value[0];
  serverResource.resourceExtInfo = formState.value.resource
    ? JSON.stringify(formState.value.resource)
    : '';
  const middlewareList = formState.value.middleware?.map((middlewareId: string) => {
    return {
      id: middlewareId,
      resourceExtInfo: '',
    };
  });
  return {
    programmeId: route.params.id as string,
    resourceList: [serverResource, ...middlewareList], // 组合下参数
  };
});

// 进入研发空间
const enterDevSpace = () => {
  let tempProjectInfo = cloneDeep(startDevFormStore.projectInfo);
  if (tempProjectInfo) {
    const { id, sysAppKey } = tempProjectInfo as any;
    startDevFormStore.reset();
    // 跳转项目工作空间
    router.push({
      path: '/component-market',
      query: {
        appId: id,
        sysAppKey: sysAppKey || undefined,
      },
    });
  }
};

// 查看审批列表
const viewAuditList = () => {
  const { href } = router.resolve('/approval');
  window.open(href, '_blank');
};
//  这里提交会提交第一第二步的表单数据
async function handleSubmit() {

  submitLoading.value = true;
  if (!hasApplyed.value && formState.value.resource.name) { // 如果是未申请过资源且选择了资源，则调用申请资源
    await to(Api.resourceApply(formParam.value));
  }
  // 追加状态翻转
  const [err1, res1] = await to(startingDev({ programmeId: route.params.id as string }));
  submitLoading.value = false;
  if (err1 || !res1.data.success) {
    return;
  }
  // 显示成功提示框
  Modal.confirm({
    title: '项目初始化成功',
    content: h('div', [
      h('span', '你可以前往 '),
      h(
        'a',
        {
          onClick: () => {
            Modal.destroyAll();
            viewAuditList();
          },
        },
        '审批列表',
      ),
      h('span', ' 查看'),
    ]),
    okText: '进入研发空间',
    cancelText: '返回项目列表',
    onOk: () => {
      enterDevSpace();
    },
    onCancel: () => {
      router.push('/project');
    },
  });
}
// 存放服务器列表
const resourceList = ref<any>([]);

const middlewareOptions = ref<any>([]);
const getMiddlewareOptions = async () => {
  const [err, res] = await to(Api.resourceList());
  if (err || !res.data.data) {
    return;
  }
  const middlewares = res.data?.data?.middleware || [];
  middlewareOptions.value = middlewares.map((item: any) => ({
    label: `${item.code}:(${item.version})`,
    value: item.id,
    description: item.description || '',
  }));

  const server = res.data?.data?.server || [];
  const serverInfo = server[0] ? server[0] : null;
  serverInfo &&
    resourceList.value.unshift({
      id: serverInfo.id,
      type: serverInfo.type,
      resourceExtInfo: '',
    });
};

getMiddlewareOptions();

// 获取当前项目下的申请资源

const getResourceApplyList = async () => {
  const [err, res] = await to(Api.resourceByProgrammeId(route.params.id as string));
  if (err || !res.data.data || !res.data.data.server || !res.data.data.server.resourceExtInfo) {
    return;
  }
  hasApplyed.value = true;
  formState.value.resource = res.data.data?.server?.resourceExtInfo;
}
getResourceApplyList();
</script>

<template>
  <ASpin :spinning="submitLoading">
    <AForm class="mx-auto mt-20 w-[40%]" :model="formState" :rules="rules" @finish="handleSubmit">

      <AFormItem label="服务器资源">
        <AFlex :gap="20" align="center">
          <ApplyResourceTrigger v-if="!hasApplyed" v-model:value="formState.resource" />
          <span class="info-alert ml-2" v-else>此项目已申请过资源，<a @click="isApplyResourceVisible = true">点击查看信息</a></span>
        </AFlex>
      </AFormItem>

      <AFlex justify="center" :gap="10" class="mt-[100px]">
        <AButton type="primary" @click="backStep">
          上一步
        </AButton>

        <AButton type="primary" html-type="submit" :loading="submitLoading">
          完成
        </AButton>
      </AFlex>
    </AForm>
  </ASpin>
  <!-- 查看资源信息，这里是详情 -->
  <ApplyResource :model-value="formState.resource" mode="view" v-model:visible="isApplyResourceVisible" />
</template>

<style scoped lang="less"></style>
