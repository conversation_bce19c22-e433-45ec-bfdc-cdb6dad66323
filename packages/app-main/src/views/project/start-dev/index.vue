<script setup lang="ts">
import { onUnmounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { getProjectDetailById, type Project } from '@/apis/project/project';
import { useStartDevFormStore } from '@/stores/startDevForm';

const steps = [
  { title: '选择底座版本', path: 'step1' },
  { title: '集成组件', path: 'step2' },
  { title: '申请资源', path: 'step3' },
];

const route = useRoute();
const router = useRouter();
const startDevFormStore = useStartDevFormStore();

// function goStep(index: number) {
//   router.push({ path: `/project/${route.params.id}/start-dev/${steps[index].value}` });
// }
const backToProjectList = () => {
  router.push('/project');
};
const handleStepChange = (current: number) => {
  router.push(`/project/${startDevFormStore.projectInfo?.id}/start-dev/${steps[current].path}`);
};
// 当前激活步骤
const activeStep = computed(() => {
  return Number(route.meta.step) || 0;
});
const getProjectInfo = async () => {
  const res = await getProjectDetailById(route.params.id as string);
  if (res.data && res.data.success) {
    startDevFormStore.setProjectInfo(res.data.data);
  }
};
onUnmounted(() => {
  startDevFormStore.reset();
});

getProjectInfo();
</script>

<template>
  <div class="project-step-page">
    <!-- 顶部 banner 区域，和项目列表页一致 -->
    <div class="mb-5 rounded-[10px] h-146px flex px-[50px] project-step-title-wrapper">
      <div class="mx-auto  w-100% flex flex-col items-start justify-center">
        <div class="mb-10px text-[18px] font-600">
          {{ startDevFormStore.projectInfo?.programmeName ?? '测试项目A' }}
        </div>
        <div class="text-14px text-[#515A6E]">
          研发一体化平台提供通用技术架构能力，组件一键复用能力，代码快速生成能力，提升高业务的开发效率，欢迎一起来解锁～
        </div>
      </div>
    </div>
    <!-- 步骤条 -->
    <card-pane class="min-h-[580px]">
      <AFlex justify="space-between">
        <div class="cursor-pointer text-16px font-normal text-#565865" @click="backToProjectList">
          <LeftCircleOutlined style="color: #d8d8d8; margin-right: 8px" />
          返回
        </div>
      </AFlex>
      <ASteps class="mx-auto mt-[110px] w-[80%]" :current="activeStep" @change="handleStepChange"
        label-placement="vertical">
        <AStep v-for="step in steps" :key="step.path" :title="step.title" />
      </ASteps>
      <RouterView />
      <!-- 步骤内容 -->
    </card-pane>
  </div>
</template>
<style scoped lang="less">
// 这里用样式改吧，不想自定义了
.project-step-page {
  .project-step-title-wrapper {
    background: linear-gradient(293deg,
        #d9e4fd -1%,
        #e9f0fe 6%,
        #fcfdff 40%,
        #ffffff 64%,
        #e3fcff 112%);
  }

  :deep(.ant-steps) {
    .ant-steps-item-icon {
      background: linear-gradient(180deg, #f8f6f6 6%, #eaecf0 100%);

      .ant-steps-icon {
        color: var(--zion-text-base1);
      }
    }

    .ant-steps-item-process {
      .ant-steps-icon {
        color: #1677ff;
      }
    }

    .ant-steps-item-finish .ant-steps-item-container .ant-steps-item-icon {
      background: linear-gradient(180deg,
          rgba(232, 244, 255, 0.2) 0%,
          rgba(174, 192, 255, 0.2) 100%);
      border: 1px solid rgba(49, 115, 255, 0.2) !important;

      .ant-steps-icon {
        color: #1677ff;
      }
    }
  }
}
</style>
