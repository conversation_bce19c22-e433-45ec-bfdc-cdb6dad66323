<script setup lang="ts">
import { PlusOutlined, EditOutlined } from '@ant-design/icons-vue';
import { Form, Button } from 'ant-design-vue';
import { h, computed, ref } from 'vue';
import ApplyResource from './ApplyResource.vue';

const props = defineProps<{
  value?: any
}>();
const emits = defineEmits(['update:value']);

const formItemContext = Form.useInjectFormItemContext();

const modalVisible = ref(false);


function handleClick() {
  modalVisible.value = true;
  // 触发 antd 校验
  formItemContext?.onFieldChange();
}
function handleResourceChange(val: any) {
  emits('update:value', val);
  // 触发 antd 校验
  formItemContext?.onFieldChange();
}
const hasValue = computed(() => !!props.value?.name);
</script>

<template>
  <div>
    <a-button
      type="primary"
      ghost
      @click="handleClick"
    >
    <!-- 注意这里要用插槽，不能:icon -->
      <template #icon>
        <EditOutlined v-if="hasValue" />
        <PlusOutlined v-else />
      </template>
      {{ hasValue ? '修改资源' : '申请资源' }}
    </a-button>
    <span class="warning-alert ml-2">只能申请一次资源</span>
    <ApplyResource
      :model-value="value"
      v-model:visible="modalVisible"
      @update:modelValue="handleResourceChange"
    />
  </div>
</template>

<style scoped>
.warning-alert {
  color: #faad14;
  font-size: 12px;
}
</style>
