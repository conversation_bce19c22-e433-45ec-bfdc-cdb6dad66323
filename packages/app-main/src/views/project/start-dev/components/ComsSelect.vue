// 组件市场选择
<template>
  <a-select
    v-model:value="innerValue"
    :show-search="true"
    :mode="mode"
    allow-clear
    placeholder="请选择组件"
    :default-active-first-option="false"
    :show-arrow="false"
    :filter-option="false"
    :options="data"
    :field-names="{ label: 'name', value: 'id' }"
    @change="handleChange"
    @search="handleSearch"
    v-bind="$attrs"
  >
    <template
      v-if="fetching"
      #notFoundContent
    >
      <a-spin size="small" />
    </template>
    <template
      v-else-if="searchVal && data.length === 0"
      #notFoundContent
    >
      <a-empty
        description="暂无相关组件"
        :image="Empty.PRESENTED_IMAGE_SIMPLE"
      />
    </template>
    <!-- 自定义渲染 -->
    <template #option="{ name, isOfficial }">
      <a-space>
        <div
          v-if="isOfficial"
          class="rounded-[var(--ant-borderRadiusXS)] from-[#ffb349] to-[#ff8329] bg-gradient-to-r px-[4px] text-sm font-normal text-white"
        >
          官方
        </div>
        {{ name }}
      </a-space>
    </template>
  </a-select>
</template>

<script setup lang="ts">
  import { ref, watch } from 'vue';
  import marketApi from '@/apis/code-generate/componentMarket';
  import type { SelectValue, DefaultOptionType } from 'ant-design-vue/es/select';
  import { Form, Empty } from 'ant-design-vue';
  import { debounce } from 'lodash-es';
  const props = withDefaults(
    defineProps<{
      programmeId: string;
      value?: SelectValue;
      mode?: 'multiple';
    }>(),
    {},
  );
  const formItemContext = Form.useInjectFormItemContext();
  const emits = defineEmits(['update:value']);
  let innerValue = ref<SelectValue>();
  const searchVal = ref('');
  const fetching = ref(false);
  const data = ref<any[]>([]);
  const fetch = debounce(async () => {
    fetching.value = true;
    const res = await marketApi.pageQuery({
      name: searchVal.value,
      programmeId: props.programmeId,
      pageIndex: 1,
      pageSize: 10000, // 目前没有全量查询
    });

    fetching.value = false;
    if (res) {
      data.value = res.data.data ?? [];
    }
  });

  const handleChange = (value: SelectValue, option: DefaultOptionType | DefaultOptionType[]) => {
    emits('update:value', value, option);
    formItemContext?.onFieldChange();

    // 选择后重新搜索全量数据，清空搜索条件
    searchVal.value = '';
    fetch();
  };

  const handleSearch = debounce((value: string) => {
    searchVal.value = value;
    fetch();
  }, 300);

  fetch();
  watch(
    () => props.value,
    (val: SelectValue) => {
      innerValue.value = val;
    },
    {
      immediate: true,
    },
  );
</script>

<style scoped lang="less"></style>
