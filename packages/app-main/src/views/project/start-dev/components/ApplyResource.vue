<script setup lang="ts">
import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons-vue';
import dayjs from 'dayjs';
import { defineEmits, defineProps, h, ref, watch } from 'vue';
import { type Step3Form } from '@/stores/startDevForm';
import type { FormInstance, Rule } from 'ant-design-vue/es/form';
import { Form } from 'ant-design-vue'

type ResourceInfo = Step3Form['resource'];
const props = withDefaults(defineProps<{
  modelValue: ResourceInfo;
  visible: boolean;
  mode?: 'view' | 'edit'; // 这里只提供两种就行了，这里不调用接口
}>(), {
  mode: 'edit'
});
const emits = defineEmits(['update:modelValue', 'update:visible']);
const formItemContext = Form.useInjectFormItemContext();
// 端口操作
function addPort() {
  if (!Array.isArray(localResource.value.port)) localResource.value.port = [];
  localResource.value.port.push({ name: '', port: undefined });
}
function removePort(idx: number) {
  if (Array.isArray(localResource.value.port) && localResource.value.port.length > 1) {
    localResource.value.port.splice(idx, 1);
  }
}
// const generatePort = (item: ResourceInfo['port'][number]) => {
//   item.port = getSecureRandomNumber(90000);
// };
const localResource = ref({
  ...props.modelValue,
  // 确保至少有一个端口
  port: props.modelValue.port && props.modelValue.port.length > 0
    ? [...props.modelValue.port]
    : [{ name: '', port: undefined }]
});
const formRef = ref<FormInstance>();

// 端口名称校验规则
const portNameRules: Rule[] = [
  { required: true, message: '请输入端口名称', trigger: 'blur' },
  { max: 20, message: '端口名称最大长度为20个字符', trigger: 'blur' },
  {
    pattern: /^[\u4e00-\u9fa5a-zA-Z0-9_]+$/,
    message: '端口名称只能包含汉字、数字、字母、下划线',
    trigger: 'blur'
  }
];

// 端口号校验规则
const portNumberRules: Rule[] = [
  { required: true, message: '请输入端口号', trigger: 'blur' },
  { type: 'number', min: 0, message: '端口号不能为负数', trigger: 'blur' }
];

// 校验规则
const rules: Record<string, Rule[]> = {
  name: [
    { required: true, message: '请输入资源名称', trigger: 'blur' },
    { max: 20, message: '资源名称最大长度为20个字符', trigger: 'blur' },
    {
      pattern: /^[\u4e00-\u9fa5a-zA-Z0-9_]+$/,
      message: '资源名称只能包含汉字、数字、字母、下划线',
      trigger: 'blur'
    }
  ],
  cpuSize: [
    { required: true, message: '请输入CPU核数', trigger: 'blur' },
    { type: 'number', min: 0, message: 'CPU核数不能为负数', trigger: 'blur' }
  ],
  memorySize: [
    { required: true, message: '请输入内存大小', trigger: 'blur' },
    { type: 'number', min: 0, message: '内存大小不能为负数', trigger: 'blur' }
  ],
  diskSize: [
    { required: true, message: '请输入磁盘大小', trigger: 'blur' },
    { type: 'number', min: 0, message: '磁盘大小不能为负数', trigger: 'blur' }
  ],
  port: [
    // 这里不用做校验了，内部每一项有校验
    {
      type: 'array',
      required: true,
      trigger: 'change'
    }
  ],
  validDateRange: [
    { required: true, message: '请选择使用时间', trigger: 'change' }
  ]
};

watch(
  () => props.modelValue,
  (val) => {
    localResource.value = {
      ...val,
      // 确保至少有一个端口
      port: val.port && val.port.length > 0
        ? [...val.port]
        : [{ name: '', port: undefined }]
    };
  },
);

async function handleApply() {
  try {
    await formRef.value?.validate();
    emits('update:modelValue', { ...localResource.value });
    emits('update:visible', false);
    formItemContext?.onFieldChange();
  } catch (error) {
    console.log('表单校验失败:', error);
  }
}
</script>

<template>
  <SFullModal :width="800" :visible="visible" :title="mode === 'view' ? '资源详情' : '资源申请'" :footer="mode === 'edit'"
    @ok="handleApply" @cancel="emits('update:visible', false)">
    <AForm ref="formRef" :model="localResource" :rules="rules" class="pt-6" :disabled="mode === 'view'"
      :label-col="{ span: 5 }" :wrapper-col="{ span: 19 }">
      <AFormItem label="资源名称" name="name">
        <AInput :maxlength="20" v-model:value="localResource.name" placeholder="请输入" />
      </AFormItem>
      <AFormItem label="CPU(核)" name="cpuSize">
        <AInputNumber v-model:value="localResource.cpuSize" placeholder="请输入" class="w-full" :min="0" :precision="0" />
      </AFormItem>
      <AFormItem label="内存(G)" name="memorySize">
        <AInputNumber v-model:value="localResource.memorySize" placeholder="请输入" class="w-full" :min="0"
          :precision="0" />
      </AFormItem>
      <AFormItem label="磁盘(G)" name="diskSize">
        <AInputNumber v-model:value="localResource.diskSize" placeholder="请输入" class="w-full" :min="0" :precision="0" />
      </AFormItem>
      <AFormItem label="端口" name="port">
        <div v-for="(item, idx) in localResource.port" :key="idx"
          style="display: flex; gap: 8px; align-items: center; margin-bottom: 10px">
          <a-flex class="flex-1" align="center" justify="space-between" :gap="5">
            <AFormItem :name="['port', idx, 'name']" class="flex-1 mb-0" :rules="portNameRules">
              <AInput :maxlength="20" v-model:value="item.name" placeholder="端口名称" class="w-full" />
            </AFormItem>
            <span class="shrink-0 w-[18px] h-[1px] bg-[var(--zion-border-color-2)]"></span>
            <AFormItem :name="['port', idx, 'port']" class="flex-1 mb-0" :rules="portNumberRules">
              <AInputNumber class="w-full" v-model:value="item.port" placeholder="端口号" :min="0" :precision="0" />
            </AFormItem>
          </a-flex>
          <span v-if="localResource.port.length > 1" class="w-[22px] cursor-pointer shrink-0" @click="removePort(idx)">
            <MinusCircleOutlined class="text-[20px] text-[var(--ant-colorError)]" />
          </span>
        </div>
        <AButton type="dashed" class="mt-2 w-full" :icon="h(PlusOutlined)" @click="addPort">
          添加端口
        </AButton>
      </AFormItem>
      <AFormItem label="使用时间" name="validDateRange">
        <ARangePicker v-model:value="localResource.validDateRange" value-format="YYYY-MM-DD HH:mm:ss"
          :show-time="{ format: 'HH:mm:ss', defaultValue: [dayjs('00:00:00', 'HH:mm:ss'), dayjs('23:59:59', 'HH:mm:ss')] }"
          class="w-full" format="YYYY-MM-DD HH:mm:ss"
          :disabled-date="(current) => current && current < dayjs().startOf('day')" />

      </AFormItem>
    </AForm>
  </SFullModal>
</template>
