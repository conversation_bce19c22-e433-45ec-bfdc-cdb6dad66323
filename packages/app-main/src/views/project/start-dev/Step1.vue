<script setup lang="ts">
import { useRoute, useRouter } from 'vue-router';

import { useStartDevFormStore, type Step1Form } from '@/stores/startDevForm';
import { setDevConfig } from '@/apis/project/project';

const router = useRouter();
const route = useRoute();
const startDevFormStore = useStartDevFormStore();
const loading = ref(false);
const formData = ref<Step1Form>(startDevFormStore.step1Form);
const rules = {
  version: [{ required: true, message: '请选择技术底座版本' }],
};
const nextStep = async (values: Step1Form) => {
  // 这里直接调用接口
  loading.value = true;
  const res = await setDevConfig({
    programmeId: route.params.id as string,
    // 去掉-base或-xc后缀,这里是假的数据传入后端是一个值
    zionVersion: values.version?.replace(/-base|-xc/, ''),
  });
  loading.value = false;
  if (res.data && res.data.success) {
    // 缓存步骤1表单数据
    startDevFormStore.setStep1Form(values);
    router.push(`/project/${route.params.id}/start-dev/step2`);
  }
};
const skipStep = () => {
  router.push(`/project/${route.params.id}/start-dev/step2`);
};
</script>

<template>
  <AForm class="mx-auto mt-20 w-[30%]" :model="formData" :rules="rules" @finish="nextStep">
    <AFormItem label="技术底座版本" name="version">
      <ASelect v-model:value="formData.version" placeholder="请选择" :options="[
        {
          label: '2.0.0 标准版',
          value: '2.0.2-base',
          description: '已适配sprintboot 2.x',
        },
        {
          label: '2.0.0 信创版',
          value: '2.0.2-xc',
          description: '已适配国产化数据库PolarDB',
        },
        {
          label: '3.0.0 标准版',
          value: '3.0.0-base',
          description: '已适配sprintboot 3.x',
        },
        {
          label: '3.0.0 信创版',
          value: '3.0.2-xc',
          description: '已适配国产化数据库PolarDB',
        }
      ]">
        <template #option="{ label, description }">
          <AFlex vertical>
            <span>{{ label }}</span>
            <span class="text-12px text-[#999]">{{ description }}</span>
          </AFlex>
        </template>
      </ASelect>
    </AFormItem>
    <AFlex justify="center" :gap="15" class="mt-[100px]">
      <AButton type="primary" html-type="submit" :loading="loading">
        下一步
      </AButton>
      <!-- <AButton
        @click="skipStep"
        type="primary"
        ghost
      >
        跳过
      </AButton> -->
    </AFlex>
  </AForm>
</template>

<style scoped lang="less"></style>
