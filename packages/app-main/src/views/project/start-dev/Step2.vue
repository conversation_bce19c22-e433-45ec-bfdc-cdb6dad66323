<script setup lang="ts">
  import { useRoute, useRouter } from 'vue-router';

  import { useStartDevFormStore } from '@/stores/startDevForm';
  import ComsSelect from './components/ComsSelect.vue';
  import { setDevConfig } from '@/apis/project/project';

  const router = useRouter();
  const route = useRoute();
  const startDevFormStore = useStartDevFormStore();
  const loading = ref(false);

  const formData = startDevFormStore.step2Form;

  const rules = {
    componentIds: [{ required: true, message: '请选择通用组件' }],
  };

  const nextStep = async (values: any) => {
    // 这里直接调用接口
    loading.value = true;
    const res = await setDevConfig({
      programmeId: route.params.id as string,
      componentIds: values.componentIds,
    });
    loading.value = false;
    if (res.data && res.data.success) {
      // 缓存步骤2表单数据
      startDevFormStore.setStep2Form(values);
      router.push(`/project/${route.params.id}/start-dev/step3`);
    }
  };
  const skipStep = () => {
    router.push(`/project/${route.params.id}/start-dev/step3`);
  };
  const backStep = () => {
    router.push(`/project/${route.params.id}/start-dev/step1`);
  };
</script>

<template>
  <AForm
    class="mx-auto mt-20 w-[40%]"
    :model="formData"
    :rules="rules"
    @finish="nextStep"
  >
    <!-- <div class="warning-alert mb-3">
      已根据项目合同/设计文档内容为您自动预选了【消息中心】、【表单管理】2个组件。
    </div> -->
    <AFormItem
      label="通用组件"
      name="componentIds"
    >
      <ComsSelect
        v-model:value="formData.componentIds"
        mode="multiple"
        :programme-id="route.params.id as string"
      />
    </AFormItem>
    <AFlex
      justify="center"
      :gap="10"
      class="mt-[100px]"
    >
      <AButton
        @click="backStep"
        type="primary"
      >
        上一步
      </AButton>
      <AButton
        type="primary"
        html-type="submit"
        :loading="loading"
      >
        下一步
      </AButton>
      <AButton
        @click="skipStep"
        type="primary"
        ghost
      >
        跳过
      </AButton>
    </AFlex>
  </AForm>
</template>

<style scoped lang="less"></style>
