<script setup lang="ts">
  import { AppstoreOutlined, SearchOutlined, UnorderedListOutlined } from '@ant-design/icons-vue';
  import type { CheckboxChangeEvent } from 'ant-design-vue/es/checkbox/interface';
  import { debounce } from 'lodash-es';
  import { computed, onMounted, onUnmounted, ref } from 'vue';

  import ProjectListView from './components/ProjectListView.vue';

  const search = ref({
    programmeName: '',
    type: undefined,
    srdcloudProjectState: 'actived', // 研发云项目状态
  });
  const pageParams = ref({
    pageSize: 9,
  });
  const srdcloudStateOptions = ref([ // closed: 已关闭, actived:研发中, other: 其他
    { label: '活动', value: 'actived', color: 'green' },
    { label: '已关闭', value: 'closed', color: 'grey' },
    { label: '其他', value: 'other', color: 'cyan' },
  ]);
  const projectTypeOptions = ref([
    { label: '项目', value: 1 },
    { label: '产品', value: 2 },
  ]);
  const STORE_MODE_KEY = 'fullProjectDisplayMode';
  const mode = ref<'card' | 'table'>(
    (localStorage.getItem(STORE_MODE_KEY) as 'card' | 'table') || 'card',
  );
  const listRef = ref();
  const screenWidth = ref(window.innerWidth);
  const handleModeChange = (e: CheckboxChangeEvent) => {
    mode.value = e.target.checked ? 'card' : 'table';
  };
  // 响应式计算 cardConfig
  const cardConfig = computed(() => {
    if (screenWidth.value < 768) {
      return { row: 1 };
    } else if (screenWidth.value < 1700) {
      return { row: 2 };
    } else if (screenWidth.value < 2500) {
      return { row: 3 };
    } else {
      return { row: 4 };
    }
  });

  // 监听窗口大小变化
  function handleResize() {
    screenWidth.value = window.innerWidth;
  }
  const handleInputSearch = debounce(function () {
    if (listRef.value) {
      listRef.value.getList();
    }
  }, 500);
  function handleSearch() {
    if (listRef.value) {
      listRef.value.getList();
    }
  }

  onMounted(() => {
    if (listRef.value) {
      listRef.value.getList();
    }
    // 添加窗口大小变化监听
    window.addEventListener('resize', handleResize);
  });

  // 组件卸载时移除监听器
  onUnmounted(() => {
    window.removeEventListener('resize', handleResize);
  });
</script>

<template>
  <div class="project-page mb-5 bg-[#F7F8FA]">
    <ACard class="mx-auto rounded-6px" :bordered="false">
      <AFlex align="center" justify="space-between">
        <ASpace>
          <h2 class="mb-0 text-20px font-[600]">我的项目</h2>

          <ACheckbox :checked="mode === 'card'" class="modeBtn ml-10px!" @change="handleModeChange">
            <BarsOutlined v-if="mode === 'card'" class="color-[var(--ant-colorPrimary)]" />
            <AppstoreOutlined v-else class="color-[var(--ant-colorPrimary)]" />
            {{ mode === 'card' ? '列表视图' : '卡片视图' }}
          </ACheckbox>
        </ASpace>

        <ASpace>
          <div class="warning-alert">项目数据为维Work立项的软件研发类的售中项目</div>
          <!-- <a-space>
            访问时间
            <ASelect
              v-model:value="search.timeRange"
              class="w-[200px]"
              placeholder="请选择"
              :options="[
                { label: '最近一周', value: '1week' },
                { label: '最近一个月', value: '1month' },
                { label: '最近三个月', value: '3month' },
              ]"
              @change="handleSearch"
            />
          </a-space> -->
          <ASpace>
            研发云项目状态
            <ASelect v-model:value="search.srdcloudProjectState" allow-clear class="w-[200px]" placeholder="请选择" :options="srdcloudStateOptions" @change="handleSearch" />
          </ASpace>
          <ASpace>
            类型
            <ASelect v-model:value="search.type" allow-clear class="w-[200px]" placeholder="请选择" :options="projectTypeOptions" @change="handleSearch" />
          </ASpace>

          <AInput v-model:value="search.programmeName" class="w-[200px]" placeholder="请输入名称搜索" allow-clear @press-enter="handleSearch" @change="handleInputSearch">
            <template #suffix>
              <SearchOutlined />
            </template>
          </AInput>
        </ASpace>
      </AFlex>
      <div class="mt-20px">
        <ProjectListView ref="listRef" :search="search" :page-params="pageParams" :mode="mode" :card-config="cardConfig" :store-mode-key="STORE_MODE_KEY" />
      </div>
    </ACard>
  </div>
</template>

<style scoped lang="less">
  .project-page {
    :deep(.ant-empty) {
      margin-top: 80px;
    }
  }
</style>
