<script setup lang="ts">
  import { FolderOpenFilled, LoadingOutlined, SearchOutlined } from '@ant-design/icons-vue';
  import type { SelectProps } from 'ant-design-vue';
  import to from 'await-to-js';
  import { computed, onMounted, ref } from 'vue';

  import { getAuthProjectList, getProjectList } from '@/apis/project/project';

  const props = withDefaults(
    defineProps<{
      actionType?: string;
      loading?: boolean;
      bordered?: boolean;
      maxSelected?: number;
      value?: any;
      mode?: 'dropDown' | 'multiple' | 'tags' | undefined;
      placement?: any;
      collapsed?: boolean;
      trigger?: 'click' | 'hover';
      query?: any;
    }>(),
    {
      actionType: '', // 设置默认值
      loading: false, // 设置默认值为 false
      collapsed: true, // 设置默认值为 true
      bordered: true, // 设置默认值为 true
      maxSelected: 0, // 设置默认值为 0
      value: undefined, // 设置默认值为 undefined
      mode: undefined, // 设置默认值为 undefined
      placement: 'right', // 设置默认值为 undefined
      trigger: 'click', // 设置默认值为 'click'
      query: null, // 查询参数
    },
  );
  const emit = defineEmits<{
    (event: 'change', data: any): void;
    (event: 'selectItem', data: any): void;
    (event: 'update:value', data: any): void;
  }>();

  const isDropDown = computed(() => props.mode === 'dropDown');
  const isEdit = computed(() => props.actionType !== 'view');
  const isView = computed(() => props.actionType === 'view');

  const open = ref(false);

  const list = ref<SelectProps['options']>([]);
  const loadding = ref(props.loading);
  const selectValue = ref('');
  const fieldNames = {
    value: 'id',
    label: 'programmeName',
  };
  const filterOption = (input: string, option: any) => {
    return option[fieldNames.label].toLowerCase().indexOf(input.toLowerCase()) >= 0;
  };
  const selectedProject = computed(() => list.value?.find((item) => item.id === props.value) || {});

  const selectChange = (value: any): void => {
    if (props.maxSelected) {
      const sliceIndex = value.length - props.maxSelected || 0;
      selectValue.value = value.slice(sliceIndex);
    } else {
      selectValue.value = value;
    }
    emit('update:value', selectValue.value);
    emit('change', selectValue.value);
  };

  const selectItemChange = (item: any) => {
    open.value = false;
    emit('update:value', item.id);
    emit('change', item.id);
    emit('selectItem', { ...item, appId: item.id });
    console.log('selectItemChange', props.value, item, selectedProject.value);
  };

  const getList = async () => {
    const params = {
      ...props.query,
    };
    loadding.value = true;
    // const [err, res] = await to(getProjectList(params));
    const [err, res] = await to(getAuthProjectList(params));
    loadding.value = false;
    if (err) return;
    list.value = (res?.data?.data as any) || [];
  };

  onMounted(() => {
    getList();
  });
</script>

<template>
  <APopover
    v-if="isDropDown"
    v-model:open="open"
    :trigger="trigger"
    :placement="placement"
    title=""
    :overlay-inner-style="{
      padding: 0,
    }"
  >
    <template #content>
      <div class="project-menu-wrap">
        <div
          v-if="loadding || props.loading"
          class="loading-wrap min-w-120px"
        >
          <LoadingOutlined spin />
          加载中...
        </div>
        <AMenu
          v-if="!loadding && !props.loading && list?.length"
          class="project-menu"
          size="small"
          selectable
          :inline-indent="0"
          :selected-keys="[props.value]"
        >
          <AMenuItem
            v-for="item in list"
            :key="item.id"
            @click="selectItemChange(item)"
          >
            <div class="menu-item-content">
              <div class="info">
                <div
                  class="name"
                  :title="item.programmeName"
                >
                  {{ item.programmeName }}
                </div>
              </div>
            </div>
          </AMenuItem>
        </AMenu>
        <div
          v-if="!loadding && !props.loading && !list?.length"
          class="loading-wrap min-w-120px"
        >
          <FolderOpenFilled />
          暂无项目...
        </div>
      </div>
    </template>
    <div
      class="project-select-trigger"
      :class="{ bordered }"
      @click="open = !open"
    >
      <div class="iconBox">
        <LoadingOutlined v-if="loadding" />
        <FolderOpenFilled
          v-else
          class="icon"
        />
      </div>
      <div
        v-if="!collapsed"
        class="info"
        :class="{ 'opacity-40': loadding || props.loading }"
      >
        <div
          class="name"
          :title="selectedProject?.programmeName || ''"
        >
          {{ selectedProject?.programmeName || '请选择项目' }}
          <DownOutlined class="arrow" />
        </div>
        <div class="desc">
          {{
            loadding || props.loading
              ? '加载中...'
              : selectedProject.introduction || '暂无项目简介...'
          }}
        </div>
      </div>
    </div>
  </APopover>
  <ASelect
    v-else
    :value="value"
    class="customSelect w-full"
    :loading="loadding"
    :mode="mode === 'dropDown' ? undefined : mode"
    :bordered="bordered"
    placeholder="请选择"
    allow-clear
    :show-search="isEdit"
    :options="list"
    :filter-option="filterOption"
    :field-names="fieldNames"
    :disabled="loadding || isView"
    @change="selectChange"
  >
    <template
      v-if="isEdit"
      #suffixIcon
    >
      <LoadingOutlined v-if="loadding" />
      <SearchOutlined v-else />
    </template>
  </ASelect>
</template>

<style lang="less" scoped>
  .customSelect {
    &.ant-select {
      &.ant-select-loading {
        // :deep(.ant-select-selector) {
        //   display: none;
        // }

        :deep(.ant-select-arrow) {
          left: calc(50% - 11px);
          width: 22px;
        }
      }

      &.ant-select-disabled {
        :deep(.ant-select-selector) {
          cursor: default;

          .ant-select-selection-item {
            color: rgba(0, 0, 0, 0.9);
          }
        }

        :deep(.ant-select-arrow) {
          display: none;

          &.ant-select-arrow-loading {
            display: inline-block;
          }
        }
      }
    }
  }

  .project-select-trigger {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 12px;
    border-radius: 8px;
    cursor: pointer;
    border: none;
    transition: border 0.2s;

    &:hover {
      background: #f7f9fa;
    }

    &.bordered {
      border: 1px solid #d9d9d9;
    }

    .iconBox {
      width: 40px;
      height: 40px;
      border-radius: 8px;
      background: #e0edfe;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-shrink: 0; // 防止被压缩
      margin-right: 12px; // 与右侧信息区间距

      .icon {
        font-size: 20px;
        color: #4e83fd;
      }
    }

    .info {
      flex: 1;
      width: calc(100% - 52px);

      .name {
        font-size: 16px;
        color: #222;
        font-weight: 500;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        position: relative;
        padding-right: 20px;

        .arrow {
          font-size: 14px;
          margin-left: 6px;
          color: #bfbfbf;
          position: absolute;
          right: 0;
          top: 3px;
        }
      }

      .desc {
        font-size: 13px;
        color: #8a919f;
        margin-top: 2px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        padding-right: 7px;
      }
    }
  }

  .project-menu-wrap {
    max-height: 320px;
    overflow-y: auto;
    padding: 3px 9px;

    .loading-wrap {
      padding: 32px 0;
      text-align: center;
      color: #888;
    }

    .project-menu {
      background: #fff;
      border-radius: 8px;
      min-width: 120px;
      max-width: 320px;
      border: none;

      :deep(.ant-menu-item) {
        padding: 0 12px;

        .menu-item-content {
          display: flex;
          align-items: center;

          .icon {
            font-size: 28px;
            color: #4e83fd;
            margin-right: 10px;
          }

          .info {
            width: 100%;

            .name {
              font-size: 15px;
              color: #222;
              font-weight: 500;
              width: 100%;
              white-space: nowrap;
              text-overflow: ellipsis;
              overflow: hidden;
            }

            .desc {
              font-size: 12px;
              color: #8a919f;
              margin-top: 2px;
              white-space: nowrap;
              text-overflow: ellipsis;
              overflow: hidden;
              max-width: 180px;
            }
          }
        }
      }
    }
  }
</style>
