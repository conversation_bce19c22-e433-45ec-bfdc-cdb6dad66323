// 已废弃，后端会直接返回，前端不用判断
<template>
  <s-tag
    v-if="stage"
    :color="statusOption.tag?.color"
    :bgColor="statusOption.tag?.bgColor"
  >
    {{ statusOption.label }}
  </s-tag>
  <a-tag v-else>
    {{ statusOption.label }}
  </a-tag>
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  import { projectStageOptions, type ProjectStage } from '@/dicts/project';

  interface Props {
    stage?: ProjectStage | null;
  }

  const props = withDefaults(defineProps<Props>(), {
    stage: null,
  });

  const statusOption = computed<any>(() => {
    if (!props.stage) {
      return {
        label: '未知状态',
      };
    }
    return (
      projectStageOptions.find((item) => item.value === props.stage) || {
        label: '未知状态',
      }
    );
  });
</script>
