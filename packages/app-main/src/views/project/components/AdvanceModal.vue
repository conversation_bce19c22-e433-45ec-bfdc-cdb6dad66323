<script setup lang="ts">
  import { message, Modal } from 'ant-design-vue';
import { computed, h, ref, watch } from 'vue';
import { useRouter } from 'vue-router';

  import { advanceProject } from '@/apis/project/project';
import StepRightArrow from '@/assets/images/step_bread_arrow.png';
import type { StepItem } from '@/components/ProjectSteps/index.vue';

  const router = useRouter();

  interface Developer {
    id: string;
    nickName: string;
    roleName: string | null;
    username: string;
  }

  interface Props {
    visible: boolean;
    currentStageConfig: StepItem;
    nextStageConfig: StepItem;
    programmeId: string;
    developers: Developer[];
    sprintId?: string;
  }

  interface Emits {
    (e: 'update:visible', value: boolean): void;
    (e: 'success'): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    developers: () => [],
  });

  const emit = defineEmits<Emits>();

  const formRef = ref();
  const loading = ref(false);

  const formData = ref({
    approvers: [] as string[],
  });

  // 审批人选项
  const approverOptions = computed(() => props.developers);

  // 过滤选项
  const filterOption = (input: string, option: any) => {
    return option.nickName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
  };

  // 提交
  const handleSubmit = async () => {
    // 表单校验
    await formRef.value.validate();

    loading.value = true;

    try {
      // 这里stage去的是id
      const params = {
        approvers: formData.value.approvers,
        currentStage: props.currentStageConfig.value,
        nextStage: props.nextStageConfig.value,
        programmeId: props.programmeId,
        sprintId: props.sprintId,
      };

      const res = await advanceProject(params);

      if (res.data && res.data.success) {
        // 显示成功提示框
        Modal.confirm({
          title: '阶段推进申请提交成功',
          content: h('div', [
            h('span', '你可以前往 '),
            h(
              'a',
              {
                onClick: () => {
                  Modal.destroyAll();
                  emit('success');
                  handleCancel();
                  const { href } = router.resolve('/approval');
                  window.open(href, '_blank');
                },
              },
              '审批列表',
            ),
            h('span', ' 查看'),
          ]),
          onOk: () => {
            emit('success');
            handleCancel();
          },
        });
      } else {
        message.error(res.data?.message || '阶段推进失败');
      }
    } catch (error) {
      console.error('阶段推进失败:', error);
    } finally {
      loading.value = false;
    }
  };

  // 取消
  const handleCancel = () => {
    formData.value.approvers = [];
    emit('update:visible', false);
  };

  // 监听 visible 变化，重置表单
  watch(
    () => props.visible,
    (newVal) => {
      if (newVal) {
        formData.value.approvers = [];
      }
    },
  );
</script>

<template>
  <SFullModal
    :visible="visible"
    title="阶段推进"
    :width="600"
    :confirm-loading="loading"
    ok-text="提交"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <AForm
      ref="formRef"
      :model="formData"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 16 }"
    >
      <AFormItem label="阶段">
        {{ currentStageConfig.title }}
        <span>
          <img
            :src="StepRightArrow"
            class="mx-[13px] h-auto w-[7px]"
          />
        </span>
        {{ nextStageConfig.title }}
      </AFormItem>

      <AFormItem
        label="审批人"
        name="approvers"
        :rules="[{ required: true, message: '请选择审批人' }]"
      >
        <ASelect
          v-model:value="formData.approvers"
          mode="multiple"
          placeholder="请选择审批人"
          :options="approverOptions"
          :field-names="{ label: 'nickName', value: 'id' }"
          show-search
          :filter-option="filterOption"
        />
      </AFormItem>
    </AForm>
  </SFullModal>
</template>

<style scoped lang="less">
  // 样式可以根据需要添加
</style>
