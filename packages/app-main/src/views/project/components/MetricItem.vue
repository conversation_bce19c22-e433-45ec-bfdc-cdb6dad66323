<template>
  <span @click.stop="handleClick">
    <span v-if="finished === '--'">--</span>
    <template v-else>
      <template v-if="!onlyShowTotal">
        <span :class="['link-text', { 'cursor-pointer': hasLink }]" :style="{ color: showFinishedLinkColor }">
          {{ finished }}
        </span>
        /
      </template>
      <span :class="['link-text', { 'cursor-pointer': hasLink }]" :style="{ color: showTotalLinkColor }">
        {{ total }}
      </span>
    </template>
  </span>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { message } from 'ant-design-vue';

interface Props {
  finished: string | number;
  total: string | number;
  link?: string;
  onlyShowTotal?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  link: '',
  onlyShowTotal: false,
});

const emit = defineEmits<{
  (e: 'click', link: string): void;
}>();

const hasLink = computed(() => {
  return props.link && props.link.trim() !== '';
});
const showFinishedLinkColor = computed(() => {
  return props.finished ? 'var(--ant-colorPrimary)' : 'inherit';
});
const showTotalLinkColor = computed(() => {
  return 'inherit';
});

const handleClick = () => {
  if (hasLink.value) {
    emit('click', props.link);
  } else {
    message.warning('当前指标无值');
  }
};
</script>

<style scoped>
.link-text {
  text-decoration: underline;
}
</style>
