<script setup lang="tsx">
  import type { FormInstance, TableColumnsType, TableProps } from 'ant-design-vue';
  import { message } from 'ant-design-vue';
  import type { Key } from 'ant-design-vue/es/table/interface';
  import { computed, nextTick, reactive, ref, unref } from 'vue';
  import type { ComponentExposed } from 'vue-component-type-helpers';

  import systemTenantApi, { type GetUserAppUserResponseItem } from '@/apis/platform/systemTenant';
  import ServerPagination from '@/components/ServerPagination/index.vue';
  import TableList from '@/components/TableList/index.vue';

  const props = withDefaults(
    defineProps<{
      checkType?: 'checkbox' | 'radio';
      modelValue?: string | string[];
      query?: Record<string, any>;
    }>(),
    {
      checkType: 'checkbox',
      modelValue: () => [],
      query: () => ({}),
    },
  );
  const submitLoading = ref(false);
  const formRef = ref<FormInstance>();
  const formState = reactive({
    userName: '',
    mobile: '',
    realName: '',
  });
  const isModalOpen = ref(false);
  const modalKey = ref(0);
  const openModal = async () => {
    modalKey.value += 1;
    await nextTick();
    isModalOpen.value = true;
  };

  const serverPaginationRef = ref<ComponentExposed<typeof ServerPagination>>();
  const handleQuery = (reset?: boolean) => {
    if (reset) {
      formRef.value?.resetFields();
    }

    serverPaginationRef.value?.fetchDataButResetPage();
  };
  const listLoading = ref(false);
  const list = ref<GetUserAppUserResponseItem[]>();
  const columns: TableColumnsType<GetUserAppUserResponseItem> = [
    {
      title: '登录名称',
      dataIndex: 'userName',
      key: 'userName',
    },
    {
      title: '真实姓名',
      dataIndex: 'realName',
      key: 'realName',
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
    },
    {
      title: '手机',
      dataIndex: 'mobile',
      key: 'mobile',
    },
    {
      title: '用户状态',
      dataIndex: 'status',
      key: 'status',
      customRender: ({ record }) => {
        return record.status ? <a-tag color="blue">启用</a-tag> : <a-tag color="red">禁用</a-tag>;
      },
    },
    // {
    //   title: '创建时间',
    //   dataIndex: 'description',
    //   key: 'description',
    // },
  ];
  const selectedRowKeys = ref<any>([]);
  const selectedRow = ref<object[]>([]);
  if (props.checkType === 'radio') {
    selectedRowKeys.value = [props.modelValue];
  } else {
    selectedRowKeys.value = props.modelValue;
  }
  const rowSelection = computed<TableProps['rowSelection']>(() => {
    return {
      selectedRowKeys: unref(selectedRowKeys),
      type: props.checkType,
      onChange: onSelectChange,
    };
  });
  const onSelectChange = (changableRowKeys: Key[], selectedRows: object[]) => {
    selectedRowKeys.value = changableRowKeys;
    selectedRow.value = selectedRows;
  };
  const emits = defineEmits(['update:modelValue', 'ok']);
  const handleModalOk = async () => {
    if (!selectedRowKeys.value.length) {
      message.error('请选择需要添加的用户');
      return;
    }
    console.log(selectedRowKeys);
    if (props.checkType === 'radio') {
      emits('update:modelValue', selectedRowKeys.value[0]);
      emits('ok', selectedRow.value[0]);
    } else {
      emits('update:modelValue', selectedRowKeys.value);
      emits('ok', selectedRow.value);
    }
    nextTick(() => {
      selectedRowKeys.value = [];
      selectedRow.value = [];
      isModalOpen.value = false;
    });
  };
</script>

<template>
  <span>
    <AButton
      type="dashed"
      size="small"
      @click="openModal"
    >
      选择用户
    </AButton>
  </span>
  <AModal
    :key="modalKey"
    v-model:open="isModalOpen"
    :mask-closable="false"
    :keyboard="false"
    width="80%"
    destroy-on-close
    :confirm-loading="submitLoading"
    title="选择用户"
    @ok="handleModalOk"
  >
    <div>
      <TableList
        :filter-form-state="formState"
        :loading="listLoading"
        :border="true"
        @query="handleQuery()"
      >
        <template #filterForm>
          <AFormItem
            label="登录名称"
            name="userName"
          >
            <AInput
              v-model:value.trim="formState.userName"
              placeholder="请输入"
              :disabled="listLoading"
            />
          </AFormItem>
          <AFormItem
            label="真实姓名"
            name="realName"
          >
            <AInput
              v-model:value.trim="formState.realName"
              allow-clear
              placeholder="请输入真实姓名"
            ></AInput>
          </AFormItem>
          <AFormItem
            label="手机号码"
            name="mobile"
          >
            <AInput
              v-model:value.trim="formState.mobile"
              placeholder="请输入"
              :disabled="listLoading"
            />
          </AFormItem>
        </template>
        <template #table>
          <ATable
            row-key="userId"
            :data-source="list"
            :columns="columns"
            :pagination="false"
            :row-selection="rowSelection"
          />
        </template>
        <template #pagination>
          <ServerPagination
            ref="serverPaginationRef"
            :request="
              ({ pageIndex, pageSize }) =>
                systemTenantApi.getUserPage({
                  pageIndex,
                  pageSize,
                  ...props.query,
                  ...formState,
                  status: 1,
                  remained: false,
                })
            "
            @loading-change="(val) => (listLoading = val)"
            @list-change="(val) => (list = val)"
          />
        </template>
      </TableList>
    </div>
  </AModal>
</template>
