<template>
  <s-full-modal :visible="visible" title="过程裁剪" width="1300px" @ok="handleOk" @cancel="handleCancel"
    :confirm-loading="confirmLoading" ok-text="提交">
    <a-form :model="formValues" :rules="rules" class="clip-config-content" ref="formRef">
      <a-form-item name="checkItemIds">
      <!-- 这里需要初始化选中 -->
        <ClipTreeSelect need-init-checked-for-all v-model:value="formValues.checkItemIds" :programmeId="programmeId" default-select-all
          mode="edit" :itemWidth="180" />
      </a-form-item>
      <a-form-item label="裁剪原因" name="reason">
        <a-textarea v-model:value="formValues.reason" :rows="3" placeholder="请输入" :maxlength="200" show-count />
      </a-form-item>
    </a-form>
  </s-full-modal>
</template>

<script setup lang="ts">
import { ref, toRaw, h, watch } from 'vue';
import { useRouter } from 'vue-router';
import { Modal, message } from 'ant-design-vue';
import type { FormInstance } from 'ant-design-vue';
import ClipTreeSelect from '@/components/ClipTreeSelect/index.vue';
import { submitCropping } from '@/apis/checkpoint/agileStage';

const router = useRouter();
const formRef = ref<FormInstance>();

interface Props {
  visible: boolean;
  programmeId: string;
}
type FormValues = {
  checkItemIds: string[];
  reason: string;
};
const confirmLoading = ref(false);
// 测试数据
const formValues = ref<FormValues>({
  checkItemIds: [],
  reason: '',
});
interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'ok', formValues: any): void;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  programmeId: '',
});
const emit = defineEmits<Emits>();

const rules = {
  reason: [{ required: true, message: '请输入裁剪原因' }],
};

/**
 * 处理确认按钮点击事件
 * @description 触发确认事件并关闭弹窗
 */
const handleOk = async () => {
  try {
    // 表单校验
    await formRef.value?.validate();
    
    console.log(formValues.value);
    confirmLoading.value = true;
    // 调用提交裁剪接口
    const res = await submitCropping({
      programmeId: props.programmeId,
      checkItemIds: formValues.value.checkItemIds,
      reason: formValues.value.reason,
    });

    if (res.data && res.data.success) {
      // 显示成功提示框
      Modal.info({
        title: '提交裁剪申请成功',
        content: h('div', [
          h('span', '你可以前往 '),
          h(
            'a',
            {
              onClick: () => {
                Modal.destroyAll();
                const { href } = router.resolve('/approval');
                window.open(href, '_blank');
              },
            },
            '审批列表',
          ),
          h('span', ' 查看'),
        ]),
        onOk: () => {
          // 这里不用刷新详情页
          emit('update:visible', false);
        },
      });
    } else {
      message.error(res.data?.errMessage || '提交失败');
    }

    emit('ok', toRaw(formValues.value));
  } catch (error) {
    console.error('提交裁剪申请失败:', error);
  } finally {
    confirmLoading.value = false;
  }
};

const handleCancel = () => {
  emit('update:visible', false);
};

watch(
  () => props.visible,
  (newVal) => {
    // 打开重置表单
    if (newVal) {
      formValues.value.reason = '';
      // 重置表单校验状态
      formRef.value?.resetFields();
    }
  },
);
</script>

<style lang="less" scoped>
.clip-config-content {
  padding-left: 24px;
  padding-right: 24px;


}
</style>
