<script setup lang="ts">
  import { LoginOutlined, PlayCircleOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';

  import type { Project } from '@/apis/project/project';
import {
  getConformityColor,
  getLevelColor,
  getProgressLinearColor,
  ProjectStage,
} from '@/dicts/project';
import { renderTemplate } from '@/utils';

  import MetricItem from './MetricItem.vue';

  const props = withDefaults(
    defineProps<{
      item: Project;
      canIedit: boolean;
    }>(),
    {
      canIedit: true,
    },
  );

  /**
   * btn-click
   * dropdown按钮点击事件
   * @param item 项目信息
   */
  /**
   * percent-click
   * 数字点击事件
   * @param item 项目信息
   * @param type 点击类型
   */
  /**
   * item-detail
   * 项目详情点击事件
   * @param item 项目信息
   */
  const isDev = import.meta.env.DEV;
  const emits = defineEmits<{
    (e: 'btn-click', item: Project): void;
    (e: 'percent-click', item: Project, type: string): void;
    (e: 'item-detail', item: Project): void;
  }>();
  const handleDropdownClick = (key: string | number) => {
    if (key === 'detail') {
      emits('item-detail', props.item);
    } else if (key === 'start-dev' || key === 'enter-workspace') {
      emits('btn-click', { ...props.item, btnType: key });
    } else {
      throw new Error('未知研发状态');
    }
  };

  const handleMetricClick = (link: string) => {
    if (link && link.trim() !== '') {
      if (!props.item.srdcloudProjectId) {
        message.warning('此项目暂未关联研发云');
        return;
      }
      window.open(renderTemplate(link, { projectId: props.item.srdcloudProjectId }), '_blank');
    }
  };

  const srdcloudStateOptions = ref([
    // closed: 已关闭, actived:研发中, other: 其他
    { label: '活动', value: 'actived', color: 'green' },
    { label: '已关闭', value: 'closed', color: 'grey' },
    { label: '其他', value: 'other', color: 'cyan' },
  ]);
  const getSrdcloudStateColor = (value: string) => {
    const option = srdcloudStateOptions.value.find((item) => item.value === value);
    return option ? option.color : 'default';
  };
  const getSrdcloudStateLabel = (value: string) => {
    const option = srdcloudStateOptions.value.find((item) => item.value === value);
    return option ? option.label : '未知状态';
  };
</script>

<template>
  <!-- <ABadgeRibbon
    :text="getSrdcloudStateLabel(item.srdcloudProjectState)"
    :color="getSrdcloudStaColor(item.srdcloudProjectState)"
  > -->
  <div class="project-card-outer">
    <div
      class="badge"
      :class="item.srdcloudProjectState"
    >
      {{ getSrdcloudStateLabel(item.srdcloudProjectState) }}
    </div>
    <!-- <div class="project-card-border"></div> -->
    <div class="project-card-content">
      <div class="project-info-item">
        <div
          class="w-100% cursor-pointer"
          @click="emits('item-detail', item)"
        >
          <AFlex
            align="center"
            justify="space-between"
          >
            <div
              :title="item.programmeName"
              class="line-clamp-1 mb-1 text-16px font-bold text-[#4B5071]"
            >
              {{ item.programmeName }}
            </div>
          </AFlex>

          <!-- 当前状态 -->
          <ASpace
            class="mb-1 mt-1"
            :gap="20"
          >
            <!-- 这里直接用a-tag,后端会配置背景这里没有stageCode, TODO:color追加 -->
            <ATag
              :color="item?.stage?.stageColor"
              :bordered="false"
              class="mr-0 font-bold"
            >
              {{ item?.stage?.stageName ?? '未知阶段' }}
            </ATag>

            <STag
              :color="getLevelColor(item.level).color"
              :bg-color="getLevelColor(item.level).bgColor"
              class="font-bold"
            >
              {{ item.level }}
            </STag>
          </ASpace>

          <AFlex
            class="w-full"
            :gap="10"
          >
            <div class="flex-1">
              <ARow class="my-2">
                <ACol
                  class="flex"
                  :span="10"
                >
                  <span class="w-86px whitespace-nowrap text-[var(--zion-gray-text-3)]">
                    项目负责人：
                  </span>
                  <span
                    class="overflow-hidden text-ellipsis whitespace-nowrap px-3px"
                    :title="item?.owner?.nickName || ''"
                  >
                    {{ item?.owner?.nickName || '--' }}
                  </span>
                </ACol>
                <ACol
                  :span="14"
                  class="flex items-center"
                >
                  <span class="w-76px whitespace-nowrap text-[var(--zion-gray-text-3)]">
                    技术经理：
                  </span>
                  <span
                    class="overflow-hidden text-ellipsis whitespace-nowrap px-3px"
                    :title="
                      item?.scrumMaster?.length
                        ? item?.scrumMaster.map((s: any) => s.nickName).join(',')
                        : ''
                    "
                  >
                    {{
                      item?.scrumMaster?.length
                        ? item?.scrumMaster.map((s: any) => s.nickName).join(',')
                        : '--'
                    }}
                  </span>
                </ACol>
              </ARow>

              <ARow class="my-2">
                <ACol :span="8">
                  <span class="text-[var(--zion-gray-text-3)]">版本：</span>
                  <MetricItem
                    :finished="item.milestone.finished"
                    :total="item.milestone.total"
                    :link="item.milestone.link"
                    @click="handleMetricClick"
                  />
                </ACol>
                <ACol
                  :span="8"
                  class="flex items-center"
                >
                  <span class="text-[var(--zion-gray-text-3)]">迭代：</span>
                  <MetricItem
                    :finished="item.iteration.finished"
                    :total="item.iteration.total"
                    :link="item.iteration.link"
                    @click="handleMetricClick"
                  />
                </ACol>
                <ACol :span="8">
                  <span class="text-[var(--zion-gray-text-3)]">需求：</span>
                  <MetricItem
                    :finished="item.demand.finished"
                    :total="item.demand.total"
                    :link="item.demand.link"
                    @click="handleMetricClick"
                  />
                </ACol>
              </ARow>

              <ARow>
                <ACol
                  :span="8"
                  class="flex items-center truncate"
                >
                  <span class="text-[var(--zion-gray-text-3)]">任务：</span>
                  <MetricItem
                    :finished="item.task.finished"
                    :total="item.task.total"
                    :link="item.task.link"
                    @click="handleMetricClick"
                  />
                </ACol>
                <ACol
                  :span="8"
                  class="truncate"
                >
                  <span class="text-[var(--zion-gray-text-3)]">漏洞：</span>
                  <MetricItem
                    only-show-total
                    :finished="item.bug.finished"
                    :total="item.bug.total"
                    :link="item.bug.link"
                    @click="handleMetricClick"
                  />
                </ACol>
                <ACol
                  :span="8"
                  class="flex items-center truncate"
                >
                  <span class="text-[var(--zion-gray-text-3)]">缺陷：</span>
                  <MetricItem
                    :finished="item.gap.finished"
                    :total="item.gap.total"
                    :link="item.gap.link"
                    @click="handleMetricClick"
                  />
                </ACol>
              </ARow>

              <ARow class="my-2">
                <ACol
                  :span="14"
                  class="whitespace-nowrap"
                >
                  <span class="w-76px text-[var(--zion-gray-text-3)]">交付时间：</span>
                  <span>
                    {{
                      item.projectDeliveryTime
                        ? dayjs(item.projectDeliveryTime).format('YYYY-MM-DD')
                        : '--'
                    }}
                  </span>
                </ACol>
                <ACol
                  :span="10"
                  class="flex items-center whitespace-nowrap"
                >
                  <span class="w-76px text-[var(--zion-gray-text-3)]">剩余天数：</span>
                  <span>{{ item?.remainingDays ?? '--' }}</span>
                </ACol>
              </ARow>

              <AFlex
                align="center"
                class="mt-3"
              >
                <span class="flex-shrink-0 text-[var(--zion-gray-text-3)]">研发进度：</span>
                <AProgress
                  class="m-0"
                  :stroke-color="{
                    '2%': getProgressLinearColor(item.processPercent)[0],
                    '100%': getProgressLinearColor(item.processPercent)[1],
                  }"
                  small
                  :percent="item.processPercent"
                  :format="() => (item.processPercent ? item.processPercent + '%' : '--')"
                />
              </AFlex>
            </div>
            <AFlex
              vertical
              align="center"
              :gap="5"
              class="ml-2 mt-[-5px] w-[90px] flex-shrink-0"
            >
              <AProgress
                type="circle"
                :size="90"
                :stroke-width="8"
                :stroke-color="getConformityColor(item.conformityPercent)"
                :percent="item.conformityPercent"
              >
                <template #format="percent">
                  <AFlex vertical>
                    <span
                      :style="{ color: getConformityColor(percent) }"
                      class="mb-1 text-[16px]"
                    >
                      {{ percent ? percent + '%' : '--' }}
                    </span>
                    <span class="text-[12px] text-[var(--zion-gray-text-2)]">过程符合度</span>
                  </AFlex>
                </template>
              </AProgress>
            </AFlex>
          </AFlex>
          <div
            v-if="canIedit"
            class="mt-3 h-10 w-full flex items-end justify-between border-t border-t-#E8E8E8 border-t-solid text-[#333]"
          >
            <div class="flex flex-1 items-center justify-center">
              <ATooltip title="项目详情">
                <div class="flex items-center gap-2">
                  <EyeOutlined @click="handleDropdownClick('detail')" />
                  查看
                </div>
              </ATooltip>
            </div>
            <ADivider
              type="vertical"
              style="height: 28px; transform: translateY(4px)"
            />
            <div class="flex flex-1 items-center justify-center">
              <!-- 触发按钮：开始研发，申请放行 -->
              <ATooltip
                v-if="item.devState === 0"
                title="研发初始化"
              >
                <div class="flex items-center gap-2">
                  <PlayCircleOutlined @click.stop="handleDropdownClick('start-dev')" />
                  开始研发
                </div>
              </ATooltip>
              <UserPermissions
                v-else
                permission="entryProjectSpace"
              >
                <ATooltip title="进入项目空间">
                  <div class="flex items-center gap-2">
                    <LoginOutlined
                      v-if="
                        [ProjectStage.DEV].includes(item.stage?.stageCode as ProjectStage) &&
                        item.devState === 1
                      "
                      @click.stop="handleDropdownClick('enter-workspace')"
                    />
                    进入研发空间
                  </div>
                </ATooltip>
              </UserPermissions>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- </ABadgeRibbon> -->
</template>

<style lang="less" scoped>
  .ant-ribbon-wrapper {
    :deep(.ant-ribbon) {
      border-radius: 3px 3px 2px 3px;
      top: 2px;
      font-size: 11px;
      line-height: 20px;
      height: 20px;
      opacity: 0.76;
    }
  }

  .project-card-outer {
    position: relative;
    // background: #fff;
    border-radius: 8px;
    box-shadow: 0px 2px 14px 0px rgba(226, 226, 226, 1);
    background: linear-gradient(180deg, #f3f6ff 0%, #ffffff 24%);
    display: flex;
    flex-direction: row;
    min-height: 180px;
    // border: 1px solid var(--zion-border-color);
  }
  .badge {
    position: absolute;
    top: 0;
    right: 0;
    color: #fff;
    font-size: 12px;
    width: 54px;
    height: 24px;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    text-align: center;
    padding-left: 10px;
    line-height: 24px;
    &.actived {
      background-image: url('@/assets/images/project_badge_actived.png');
    }
    &.closed {
      // background-image: url('@/assets/images/project_badge_closed.png');
      background-image: url('@/assets/images/project_badge_actived.png');
    }
    &.other {
      // background-image: url('@/assets/images/project_badge_other.png');
      background-image: url('@/assets/images/project_badge_actived.png');
    }
  }

  .project-card-border {
    width: 5px;
    background: var(--zion-blue-border-color);
    border-radius: 8px 0 0 8px;
  }

  .project-card-content {
    flex: 1;
    padding: 16px 20px 12px;
    padding-left: 22px;
    display: flex;
    flex-direction: column;
    width: 100%;
  }

  .project-card-header {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-bottom: 8px;
  }

  .project-info-item {
    .link-text {
      color: var(--ant-colorPrimary);
      text-decoration: underline;
      cursor: pointer;
    }
  }
</style>
