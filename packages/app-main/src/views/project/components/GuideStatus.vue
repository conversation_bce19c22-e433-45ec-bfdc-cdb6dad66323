// TODO:可能用流程图绘制
<script setup lang="ts">
import { ref, onMounted, computed, nextTick, onUnmounted } from 'vue';
import { CheckCircleFilled, CloseCircleFilled } from '@ant-design/icons-vue';
import ProjectGuide from '@/assets/images/project_guide.png';
import { useBreakpoint } from '@/hooks/useBreakpoint';

const { isLessThan } = useBreakpoint();

const ratioX = ref(1);
const guideChangeMode = ref<'add' | 'reduce'>('add');

type aiUsageCount = {
  assistantName: string;
  usageCount: number;
  assistantId: string;
}

const props = withDefaults(
  defineProps<{
    aiUsageCounts: aiUsageCount[];
  }>(),
  {
    aiUsageCounts: () => [],
  },
);
// 检测是否小于1366px
const isSmallScreen = computed(() => {
  return isLessThan('desktop');
});
// 小于1366下会占用整行
const baseImageWidth = computed(() => {
  return isSmallScreen.value ? 926 : 949;
});
const progressCpt = computed(() => {
  console.log(guideChangeMode.value)
  return [
    {
      status: 0,
      name: '需求助手',
      matchIds: ['13'],
      position: {
        left: 232 * ratioX.value + 'px',
        top: 157 + 'px',
      },
    },
    {
      status: 0,
      name: '代码工程助手',
      matchIds: ['7'],
      position: {
        left: 519 * ratioX.value + 'px',
        top: 157 + 'px',
      },
    },
    {
      status: 0,
      name: '编程助手',
      matchIds: ['1', '2', '3', '4'],
      position: {
        right: 45 * ratioX.value + 'px',
        top: 160 + 'px',
      },
    },
    {
      status: 0,
      name: '代码评审助手',
      matchIds: ['12'],
      position: {
        right: 180 * ratioX.value + 'px',
        bottom: 90 + 'px',
      },
    },
    {
      status: 0,
      name: '代码文档助手',
      matchIds: ['5'],
      position: {
        right: 180 * ratioX.value + 'px',
        bottom: 133 + 'px',
      },
    },
    {
      status: 0,
      name: '安全助手',
      matchIds: ['10'],
      position: {
        right: 180 * ratioX.value + 'px',
        bottom: 42 + 'px',
      },
    },
    {
      status: 0,
      name: '流水线助手',
      matchIds: ['11'],
      position: {
        left: 430 * ratioX.value + 'px',
        bottom: 93 + 'px',
      },
    },
    {
      status: 0,
      name: '测试助手',
      matchIds: ['6'],
      position: {
        left: 232 * ratioX.value + 'px',
        bottom: 93 + 'px',
      },
    },
  ];
});

// 根据 aiUsageCount 计算状态
const validCpt = computed(() => {
  return progressCpt.value.filter((item) => {
    // 只保留在 aiUsageCounts 中存在的助手
    return props.aiUsageCounts.some((usageItem: aiUsageCount) =>
      item.matchIds.includes(usageItem.assistantId)
    );
  }).map((item) => {
    // 查找符合的项
    const matchedItems = props.aiUsageCounts.filter((usageItem: aiUsageCount) => {
      return item.matchIds.includes(usageItem.assistantId)
    });
    // 计算总次数
    const useCount = matchedItems.reduce((acc, curr) => acc + curr.usageCount, 0);
    return {
      ...item,
      useCount,
      status: useCount > 0 ? 1 : 0,
    };
  });
});

const guideStatusRef = ref<HTMLDivElement>();
const calculateRatio = () => {
  if (guideStatusRef.value) {
    // 获取当前图片容器的实际宽度
    const currentImageWidth = guideStatusRef.value.clientWidth;
    if (document.documentElement.clientWidth === 1920) { // 基准
      ratioX.value = 1;
    } else {
      ratioX.value = Number((currentImageWidth / baseImageWidth.value).toFixed(2));
    }
  }
};
onMounted(() => {
  setTimeout(() => {
    calculateRatio();
  }, 1000);

  // 监听窗口大小变化
  window.addEventListener('resize', calculateRatio);
});

onUnmounted(() => {
  window.removeEventListener('resize', calculateRatio);
});
</script>

<template>
  <!-- TODO:换成png试试 -->
  <div class="max-w-[1200px] flex h-[410px] items-center justify-center relative overflow-hidden">
    <img class="h-full w-full" :src="ProjectGuide" ref="guideStatusRef" />
    <span v-for="(item, index) in validCpt" :key="index" class="absolute w-[14px] h-[14px]" :style="item.position">
      <a-tooltip :title="item.name + '使用次数：' + item.useCount" v-if="item.status === 0">
        <CloseCircleFilled :style="{ color: '#F53F3F' }" />
      </a-tooltip>
      <a-tooltip :title="item.name + '使用次数：' + item.useCount" v-else>
        <CheckCircleFilled :style="{ color: '#52C41A' }" />
      </a-tooltip>
    </span>
  </div>
</template>

<style scoped>
.guide-status {
  position: relative;
  width: 100%;
  height: 100%;
}
</style>
