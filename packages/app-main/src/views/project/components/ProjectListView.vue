<script setup lang="tsx">
  import {
  Button,
  message,
  Pagination,
  Progress,
  Space,
  Table,
  Tag,
  Tooltip,
} from 'ant-design-vue';
import { STag } from 'speed-components-ui/components';
import { computed, defineExpose, onMounted, ref, watch } from 'vue';
import { useRouter } from 'vue-router';

  import { getProjectList, type Project } from '@/apis/project/project';
import {
  getConformityColor,
  getLevelColor,
  getProgressStrokeColor,
  ProjectStage,
  projectStageOptions,
} from '@/dicts/project';
import { formatTime, renderTemplate } from '@/utils';

  import { transformProjectList } from '../helper';
import { useTable } from '../useLoad';
import MetricItem from './MetricItem.vue';
import ProjectItem from './ProjectItem.vue';

  const router = useRouter();
  const props = withDefaults(
    defineProps<{
      search?: Record<string, any>;
      cardConfig: { row: number };
      storeModeKey?: string;
      mode?: 'card' | 'table';
      showPagination?: boolean;
      canIedit?: boolean;
      pageParams?: {
        pageIndex?: number;
        pageSize?: number;
      };
    }>(),
    {
      storeModeKey: '',
      mode: 'card',
      showPagination: true,
      canIedit: true,
      pageParams: () => ({
        pageIndex: 1,
        pageSize: 10,
      }),
    },
  );

  const emit = defineEmits([
    'change', // 分页等
    'enter', // 进入项目
    'detail', // 详情
  ]);

  const innerMode = ref<'card' | 'table'>(
    (localStorage.getItem(props.storeModeKey) as 'card' | 'table') || 'card',
  );
  const options = computed(() => ({
    extraParams: props.search,
    // 修改stage为开始研发
    transformAfterFetch: (list: Project[]) => transformProjectList(list),
  }));
  const { dataSource, loading, pagination, handleTableChange, getList } = useTable(
    getProjectList,
    options,
  );
  // mock 20条项目数据
  // const dataSource = ref(
  //   Array.from({ length: 20 }, (_, i) => ({
  //     id: String(i + 1),
  //     programmeCode: `PROJ${String(i + 1).padStart(3, '0')}`,
  //     programmeName: `项目名称${i + 1}`,
  //     base64Icon: '',
  //     basePackage: `com.example.project${i + 1}`,
  //     description: `项目${i + 1}的描述信息`,
  //     developerIds: [],
  //     ownerId: 'owner1',
  //     level: 'C1',
  //     tenantId: 'tenant1',
  //     stage: 'develop',
  //     status: 0, // 0- 未开始, 4- 放行测试中
  //     milestone: { finished: 1, total: 3 },
  //     iteration: { finished: 4, total: 8 },
  //     demand: { finished: 3, total: 10 },
  //     task: { finished: 2, total: 5 },
  //     bug: { finished: 14, total: 20 },
  //     gap: { finished: 10, total: 20 },
  //     conformityPercent: 80 + (i % 5) * 3,
  //     processPercent: 20 + (i % 10) * 5,
  //     createTime: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString(),
  //   })),
  // );
  // const loading = ref(false);
  // const pagination = ref({ current: 1, pageSize: 10, total: 20 });

  // 读取localStorage
  onMounted(() => {
    const saved = localStorage.getItem(props.storeModeKey);
    if (saved === 'card' || saved === 'table') innerMode.value = saved;
  });
  watch(
    () => props.mode,
    (v) => {
      innerMode.value = v;
      localStorage.setItem(props.storeModeKey, v);
    },
  );
  // 监听默认分页参数
  watch(
    () => props.pageParams,
    (v) => {
      if (v) {
        pagination.value.current = v.pageIndex || 1;
        pagination.value.pageSize = v.pageSize || 10;
      }
    },
    {
      immediate: true,
    },
  );

  function handleEnterProject(item: Project) {
    const isToWorkspace = item.btnType === 'enter-workspace';
    // 判断不同阶段
    if (item.devState === 0 && !isToWorkspace) {
      router.push({ path: `/project/${item.id}/start-dev` });
      return;
    }
    // 进入项目控件

    const { id, sysAppKey } = item;
    const { href } = router.resolve({
      name: 'component-market',
      query: {
        appId: id,
        sysAppKey: sysAppKey || undefined,
      },
    });

    window.open(href, id);
  }
  const handleProjectDetail = (item: Project) => {
    router.push(`/project/detail/${item.id}/check`);
  };

  const handleMetricClick = (link: string, item: Project) => {
    if (link && link.trim() !== '') {
      if (!item.srdcloudProjectId) {
        message.warning('此项目暂未关联研发云');
        return;
      }
      window.open(renderTemplate(link, { projectId: item.srdcloudProjectId }), '_blank');
    }
  };

  const getStatusOption = (stage: ProjectStage | null) => {
    if (!stage) {
      return {
        tag: {
          color: 'rgba(0, 0, 0, 0.88)',
          bgColor: 'rgba(225,255,225, 0.2)',
        },
        label: '未知状态',
      };
    }
    return projectStageOptions.find((item) => item.value === stage);
  };

  // function getList() {
  //   // mock刷新
  //   loading.value = true;
  //   setTimeout(() => {
  //     loading.value = false;
  //   }, 500);
  // }

  // function handlePageChange(page: number) {
  //   pagination.value.current = page;
  //   getList();
  //   emit('change', page);
  // }

  defineExpose({ getList });

  const columns = [
    {
      title: '项目/产品',
      dataIndex: 'programmeName',
      key: 'programmeName',
      width: 260,
      fixed: 'left',
      ellipsis: true,
      customRender: ({ text, record }: any) => {
        return (
          <Tooltip title={text}>
            <a
              class="w-full truncate"
              onClick={() => handleProjectDetail(record)}
            >
              {text}
            </a>
          </Tooltip>
        );
      },
    },
    {
      title: '所处阶段',
      dataIndex: 'stage',
      key: 'stage',
      width: 100,
      customRender: ({ text }: any) => {
        return <Tag color={text?.stageColor}>{text?.stageName ?? '未知阶段'}</Tag>;
      },
    },
    {
      title: '级别',
      dataIndex: 'level',
      key: 'level',
      width: 70,
      customRender: ({ text }: any) => {
        return (
          <STag
            color={getLevelColor(text).color}
            bgColor={getLevelColor(text).bgColor}
          >
            {text ?? '未知级别'}
          </STag>
        );
      },
    },
    {
      title: '版本',
      dataIndex: 'milestone',
      key: 'milestone',
      customRender: ({ text, record }: any) => {
        return (
          <MetricItem
            finished={text.finished}
            total={text.total}
            link={text.link}
            onClick={(link: string) => handleMetricClick(link, record)}
          />
        );
      },
    },
    {
      title: '迭代',
      dataIndex: 'iteration',
      key: 'iteration',
      customRender: ({ text, record }: any) => {
        return (
          <MetricItem
            finished={text.finished}
            total={text.total}
            link={text.link}
            onClick={(link: string) => handleMetricClick(link, record)}
          />
        );
      },
    },
    {
      title: '需求',
      dataIndex: 'demand',
      key: 'demand',
      customRender: ({ text, record }: any) => {
        return (
          <MetricItem
            finished={text.finished}
            total={text.total}
            link={text.link}
            onClick={(link: string) => handleMetricClick(link, record)}
          />
        );
      },
    },
    {
      title: '任务',
      dataIndex: 'task',
      key: 'task',
      customRender: ({ text, record }: any) => {
        return (
          <MetricItem
            finished={text.finished}
            total={text.total}
            link={text.link}
            onClick={(link: string) => handleMetricClick(link, record)}
          />
        );
      },
    },
    {
      title: '漏洞',
      dataIndex: 'bug',
      key: 'bug',
      width: 80,
      customRender: ({ text, record }: any) => {
        return (
          <MetricItem
            finished={text.finished}
            total={text.total}
            link={text.link}
            only-show-total
            onClick={(link: string) => handleMetricClick(link, record)}
          />
        );
      },
    },
    {
      title: '缺陷',
      dataIndex: 'gap',
      key: 'gap',
      customRender: ({ text, record }: any) => {
        return (
          <MetricItem
            finished={text.finished}
            total={text.total}
            link={text.link}
            onClick={(link: string) => handleMetricClick(link, record)}
          />
        );
      },
    },
    {
      title: '研发进度',
      dataIndex: 'processPercent',
      key: 'processPercent',
      width: 160,
      customRender: ({ text }: any) => {
        return (
          <Progress
            class="m-0"
            strokeColor={getProgressStrokeColor(text)}
            percent={text}
            format={() => (text ? text + '%' : '--')}
          />
        );
      },
    },
    {
      title: '过程符合度',
      dataIndex: 'conformityPercent',
      key: 'conformityPercent',
      width: 160,
      customRender: ({ text }: any) => {
        return (
          <Progress
            class="m-0"
            strokeColor={getConformityColor(text)}
            percent={text}
            format={() => (text ? text + '%' : '--')}
          />
        );
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 180,
      customRender: ({ text }: any) => {
        return text ? formatTime(text) : '-';
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      fixed: 'right' as const,
      customRender: ({ record }: any) => {
        return (
          <Space size={5}>
            <Button
              size="small"
              type="link"
              onClick={() => handleProjectDetail(record)}
            >
              详情
            </Button>
            {[ProjectStage.DEV].includes(record.stage?.stageCode as ProjectStage) && (
              <>
                {record.devState === 0 && (
                  <Button
                    size="small"
                    type="link"
                    onClick={() => handleEnterProject(record)}
                  >
                    开始研发
                  </Button>
                )}
                {record.devState === 1 && (
                  <Button
                    size="small"
                    type="link"
                    onClick={() => handleEnterProject(record)}
                  >
                    进入项目空间
                  </Button>
                )}
              </>
            )}
          </Space>
        );
      },
    },
  ];

  const cardRow = computed(() => props.cardConfig?.row || 3);
</script>

<template>
  <div>
    <!-- card模式 -->
    <div v-if="innerMode === 'card'">
      <ASpin :spinning="loading">
        <div
          v-if="dataSource.length > 0"
          class="project-card-grid"
          :style="{
            display: 'grid',
            gridTemplateColumns: `repeat(${cardRow}, 1fr)`,
            gap: '28px 28px',
          }"
        >
          <ProjectItem
            v-for="item in dataSource"
            :key="item.id"
            :item="item"
            :can-iedit="canIedit"
            @btn-click="handleEnterProject"
            @item-detail="handleProjectDetail"
          />
        </div>
        <AEmpty
          v-if="dataSource.length === 0"
          description="暂无项目"
        />
      </ASpin>
    </div>
    <!-- table模式 -->
    <div
      v-else
      class="project-list-wrapper"
    >
      <!-- 增加一个横向滚动条 -->
      <Table
        :scroll="{ x: 1650 }"
        class="stat-table"
        :columns="columns"
        :data-source="dataSource"
        :loading="loading"
        empty-text="暂无项目"
        :row-key="(record) => record.id"
        :pagination="false"
      ></Table>
    </div>
    <div class="mt-4 flex justify-end">
      <Pagination
        v-if="showPagination"
        :current="pagination.current"
        :total="pagination.total"
        :page-size="pagination.pageSize"
        :show-size-changer="false"
        show-quick-jumper
        @change="
          (page) => handleTableChange({ current: page, pageSize: pagination.pageSize }, {}, {})
        "
      />
    </div>
  </div>
</template>

<style scoped lang="less">
  .project-list-wrapper {
    :deep(.ant-btn.ant-btn-link) {
      padding-left: 0px;
      padding-right: 0px;
    }

    :deep(.ant-divider) {
      margin-left: 0px;
      margin-right: 0px;
    }
  }
</style>
