<script setup lang="ts">
  import { PlusOutlined } from '@ant-design/icons-vue';
  import { message } from 'ant-design-vue';

  // import projectIcon1 from '@/assets/images/projectIcons/project-icon1.png';
  // import projectIcon2 from '@/assets/images/projectIcons/project-icon2.png';
  // import projectIcon3 from '@/assets/images/projectIcons/project-icon3.png';
  // import projectIcon4 from '@/assets/images/projectIcons/project-icon4.png';
  // import projectIcon5 from '@/assets/images/projectIcons/project-icon5.png';
  // import projectIcon6 from '@/assets/images/projectIcons/project-icon6.png';
  // import projectIcon7 from '@/assets/images/projectIcons/project-icon7.png';
  import { isImageFile } from '@/utils';

  interface Props {
    modelValue: string;
    onChange: (value: string) => void;
  }
  const props = defineProps<Props>();
  const builtInImgList = ref<string[]>([
    // projectIcon1,
    // projectIcon2,
    // projectIcon3,
    // projectIcon4,
    // projectIcon5,
    // projectIcon6,
    // projectIcon7,
  ]);
  const imgList = computed(() => {
    if (uploadImg.value) {
      return new Set([...builtInImgList.value, uploadImg.value]);
    }
    return builtInImgList.value;
  });
  const emit = defineEmits<{
    (e: 'update:modelValue', value: string): void;
  }>();
  function handleOpenSelectImg() {
    open.value = true;
    uploadImg.value = props.modelValue || '';
    selectImg.value = props.modelValue || '';
  }
  const open = ref(false);
  const uploadImg = ref('');
  const selectImg = ref('');
  const handleUploadChange = (info: any) => {
    return new Promise(function (resolve, reject) {
      if (!isImageFile(info.file?.name || '')) {
        message.error('请上传图片文件');
        return;
      }

      const isLt2M = (info.file?.size || 0) / 1024 / 1024 < 2;
      if (!isLt2M) {
        message.error('图片大小不能超过2MB');
        return;
      }

      const reader = new FileReader();
      let imgResult = '';
      reader.readAsDataURL(info.file);
      reader.onload = function () {
        uploadImg.value = reader.result as string;
        nextTick(() => {
          selectImg.value = reader.result as string;
        });
      };
      reader.onerror = function (error) {
        reject(error);
      };
      reader.onloadend = function () {
        resolve(imgResult);
      };
    });
  };
  const handleSelectImg = (item: string) => {
    selectImg.value = item;
  };
  const handleOk = () => {
    emit('update:modelValue', selectImg.value);
    open.value = false;
  };
</script>

<template>
  <div>
    <div
      class="img-preview"
      @click="handleOpenSelectImg"
    >
      <img
        v-if="props.modelValue"
        :src="props.modelValue"
        class="img-preview-img"
      />
      <div
        v-else
        class="img-preview-empty"
      >
        <PlusOutlined></PlusOutlined>
        <div class="ant-upload-text">选择图片</div>
      </div>
    </div>
    <AModal
      v-model:open="open"
      title="选择图片"
      width="460px"
      @ok="handleOk"
    >
      <div class="img-list">
        <img
          v-for="item in imgList"
          :key="item"
          :src="item"
          class="img-list-item"
          :class="{ active: selectImg === item }"
          @click="handleSelectImg(item)"
        />
        <AUpload
          name="avatar"
          list-type="picture-card"
          class="upload-img"
          accept="image/*"
          :show-upload-list="false"
          :before-upload="() => false"
          @change="handleUploadChange"
        >
          <div>
            <PlusOutlined></PlusOutlined>
            <div class="ant-upload-text">上传图片</div>
          </div>
        </AUpload>
      </div>
    </AModal>
  </div>
</template>

<style scoped lang="less">
  .img-preview {
    width: 80px;
    height: 80px;
    border: 1px dashed #f0f0f0;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    .img-preview-img {
      width: 100%;
      height: 100%;
      padding: 10px;
    }
    .img-preview-empty {
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: #999;
    }
  }
  .img-list {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
  }
  .upload-img {
    width: 60px;
    height: 60px;
    border: 1px dashed #f0f0f0;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    color: #999;
    & > :deep(.ant-upload) {
      border: none !important;
      width: 100% !important;
      height: 100% !important;
      border-radius: 4px !important;
    }
  }
  .img-list-item {
    width: 60px;
    height: 60px;
    border: 1px dashed #f0f0f0;
    border-radius: 4px;
    cursor: pointer;
    object-fit: cover;
    padding: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    &:hover,
    &.active {
      border-color: #1890ff;
      border-style: solid;
    }
  }
</style>
