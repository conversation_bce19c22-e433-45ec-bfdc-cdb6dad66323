import type { Project } from '@/apis/project/project';
import { ProjectStage } from '@/dicts/project';

/**
 * 转换项目数据中的 progress 数组为各个字段
 * @param item 项目数据
 * @returns 转换后的项目数据
 */
export function transformProjectData(item: Project): Project {
  // 处理 progress 数组数据
  const progressMap = new Map();
  if (item.progress && Array.isArray(item.progress)) {
    item.progress.forEach((progressItem) => {
      progressMap.set(progressItem.category, {
        finished: progressItem.completed,
        total: progressItem.total,
      });
    });
  }
  // 追加跳转链接
  return {
    ...item,
    // test
    stage: item.stage || ProjectStage.DEV,
    // 1-版本, 2-迭代, 3-需求, 4-任务, 5-漏洞, 6-缺陷 7- 研发进度 8 代码库数 9-cicd执行数
    milestone: progressMap.get(1)
      ? { ...progressMap.get(1), link: 'https://www.srdcloud.cn/version/{projectId}/version' }
      : { finished: '--', total: '--', link: '' },
    iteration: progressMap.get(2)
      ? {
          ...progressMap.get(2),
          link: 'https://www.srdcloud.cn/zxwim/{projectId}/setting/workspace',
        }
      : { finished: '--', total: '--', link: '' },
    demand: progressMap.get(3)
      ? { ...progressMap.get(3), link: 'https://www.srdcloud.cn/zxwim/{projectId}/allWorkItems' }
      : { finished: '--', total: '--', link: '' },
    task: progressMap.get(4)
      ? { ...progressMap.get(4), link: 'https://www.srdcloud.cn/zxwim/{projectId}/sprint' }
      : { finished: '--', total: '--', link: '' },
    bug: progressMap.get(5)
      ? { ...progressMap.get(5), link: 'https://www.srdcloud.cn/sqsc/{projectId}/report/fortify' }
      : { finished: '--', total: '--', link: '' },
    gap: progressMap.get(6)
      ? { ...progressMap.get(6), link: 'https://www.srdcloud.cn/taas/{projectId}/test-bug' }
      : { finished: '--', total: '--', link: '' },
    conformityPercent: item.processConformity || 0,
    processPercent: progressMap.get(7)?.finished || 0,
  };
}

/**
 * 转换项目列表数据
 * @param list 项目列表
 * @returns 转换后的项目列表
 */
export function transformProjectList(list: Project[]): Project[] {
  return list.map(transformProjectData);
}
