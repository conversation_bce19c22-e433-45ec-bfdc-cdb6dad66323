<template>
  <card-pane class="mb-6" :loading="loading">
    <AFlex justify="space-between" class="mb-4">
      <div class="cursor-pointer text-16px font-normal text-#565865" @click="backToDetailCheck">
        <LeftCircleOutlined style="color: #d8d8d8; margin-right: 8px" />
        返回
      </div>
    </AFlex>
    <!-- 这里bizId就是项目id -->
    <ClipTreeSelect :value="croppingDetail.checkItemIds" :programme-id="programmeId" mode="edit" :treeConfig="{
      checkable: false,
    }" />

    <AForm ref="formRef" :model="formValues" :rules="formRules" :label-col="{ span: 2 }" :wrapper-col="{ span: 16 }"
      class="mt-6">
      <!-- 详情显示审批记录，审批不显示 -->
      <AFormItem label="审批记录" v-if="!showApproval">
        <ASteps direction="vertical" progress-dot :current="croppingDetail.approveRecords.length">
          <AStep v-for="record in croppingDetail.approveRecords" :key="record.id">
            <template #description>
              <AFlex vertical class="text-[var(--zion-text-base)] leading-[25px]">
                <div>
                  <span>{{ record?.approver?.nickName }}</span>
                  <span class="mx-2 text-[var(--zion-gray-text-3)]">
                    {{ record.approveTime }}
                  </span>
                  <ATag :bordered="false" :color="getAuditOption(record.action)?.color">
                    {{ getAuditOption(record.action)?.label }}
                  </ATag>
                </div>
                <!-- 修改位置：第89行 -->
                <span v-if="record.opinion"> <!-- 仅保留条件判断 -->
                  审批意见：{{ record.opinion }}
                </span>
              </AFlex>
            </template>

          </AStep>
        </ASteps>
      </AFormItem>
      <AFormItem label="裁剪原因">
        <div class="text-[var(--ant-colorText)]">{{ formValues.reason || '--' }}</div>
      </AFormItem>

      <!-- <AFormItem label="提交人">
        <div class="text-[var(--ant-colorText)]">{{ formValues.createUserName || '--' }}</div>
      </AFormItem> -->

      <AFormItem label="审批意见" name="opinion" v-if="showApproval">
        <ATextarea v-model:value="formValues.opinion" :rows="3" placeholder="请输入审批意见" :maxlength="128" show-count />
      </AFormItem>
    </AForm>
    <!-- 仅在processing时显示审批按钮 -->
    <a-flex justify="center" :gap="10" class="mt-20" v-if="showApproval">
      <a-button type="primary" :loading="agreeLoading" @click="handleSubmitAudit('agree')">
        同意
      </a-button>
      <a-button danger :loading="rejectLoading" @click="handleSubmitAudit('reject')">
        拒绝
      </a-button>
      <a-button @click="router.push('/approval')">返回</a-button>
    </a-flex>
  </card-pane>
</template>

<script setup lang="ts">
import { ref, toRaw } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import type { FormInstance, Rule } from 'ant-design-vue/es/form';
import ClipTreeSelect from '@/components/ClipTreeSelect/index.vue';
import { message } from 'ant-design-vue';
import { APPROVE_STATUS_STRS } from '@/apis/code-generate/approval';
import { useCurrentUser } from '@/stores/currentUser';
import {
  fetchCroppingDetail,
  submitCroppingApproval,
  type CroppingDetail,
} from '@/apis/project/cropping';
import { getAuditOption } from '@/dicts/audit';

type FormValues = {
  reason: string;
  createUserName: string;
  opinion: string;
};
const currentUser = useCurrentUser();
const userInfo = computed(() => currentUser.state?.data.data);
// 表单数据
const formValues = ref<FormValues>({
  reason: '',
  createUserName: '',
  opinion: '',
});

// 表单验证规则
const formRules: Record<string, Rule[]> = {
  opinion: [
    {
      required: true,
      message: '请输入意见',
      trigger: 'blur'
    },
    {
      max: 500,
      message: '意见不能超过500字',
      trigger: 'blur'
    }
  ]
}


const router = useRouter();
const route = useRoute();
const loading = ref(false);
const formRef = ref<FormInstance>();
const agreeLoading = ref(false);
const rejectLoading = ref(false);
const approvalState = computed(() => {
  return route.query.approvalState;
});
const croppingDetail = ref<CroppingDetail>({
  checkItemIds: [],
  createUserName: '',
  reason: '',
  approveRecords: [],
});
// 当前登录人是否含有审批权限
const currentUserHasApprovalPermission = computed(() => {

  const approverIds = croppingDetail.value.approveRecords.map((item: any) => item.approver.id);
  return approverIds.includes(userInfo.value?.userId);
});
// 根据当前登录人查找审批记录,判断是否审批过了
const currentUserHasApprovaled = computed(() => {
  const record = croppingDetail.value.approveRecords.find((item: any) => item.approver.id === userInfo.value?.userId);
  return !!record?.action;
});
const programmeId = computed(() => {
  // 添加空值保护和类型提示
  return route.query.bizId?.toString() || ''
});

// 待审批，审批中 且当前登录人未审批过
const showApproval = computed(() => {
  return route.query.pageType === 'approve' && currentUserHasApprovalPermission.value && !currentUserHasApprovaled.value;
});


const initCroppingDetail = async () => {
  if (!route.query.id) {
    message.warning('缺少必要业务参数id');
    return;
  }
  loading.value = true;
  try {
    const res = await fetchCroppingDetail({
      approvalTaskId: route.query.id as string,
    });
    if (res.data.success && res.data.data) {
      croppingDetail.value = res.data.data;
      // 更新表单数据
      formValues.value.reason = res.data.data.reason || '';
      formValues.value.createUserName = res.data.data.createUserName || '';
    }
  } catch (error) {
    console.error('获取裁剪详情失败', error);
  } finally {
    loading.value = false;
  }
};

const backToDetailCheck = () => {
  router.go(-1);
};

/**
 * 处理审批提交
 * @param action 审批动作：agree-同意, reject-拒绝
 */
const handleSubmitAudit = async (action: 'agree' | 'reject') => {
  try {
    // 表单验证
    await formRef.value?.validate();

    if (action === 'agree') {
      agreeLoading.value = true;
    } else {
      rejectLoading.value = true;
    }

    // 调用审批API
    const res = await submitCroppingApproval({
      action,
      approvalId: route.query.id as string,
      opinion: formValues.value.opinion,
    });

    if (res.data.success) {
      message.success(action === 'agree' ? APPROVE_STATUS_STRS.PASS : APPROVE_STATUS_STRS.REJECT);
      router.push('/approval');
    } else {
      message.error(res.data.errMessage || '审批失败');
    }
  } catch (error) {
    console.error('审批提交失败', error);
  } finally {
    if (action === 'agree') {
      agreeLoading.value = false;
    } else {
      rejectLoading.value = false;
    }
  }
};

// 获取裁剪详情信息
initCroppingDetail();
</script>

<style lang="less" scoped>
.clip-config-content {
  padding: 24px;

  .tree-container {
    margin-top: 24px;
    max-height: 400px;
    overflow-y: auto;
  }
}
</style>
