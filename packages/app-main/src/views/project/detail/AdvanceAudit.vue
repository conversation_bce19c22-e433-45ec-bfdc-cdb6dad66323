<script setup lang="ts">
import { projectStageOptions, type ProjectStage } from '@/dicts/project';
import {
  CheckCircleFilled,
  CloseCircleFilled,
  ExclamationCircleFilled,
  CaretDownOutlined,
} from '@ant-design/icons-vue';
import StepRightArrow from '@/assets/images/step_bread_arrow.png';
import { onMounted, ref, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useCurrentUser } from '@/stores/currentUser';
import {
  fetchStageDetail,
  submitStageApproval,
  type Approver,
  type StageAuditDetail,
} from '@/apis/project/project';
import { message } from 'ant-design-vue';
import type { FormInstance } from 'ant-design-vue';
import { APPROVE_STATUS, APPROVE_STATUS_STRS } from '@/apis/code-generate/approval';
import { getProjectQualityCheckResults, type AgileStage } from '@/apis/checkpoint/agileStage';
import type { StepItem } from '@/components/ProjectSteps/index.vue';
import { AUDTI_STATUS, getAuditOption, getStatusOption } from '@/dicts/audit';
import { renderTemplate } from '@/utils';
import CheckTree from '@/components/ClipTreeSelect/CheckTree.vue';
const route = useRoute();
const router = useRouter();
const loading = ref(false);
const formRef = ref<FormInstance>();
const currentUser = useCurrentUser();
const userInfo = computed(() => currentUser.state?.data.data);
const auditForm = ref({
  opinion: '',
});

// 详情数据
const stageAuditDetail = ref<StageAuditDetail>({
  approveRecords: [],
  approvers: [],
  currentStage: '',
  nextStage: '',
  status: undefined,
});

const steps = ref<StepItem[]>([]); // 全量阶段配置
const programmeLoading = ref(false);
const backToApproval = () => {
  router.push('/approval');
};

const agreeLoading = ref(false);
const rejectLoading = ref(false);
// 提交审批
const handleSubmitAudit = async (action: 'agree' | 'reject') => {
  try {
    // 表单验证
    await formRef.value?.validate();

    if (action === 'agree') {
      agreeLoading.value = true;
    } else {
      rejectLoading.value = true;
    }

    // 调用审批API
    const res = await submitStageApproval({
      action,
      approvalId: route.query.id as string,
      opinion: auditForm.value.opinion,
    });

    if (res.data.success) {
      message.success(action === 'agree' ? APPROVE_STATUS_STRS.PASS : APPROVE_STATUS_STRS.REJECT);
      router.push('/approval');
    } else {
      message.error(res.data.errMessage || '审批失败');
    }
  } catch (error) {
    console.error('审批提交失败', error);
  } finally {
    if (action === 'agree') {
      agreeLoading.value = false;
    } else {
      rejectLoading.value = false;
    }
  }
};

const getStageText = (stageId: string) => {
  return steps.value.find((item: any) => item.value === stageId)?.title;
};
// 当前阶段的指标
const curStageTreeData = computed(() => {
  return steps.value.find((item: any) => item.value === stageAuditDetail.value.currentStage);
});
const initAdvanceDetail = async () => {
  if (!route.query.bizId) {
    message.warning('缺少必要业务参数bizId');
    return;
  }
  loading.value = true;
  try {
    const res = await fetchStageDetail({
      advanceId: route.query.bizId as string,
    });
    if (res.data.success && res.data.data) {
      stageAuditDetail.value = res.data.data;
    }
  } catch (error) {
    console.error('获取阶段推进详情失败', error);
  } finally {
    loading.value = false;
  }
};
// 当前登录人是否含有审批权限
const currentUserHasApprovalPermission = computed(() => {
  const approverIds = stageAuditDetail.value.approveRecords.map((item: any) => item.approver.id);
  return approverIds.includes(userInfo.value?.userId);
});
// 根据当前登录人查找审批记录,判断是否审批过了
const currentUserHasApprovaled = computed(() => {
  const record = stageAuditDetail.value.approveRecords.find((item: any) => item.approver.id === userInfo.value?.userId);
  return !!record?.action;
});
// 待审批，审批中 且当前登录人未审批过
const showApproval = computed(() => {
  return route.query.pageType === 'approve' && currentUserHasApprovalPermission.value && !currentUserHasApprovaled.value;
});
const initAgilestageByProId = async () => {
  // 判断是否是裁剪操作(edit是裁剪)
  if (!route.query.programmeId) {
    message.warning('缺少必要参数programmeId');
    return;
  }
  programmeLoading.value = true;

  try {
    const res = await getProjectQualityCheckResults(route.query.programmeId as string);

    if (res.data && res.data.success && res.data.data) {
      const agileData = res.data.data;
      // 按照 stageOrder 排序
      const sortedAgileData = agileData.sort(
        (a: AgileStage, b: AgileStage) => a.stageOrder - b.stageOrder,
      );
      steps.value = sortedAgileData.map((item: AgileStage) => ({
        title: item.stageName,
        value: item.id || '',
        treeData: item.agileStageItemVOList || [],
      }));
    }
  } catch (error) {
    console.error('加载数据失败:', error);
  } finally {
    programmeLoading.value = false;
  }
};
// 获取阶段推进详情信息
initAdvanceDetail();
// 根据项目id获取所有阶段配置
initAgilestageByProId();

// 安全解析 JSON
const safeParseJSON = (jsonString: string) => {
  try {
    return JSON.parse(jsonString);
  } catch (error) {
    console.warn('JSON 解析失败:', jsonString, error);
    return [];
  }
};
// TODO:研发云跳转
const handleLinkClick = (link: { btn_name: string; btn_url: string } | string) => {
  if (typeof link === 'string') {
    return;
  }

  // 校验是否是有效链接
  if (!link.btn_url || typeof link.btn_url !== 'string') {
    console.warn('无效的链接地址:', link.btn_url);
    return;
  }
  // 正则校验 URL 协议
  const urlRegex = /^(https?|ftp|file):\/\/.+/i;
  if (!urlRegex.test(link.btn_url)) {
    console.warn('无效的 URL 协议:', link.btn_url);
    return;
  }

  // 正则替换 {projectId} 为固定的 srdProjectId(TODO: 替换为研发云项目id)
  const processedUrl = renderTemplate(link.btn_url, {
    projectId: route.query.programmeId as string,
  });

  window.open(processedUrl, '_blank');
};
</script>

<template>
  <div class="audit-page">
    <!-- 阶段推进 -->
    <card-pane class="mb-6" :loading="loading">
      <AFlex justify="space-between">
        <div class="cursor-pointer text-16px font-normal text-#565865" @click="backToApproval">
          <LeftCircleOutlined style="color: #d8d8d8; margin-right: 8px" />
          返回
        </div>
      </AFlex>
      <div class="px-30 py-6 max-h-[700px] min-h-[400px]">
        <a-flex class="h-full">
          <AForm class="flex-1" ref="formRef" :model="auditForm" :label-col="{ span: 4 }" :wrapper-col="{ span: 14 }">
            <AFormItem label="阶段推进">
              {{ getStageText(stageAuditDetail.currentStage) }}
              <span>
                <img :src="StepRightArrow" class="w-[7px] mx-[13px] h-auto" />
              </span>
              {{ getStageText(stageAuditDetail.nextStage) }}
            </AFormItem>
            <AFormItem label="审批人">
              {{stageAuditDetail.approvers.map((item: Approver) => item.roleName ? item.roleName + '-' + item.nickName :
                item.nickName).join(',')}}
            </AFormItem>
            <AFormItem label="审批状态">
              {{
                getStatusOption(stageAuditDetail?.status as unknown as AUDTI_STATUS)?.label ?? ''
              }}
            </AFormItem>
            <!-- 详情才显示审批记录 -->
            <AFormItem label="审批记录" v-if="route.query.approvalState !== APPROVE_STATUS.INIT">
              <!-- 阶段直接到最后一步 -->
              <ASteps direction="vertical" progress-dot :current="stageAuditDetail.approveRecords.length">
                <AStep v-for="record in stageAuditDetail.approveRecords" :key="record.id">
                  <template #description>
                    <AFlex vertical class="text-[var(--zion-text-base)] leading-[25px]">
                      <div>
                        <span>
                          {{ record.approver.nickName }}
                        </span>
                        <span class="mx-2 text-[var(--zion-gray-text-3)]">
                          {{ record.approveTime }}
                        </span>
                        <ATag :bordered="false" :color="getAuditOption(record.action)?.color">
                          {{ getAuditOption(record.action)?.label }}
                        </ATag>
                      </div>
                      <span>{{ record.opinion }}</span>
                    </AFlex>
                  </template>
                </AStep>
              </ASteps>
            </AFormItem>
            <AFormItem label="审批意见" name="opinion" :rules="[{ required: true, message: '请输入审批意见' }]"
              v-if="showApproval">
              <ATextarea v-model:value="auditForm.opinion" placeholder="请输入" :rows="4" show-count :maxlength="128" />
            </AFormItem>
          </AForm>


          <div class="check-result-wrapper">
            <a-spin :spinning="programmeLoading">
              <div class="title">
                当前阶段（{{ getStageText(stageAuditDetail.currentStage) }}）检查结果
              </div>
              <a-flex
                :class="['py-4 overflow-y-auto flex-1', curStageTreeData?.treeData && curStageTreeData?.treeData?.length > 0 ? 'pl-[10%]' : 'flex justify-center']">
                <CheckTree v-if="curStageTreeData?.treeData && curStageTreeData?.treeData?.length > 0"
                  :tree-data="curStageTreeData?.treeData ?? []" :show-link-btn="false"
                  :srdcloudProjectId="route.query.srdcloudProjectId as string" />
                <a-empty v-else description="暂无检查结果" />
              </a-flex>
            </a-spin>
          </div>
        </a-flex>
        <!-- 仅在processing时显示审批按钮,且当前登录人未审批过 -->
        <a-flex justify="center" :gap="10" class="mt-20" v-if="showApproval">
          <a-button type="primary" :loading="agreeLoading" @click="handleSubmitAudit('agree')">
            同意
          </a-button>
          <a-button danger :loading="rejectLoading" @click="handleSubmitAudit('reject')">
            拒绝
          </a-button>
          <a-button @click="backToApproval">返回</a-button>
        </a-flex>
      </div>
    </card-pane>
  </div>
</template>

<style scoped lang="less">
.check-result-wrapper {
  border-radius: 2px;
  flex: 0 0 530px;
  display: flex;
  flex-direction: column;
  border: 1px solid var(--zion-border-color);

  .title {
    background-color: var(--zion-table-header-bg);
    padding-left: 40px;
    height: 40px;
    line-height: 40px;
    position: relative;

    &::before {
      position: absolute;
      content: ' ';
      left: 18px;
      top: 50%;
      width: 10px;
      height: 13px;
      background: url('@/assets/images/card_title_icon.png') no-repeat center center;
      background-size: 100% 100%;
      transform: translateY(-50%);
    }
  }
}
</style>
