<script setup lang="ts">
  import { message } from 'ant-design-vue';
  import { computed, onMounted, ref } from 'vue';
  import { useRoute, useRouter } from 'vue-router';

  import { type CroppingDetail, fetchCroppingDetail } from '@/apis/project/cropping';
  import {
    fetchStageDetail,
    getProjectDetailById,
    getSprintList,
    type ProgressStat,
    type Project,
    type Sprint,
    type StageAuditDetail,
  } from '@/apis/project/project';
  import LiquidFillScore from '@/components/LiquidFillScore/index.vue';
  import { getConformityLiquidFillStageOptions } from '@/dicts/project';
  import { useBreakpoint } from '@/hooks/useBreakpoint';

  import Check from './Check.vue';

  const { isLessThan } = useBreakpoint();
  const route = useRoute();
  const router = useRouter();
  const maturity = ref(0);
  // 阶段审批详情
  const stageAuditDetail = ref<StageAuditDetail>({
    approveRecords: [],
    approvers: [],
    currentStage: '',
    nextStage: '',
    status: '',
  });
  // 裁剪审批详情
  const croppingDetail = ref<CroppingDetail>({
    checkItemIds: [],
    createUserName: '',
    reason: '',
    approveRecords: [],
  });

  const loading = ref(false);
  const projectInfo = ref<Project>();
  const stats = ref([
    { label: '人数', value: '--' },
    { label: '需求数', value: '--' },
    { label: '迭代数', value: '--' },
    { label: '代码库数', value: '--' },
    { label: 'CI/CD 执行数', value: '--' },
  ]);
  const isSmallScreen = computed(() => {
    return isLessThan('1440');
  });
  const initStageApproval = async () => {
    const res = await fetchStageDetail({
      programmeId: programmeId.value,
      sprintId: sprintId.value,
    });
    if (res?.data?.data) {
      stageAuditDetail.value = res.data.data;
    }
  };
  const initCroppingApproval = async () => {
    const res = await fetchCroppingDetail({
      programmeId: programmeId.value,
    });
    if (res?.data?.data) {
      croppingDetail.value = res.data.data;
    }
  };
  const initProjectInfo = async () => {
    try {
      loading.value = true;
      const res = await getProjectDetailById(programmeId.value);
      if (res?.data?.data) {
        projectInfo.value = res.data.data;
        // 更新一部分顶部统计
        stats.value[0].value = projectInfo.value.userCount;
        const progressStat = projectInfo.value.progress;
        // 获取数组内部的值，2-需求数，8-代码库数  9- ci/cd执行数，这里都取total
        if (progressStat && progressStat.length > 0) {
          const demand = progressStat.find((it: ProgressStat) => it.category === 3);
          const iteration = progressStat.find((it: ProgressStat) => it.category === 2);
          const code = progressStat.find((it: ProgressStat) => it.category === 8);
          const ci = progressStat.find((it: ProgressStat) => it.category === 9);
          demand && demand.total && (stats.value[1].value = demand.total.toString());
          iteration && iteration.total && (stats.value[2].value = iteration.total.toString());
          code && code.total && (stats.value[3].value = code.total.toString());
          ci && ci.total && (stats.value[4].value = ci.total.toString());
        }
        // 过程符合度
        projectInfo.value.processConformity &&
          (maturity.value = projectInfo.value.processConformity);
        // 阶段审批人列表
        projectInfo.value.stageApprovers = [
          ...(projectInfo.value.owner ? [projectInfo.value.owner] : []),
          ...(projectInfo.value.scrumMaster?.length ? projectInfo.value.scrumMaster : []),
        ];
        loading.value = false;
      }
    } catch (e) {
      loading.value = false;
      message.error('获取项目详情失败');
    }
  };
  const sprintList = ref<Sprint[]>([]);
  const sprintId = ref();

  const initSprint = async () => {
    const res = await getSprintList(programmeId.value);
    if (res?.data?.data) {
      sprintList.value = res.data.data || [];
      sprintId.value = sprintList.value[0]?.id;
    }
  };
  const programmeId = computed(() => {
    return route.params.id as string;
  });
  onMounted(async () => {
    const programmeId = route.params.id as string;
    if (!programmeId) {
      message.warning('项目ID不存在');
      return;
    }
    initProjectInfo();
    await initSprint();
    // 获取下阶段审批，
    // initStageApproval();
    // 裁剪审批 可以提交多次，这里无法通过项目id查
    // initCroppingApproval();
  });
  watch(
    sprintId,
    (newVal) => {
      newVal && initStageApproval();
    },
    {
      immediate: true,
    },
  );
  const backToProjectList = () => {
    router.push('/project');
  };
  // 自定义format
  const serizeOptions = computed(() => {
    return {
      label: {
        formatter: () =>
          '{value|' + (maturity.value ? maturity.value + '%' : '--') + '}\n{title|过程符合度}',
        rich: {
          title: {
            color: getConformityLiquidFillStageOptions(maturity.value).valueStyle.color,
            fontSize: 12,
            lineHeight: 20,
          },
          value: {
            color: getConformityLiquidFillStageOptions(maturity.value).valueStyle.color,
            fontSize: 25,
            lineHeight: 25,
          },
        },
        fontSize: 14,
        align: 'center',
      },
    };
  });
</script>

<template>
  <div class="project-detail-page min-h-screen">
    <!-- 顶部 -->
    <div class="mb-6 flex items-center rounded-[10px] bg-[#fff] p-16px">
      <div
        :class="[
          'blue-border-left flex relative pl-[24px] rounded-l-[4px] items-center  border-l-4 from-blue-50/0 via-blue-200/10 to-blue-300/10',
          isSmallScreen ? 'w-[400px] shrink-0' : 'flex-1 max-w-46%',
        ]"
      >
        <AFlex justify="space-between">
          <div
            class="absolute top-[0px] cursor-pointer text-16px font-normal text-#565865"
            @click="backToProjectList"
          >
            <LeftCircleOutlined style="color: #d8d8d8; margin-right: 8px" />
            返回
          </div>
        </AFlex>
        <div
          class="overflow-hidden-two mr-10 mt-10px flex-1 pt-20px text-2xl font-bold"
          :title="projectInfo?.programmeName"
        >
          <ASkeleton
            v-if="loading"
            active
            :paragraph="{ rows: 2 }"
          />
          <div
            v-if="!loading"
            class="mb-7px overflow-hidden text-ellipsis whitespace-nowrap"
            :title="projectInfo?.programmeName"
          >
            {{ projectInfo?.programmeName }}
          </div>
          <ARow
            v-if="!loading"
            :gutter="20"
            class="flex-nowrap"
          >
            <ACol class="flex items-center text-12px">
              <span class="flex items-center whitespace-nowrap color-[rgba(0,0,0,.5)]">
                <img
                  src="@/assets/images/user/xmjl.png"
                  class="mr-7px inline-block h-16px w-16px rd-50%"
                />
                项目负责人：
              </span>
              <ATag
                v-if="projectInfo?.owner"
                class="mr-2 h-22px rounded-4px border-none bg-[#f4f6fa] px-1.75 text-12px line-height-22px text-[#5a6dff]"
              >
                {{ projectInfo?.owner?.nickName || '--' }}
              </ATag>
              <span
                v-else
                class="px-3px"
              >
                --
              </span>
            </ACol>
            <ACol
              :span="16"
              class="flex items-center text-12px"
            >
              <span class="w-96px flex items-center whitespace-nowrap color-[rgba(0,0,0,.5)]">
                <img
                  src="@/assets/images/user/zy2.png"
                  class="mr-7px inline-block h-16px w-16px rd-50%"
                />
                技&nbsp术&nbsp经&nbsp理：
              </span>
              <APopover
                v-if="projectInfo?.scrumMaster?.length"
                placement="right"
              >
                <template #content>
                  <div class="max-w-320px">
                    <ATag
                      v-for="user in projectInfo.scrumMaster"
                      :key="user.id"
                      class="mr-2 rounded-4px border-none bg-[#f4f6fa] px-1.75 text-12px text-[#5a6dff]"
                    >
                      {{ user?.nickName || '' }}
                    </ATag>
                  </div>
                </template>
                <div class="overflow-hidden text-ellipsis whitespace-nowrap">
                  <ATag
                    v-for="user in projectInfo.scrumMaster"
                    :key="user.id"
                    class="mr-2 rounded-4px border-none bg-[#f4f6fa] px-1.75 text-12px text-[#5a6dff]"
                  >
                    {{ user?.nickName || '' }}
                  </ATag>
                </div>
              </APopover>
              <span
                v-else
                class="px-3px"
              >
                --
              </span>
            </ACol>
          </ARow>
          <ARow
            v-if="!loading"
            class="my-0"
          >
            <ACol
              class="flex items-start text-12px"
              :span="24"
            >
              <span class="w-96px flex items-center whitespace-nowrap color-[rgba(0,0,0,.5)]">
                <img
                  src="@/assets/images/user/zy.png"
                  class="mr-7px inline-block h-16px w-16px rd-50%"
                />
                项&nbsp目&nbsp成&nbsp员：
              </span>
              <APopover
                v-if="projectInfo?.developers?.length"
                placement="right"
              >
                <template #content>
                  <div class="max-w-320px">
                    <ATag
                      v-for="user in projectInfo.developers"
                      :key="user.id"
                      class="mr-2 rounded-4px border-none bg-[#f4f6fa] px-1.75 text-12px text-[#5a6dff]"
                    >
                      {{ user.nickName || user.username || '' }}
                    </ATag>
                  </div>
                </template>
                <div class="line2">
                  <ATag
                    v-for="user in projectInfo.developers"
                    :key="user.id"
                    class="mr-2 rounded-4px border-none bg-[#f4f6fa] px-1.75 text-12px text-[#5a6dff]"
                  >
                    {{ user.nickName || user.username || '' }}
                  </ATag>
                </div>
              </APopover>
              <span
                v-else
                class="px-3px"
              >
                --
              </span>
            </ACol>
          </ARow>
        </div>
        <div class="h-[130px] w-[130px] flex items-center justify-center p-2">
          <LiquidFillScore
            :value="maturity"
            :serize-options="serizeOptions"
            :stage-options="getConformityLiquidFillStageOptions(maturity)"
          />
        </div>
      </div>

      <div class="ml-20 h-[67px] flex flex-1 items-center justify-around pr-[40px]">
        <template
          v-for="(item, idx) in stats"
          :key="item.label"
        >
          <div class="flex flex-col items-center">
            <div
              :class="[
                'mt-1 mb-[12px] text-[30px] font-bold text-[#4B5071] whitespace-nowrap',
                idx === 1 && 'text-[var(--zion-warning-text)]',
              ]"
            >
              {{ item.value }}
            </div>
            <div class="whitespace-nowrap text-[16px] text-[#4B5071]">{{ item.label }}</div>
          </div>
          <!-- 只在中间插入分割线 -->
          <div
            v-if="idx < stats.length - 1"
            class="mx-4 h-[30px] w-[1px] border-r bg-[var(--zion-border-color-1)]"
          ></div>
        </template>
      </div>
    </div>

    <Check
      v-model:sprint-id="sprintId"
      :project-info="projectInfo"
      :stage-audit-detail="stageAuditDetail"
      :sprint-list="sprintList"
      @refresh-stage-audit="initStageApproval"
    />
  </div>
</template>

<style lang="less" scoped>
  .project-detail-page {
    min-height: 100vh;
  }

  .line2 {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
  }
</style>
