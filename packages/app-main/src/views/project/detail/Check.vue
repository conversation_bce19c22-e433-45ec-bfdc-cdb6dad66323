<script setup lang="ts">
  import { ScissorOutlined } from '@ant-design/icons-vue';
import { computed, h, ref } from 'vue';
import { useRoute } from 'vue-router';

  import { type Project, type Sprint, type StageAuditDetail } from '@/apis/project/project';
import ClipTreeSelect from '@/components/ClipTreeSelect/index.vue';
import { type StepItem } from '@/components/ProjectSteps/index.vue';
import UserPermissions from '@/components/UserPermissions/index.vue';
import { useBreakpoint } from '@/hooks/useBreakpoint';
import { useUserPermission } from '@/hooks/useUserPermissions';

  import AdvanceModal from '../components/AdvanceModal.vue';
import ClipConfigModal from '../components/ClipConfigModal.vue';
import GuideStatus from '../components/GuideStatus.vue';

  const { isLessThan } = useBreakpoint();

  const props = defineProps<{
    projectInfo: Project;
    stageAuditDetail: StageAuditDetail;
    sprintList?: Sprint[];
    sprintId?: string;
  }>();
  const currentSprint = computed({
    get() {
      return props.sprintId;
    },
    set(value: string) {
      console.log(1111, value);

      emit('update:sprint-id', value);
    },
  });
  const route = useRoute();
  const steps = ref<StepItem[]>([]);
  const canIusePushBtn = useUserPermission('project.stage.push');
  const emit = defineEmits<{
    (e: 'refresh-stage-audit'): void;
    (e: 'update:sprint-id', value: string): void;
  }>();
  const guideText = [
    {
      title: '需求和设计阶段',
      descs: [
        '需求文档助手：助力用户实现需求的生成和优化。',
        '文档助手：为用户提供高效、便捷、智能的文档处理体验。',
        '工作项助手：进行工作项拆分和任务管理，提升项目管理效率。（待上线）',
      ],
    },
    {
      title: '开发阶段',
      descs: [
        '编程助手：为用户提供代码辅助开发和优化等服务。',
        '代码工程助手：为用户提供需求书、架构设计书及前端或后端代码工程生成服务。',
        '代码评审助手：为用户提供代码变更的评审意见和优化建议生成服务。',
      ],
    },
    {
      title: '测试阶段',
      descs: [
        '测试助手：为用户提供测试用例和脚本生成服务。',
        '流水线助手：为用户提供流水线报错解释服务。',
        '安全助手：为用户提供缺陷解释和优化建议生成服务。',
      ],
    },
    {
      title: '运维和连续交付阶段',
      descs: [
        '智能问答：为用户提供数据分析和生成服务。',
        '度量助手：为用户提供开发过程中的技术问题及研发云使用问题解答服务。',
      ],
    },
  ];
  // 获取全量阶段数据
  const getSteps = (data: StepItem[]) => {
    steps.value = data;
  };
  //当前阶段id
  const currentStageId = computed<string>(() => {
    const sprint = props.sprintList?.find((item) => item.sprintId === props.sprintId);
    return sprint?.currentStageId;
  });
  // 当前阶段索引
  const currentStageIndex = computed<number>(() => {
    return steps.value.findIndex((item: StepItem) => item.value === currentStageId.value);
  });
  // 当前阶段配置
  const currentStageConfig = computed<StepItem>(() => {
    return steps.value.find((item) => item.value === currentStageId.value)!;
  });

  // 下一个阶段配置
  const nextStageConfig = computed<StepItem>(() => {
    if (currentStageIndex.value === steps.value.length) {
      return currentStageConfig.value;
    }
    return steps.value[currentStageIndex.value + 1]!;
  });

  const clipModalVisible = ref(false);
  const advanceModalVisible = ref(false);

  const handleClip = () => {
    clipModalVisible.value = true;
  };
  const programmeId = computed(() => {
    return route.params.id as string;
  });
  // 推进（这里用弹框显示，审批放到其他地方了）
  const handleAdvance = () => {
    advanceModalVisible.value = true;
  };

  const handleAdvanceSuccess = () => {
    console.log('阶段推进成功');
    // 刷新详情
    emit('refresh-stage-audit');
    // 可以在这里刷新数据或进行其他操作
  };

  const handleClipOk = (checkedKeys: string[]) => {
    console.log('checkedKeys', checkedKeys);
    // TODO: 调用接口更新流程
  };

  // 检测是否小于1366px
  const isSmallScreen = computed(() => {
    return isLessThan('desktop');
  });
  const isDev = computed(() => {
    return import.meta.env.MODE === 'development';
  });
  const srdcloudProjectId = computed(() => {
    return props.projectInfo?.srdcloudProjectId;
  });
  const createIteration = (type: 'new' | 'old') => {
    if (type === 'new') {
      window.open(
        `https://www.srdcloud.cn/srdagile/${srdcloudProjectId.value}/iteration`,
        '_blank',
      );
    } else {
      window.open(
        `https://www.srdcloud.cn/zxwim/${srdcloudProjectId.value}/setting/workspace
`,
        '_blank',
      );
    }
  };
  const deleteIteration = () => {
    window.open(`https://www.srdcloud.cn/srdagile/${srdcloudProjectId.value}/iteration`, '_blank');
  };
</script>

<template>
  <div>
    <!-- 研发流程 -->
    <CardPane>
      <template #title>
        <div class="flex items-center">
          <span>项目研发流程概览</span>
          <!-- <GuideLegend /> -->
          <div class="ml-20 font-normal">
            选择迭代：
            <ASelect
              v-model:value="currentSprint"
              style="width: 160px"
              placeholder="请选择迭代"
            >
              <ASelectOption
                v-for="item in sprintList"
                :key="item.id"
                :value="item.id"
              >
                {{ item.sprintName }}
              </ASelectOption>
            </ASelect>
          </div>
        </div>
      </template>
      <template #extra>
        <ASpace>
          <ADropdown>
            <template #overlay>
              <AMenu>
                <AMenuItem @click="createIteration('old')">研发云旧版工作项</AMenuItem>
                <AMenuItem @click="createIteration('new')">研发云自研工作项</AMenuItem>
              </AMenu>
            </template>
            <AButton
              style="background: #607cee"
              type="primary"
            >
              创建迭代
              <DownOutlined />
            </AButton>
          </ADropdown>
          <AButton
            type="primary"
            style="background: #ffa123"
            @click="deleteIteration"
          >
            结束迭代
          </AButton>
          <!-- 过程裁剪 -->
          <AButton
            v-if="isDev"
            type="primary"
            danger
            :icon="h(ScissorOutlined)"
            @click="handleClip"
          >
            过程裁剪
          </AButton>
          <UserPermissions
            v-else
            permission="project.stage.cropping"
          >
            <AButton
              type="primary"
              danger
              :icon="h(ScissorOutlined)"
              @click="handleClip"
            >
              过程裁剪
            </AButton>
          </UserPermissions>
        </ASpace>
      </template>
      <AAlert
        v-if="stageAuditDetail?.status === 1"
        closable
        class="w-[400px]"
        type="warning"
      >
        <template #message>
          <div class="flex items-center">
            <span>
              当前阶段存在审批中的推进，去
              <RouterLink
                class="mx-1"
                to="/approval"
                target="_blank"
              >
                审批列表
              </RouterLink>
              查看
            </span>
          </div>
        </template>
      </AAlert>
      <!-- 追加推进按钮的权限校验:这里等待详情获取完成才渲染进度组件, 当有审批中的推进，不显示推荐按钮 -->
      <ClipTreeSelect
        class="mb-6 mt-6"
        mode="check"
        :srdcloud-project-id="projectInfo?.srdcloudProjectId"
        :programme-id="route.params.id"
        :sprint-id="sprintId"
        :step-config="{
          stage: currentStageId,
          canPush: isDev
            ? true
            : stageAuditDetail?.status !== 1 && (canIusePushBtn === null ? true : canIusePushBtn),
        }"
        @get-agile-stage-data="getSteps"
        @push="handleAdvance"
      />
    </CardPane>
    <!-- 智能研发指引 -->
    <CardPane
      title="智能研发指引"
      class="mt-20px"
    >
      <div :class="isSmallScreen ? 'flex flex-col gap-6' : 'flex gap-10'">
        <GuideStatus
          :class="isSmallScreen ? 'w-full' : 'w-[60%]'"
          :ai-usage-counts="projectInfo?.aiUsageCounts"
        />

        <div :class="isSmallScreen ? 'w-full' : 'w-[40%]'">
          <div
            v-for="item in guideText"
            :key="item.title"
            class="mb-2"
          >
            <TitleCard0 :title="item.title">
              <ul class="list-disc text-[var(--zion-text-base1)]">
                <li
                  v-for="desc in item.descs"
                  :key="desc"
                  class="leading-[23px]"
                >
                  {{ desc }}
                </li>
              </ul>
            </TitleCard0>
          </div>
        </div>
      </div>
    </CardPane>
    <ClipConfigModal
      v-if="clipModalVisible"
      v-model:visible="clipModalVisible"
      :programme-id="programmeId"
      @ok="handleClipOk"
    />
    <AdvanceModal
      v-model:visible="advanceModalVisible"
      :current-stage-config="currentStageConfig"
      :next-stage-config="nextStageConfig"
      :programme-id="programmeId"
      :sprint-id="currentSprint"
      :developers="projectInfo?.stageApprovers as any[]"
      @success="handleAdvanceSuccess"
    />
  </div>
</template>

<style scoped></style>
