<script setup lang="ts">
  import EditorPlus from '../../../../editor-plus/index.vue';

  const llm_url = import.meta.env.VITE_LLM_URL as string;
  const llm_key = import.meta.env.VITE_LLM_KEY as string;
  const llm_model = import.meta.env.VITE_LLM_MODEL as string;
  let tempList = [
    {
      id: '1',
      name: '需求规格说明书',
      content: '',
    },
    {
      id: '2',
      name: '架构设计内容',
      content: '',
    },
  ];
  const editorRef = ref<any>(null);

  setTimeout(() => {
    if (editorRef.value) {
      // console.log(editorRef.value.getEditorContent());
      console.log(editorRef.value);
    }
  }, 3000);
</script>

<template>
  <div>
    <EditorPlus
      ref="editorRef"
      :llm_url="llm_url"
      :llm_key="llm_key"
      :llm_model="llm_model"
      :temp-list="tempList"
    />
  </div>
</template>

<style scoped></style>
