<script setup lang="tsx">
  import type { FormInstance, TableColumnsType } from 'ant-design-vue';
  import { reactive, ref } from 'vue';
  import type { ComponentExposed } from 'vue-component-type-helpers';

  import type { ListResponseItem } from '@/apis/code-generate/approval';
  import Api, {
    APPROVE_STATUS,
    ApproveStatus,
    ApproveStatusDotColors,
    ApproveStatusList,
    ApproveTypeColors,
    ApproveTypeList,
    ApproveTypes,
  } from '@/apis/code-generate/approval';
  import ServerPagination from '@/components/ServerPagination/index.vue';
  import TableList from '@/components/TableList/index.vue';

  import ConfigApproveAction from './components/ConfigApproveAction.vue';
  import ConfigDetailAction from './components/ConfigDetailAction.vue';

  const formRef = ref<FormInstance>();
  const formState = reactive<{
    pageIndex?: number;
    pageSize?: number;
    bizName?: string; // 审批名称
    approvalState?: string; // 审批状态
    bizType?: string; // 审批业务类型
  }>({
    pageIndex: 1,
    pageSize: 10,
    bizName: undefined,
    approvalState: undefined,
    bizType: undefined,
  });

  const serverPaginationRef = ref<ComponentExposed<typeof ServerPagination>>();
  const handleQuery = (reset?: boolean) => {
    if (reset) {
      formRef.value?.resetFields();
    }

    serverPaginationRef.value?.fetchDataButResetPage();
  };
  const handleRefresh = () => {
    serverPaginationRef.value?.fetchData();
  };

  const listLoading = ref(false);
  const list = ref<ListResponseItem[]>();
  const columns: TableColumnsType<ListResponseItem> = [
    {
      title: '审批业务名称',
      dataIndex: 'bizName',
      key: 'bizName',
      ellipsis: true,
    },
    {
      title: '审批业务类型',
      dataIndex: 'bizType',
      key: 'bizType',
      ellipsis: true,
    },
    {
      title: '关联项目',
      dataIndex: 'bizParam',
      key: 'bizParam',
      ellipsis: true,
    },
    {
      title: '申请人',
      dataIndex: 'submitterName',
      key: 'submitterName',
      ellipsis: true,
    },
    {
      title: '申请时间',
      dataIndex: 'createTime',
      key: 'createTime',
      ellipsis: true,
      width: 180,
      customRender: ({ record }) => <div>{record.createTime || '--'}</div>,
    },
    {
      title: '处理时间',
      dataIndex: 'approvalTime',
      key: 'approvalTime',
      align: 'center',
      width: 180,
      customRender: ({ record }) => <div>{record.approvalTime || '--'}</div>,
    },
    {
      title: '审批状态',
      dataIndex: 'approvalState',
      key: 'approvalState',
      align: 'center',
      width: 180,
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
    },
  ];
  const safeParse = (bizParams: string) => {
    try {
      return JSON.parse(bizParams);
    } catch (error) {
      return {};
    }
  };
</script>

<template>
  <div class="content-box h-full">
    <TableList
      :filter-form-state="formState"
      :loading="listLoading"
      @query="serverPaginationRef?.fetchDataButResetPage"
    >
      <template #filterForm>
        <AFormItem
          label="审批业务名称"
          name="bizName"
        >
          <AInput
            v-model:value.trim="formState.bizName"
            placeholder="请输入名称"
            :disabled="listLoading"
          />
        </AFormItem>
        <AFormItem
          label="审批业务类型"
          name="bizType"
        >
          <ASelect
            v-model:value="formState.bizType"
            placeholder="请选择类型"
            :disabled="listLoading"
            allow-clear
          >
            <ASelectOption
              v-for="item in ApproveTypeList"
              :key="item.value"
              :value="item.value"
            >
              {{ item.label }}
            </ASelectOption>
          </ASelect>
        </AFormItem>

        <AFormItem
          label="审批状态"
          name="approvalState"
        >
          <ASelect
            v-model:value="formState.approvalState"
            placeholder="请选择状态"
            :disabled="listLoading"
            allow-clear
          >
            <ASelectOption
              v-for="item in ApproveStatusList"
              :key="item.value"
              :value="item.value"
            >
              {{ item.label }}
            </ASelectOption>
          </ASelect>
        </AFormItem>
      </template>
      <template #tableActions></template>
      <template #table>
        <ATable
          row-key="id"
          :data-source="list"
          :columns="columns"
          :pagination="false"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'approvalState'">
              <div class="w-full flex items-center justify-start pl-40px">
                <span
                  class="dot mr-7px h-9px w-9px rounded-50%"
                  :style="{ backgroundColor: ApproveStatusDotColors[record.approvalState] }"
                ></span>
                <span class="">{{ ApproveStatus[record.approvalState] || '--' }}</span>
              </div>
            </template>
            <template v-if="column.key === 'bizType'">
              <ATag
                v-if="record.bizType"
                :color="ApproveTypeColors[record.bizType] || ''"
                class="mr-0 cursor-pointer"
              >
                {{ ApproveTypes[record.bizType] || '--' }}
              </ATag>
            </template>
            <template v-else-if="column.key === 'actions'">
              <!-- <ADropdown :trigger="['hover']">
                <a
                  class="ant-dropdown-link"
                  @click.prevent
                >
                  <MoreOutlined />
                </a>
                <template #overlay>
                  <AMenu> -->
              <ConfigDetailAction :record="record" />
              <ConfigApproveAction
                v-if="record.hasPermission"
                action-type="approve"
                :record="record"
                :disabled="
                  [APPROVE_STATUS.PASS, APPROVE_STATUS.REJECT].includes(record.approvalState)
                "
                @data-change="handleRefresh"
              />
              <!-- </AMenu>
                </template>
</ADropdown> -->
            </template>
            <template v-if="column.key === 'bizParam'">
              <RouterLink
                v-if="safeParse(record.bizParam)?.programmeName"
                target="_blank"
                :to="`/project/detail/${safeParse(record.bizParam)?.programmeId}/check`"
              >
                {{ safeParse(record.bizParam)?.programmeName || '--' }}
              </RouterLink>
              <span v-else>--</span>
            </template>
          </template>
        </ATable>
      </template>
      <template #pagination>
        <ServerPagination
          ref="serverPaginationRef"
          :request="
            ({ pageIndex, pageSize }) =>
              Api.list({
                ...formState,
                pageIndex,
                pageSize,
              })
          "
          @loading-change="(val) => (listLoading = val)"
          @list-change="(val) => (list = val)"
        />
      </template>
    </TableList>
  </div>
</template>
