<script setup lang="tsx">
  import { computed } from 'vue';

  import type { ListResponseItem } from '@/apis/code-generate/approval';
  import { APPROVE_STATUS, APPROVE_TYPE } from '@/apis/code-generate/approval';

  const props = defineProps<{
    actionType: 'approve'; // 审批
    record?: ListResponseItem;
    disabled?: boolean;
  }>();

  const isApprove = computed(
    () =>
      props.actionType === 'approve' &&
      props.record?.approvalState !== APPROVE_STATUS.REJECT &&
      props.record?.approvalState !== APPROVE_STATUS.PASS,
  );

  const router = useRouter();
  const changeRouterToApprove = () => {
    const { id, bizId, bizType, bizParam } = props.record as ListResponseItem;
    const queryParams = { bizId, bizType };
    const programmeId = bizParam ? JSON.parse(bizParam).programmeId : ''; // 直接提取项目id
    const srdcloudProjectId = bizParam ? JSON.parse(bizParam).srdcloudProjectId : ''; // 直接提取srdcloud项目id
    switch (bizType) {
      case APPROVE_TYPE.RESOURCE:
        router.push({
          path: `/resource/approve/${id}`,
          query: queryParams,
        });
        break;
      case APPROVE_TYPE.PROCESS:
        router.push({
          path: '/project/advance/audit',
          query: { bizId, pageType: 'approve', id, programmeId, srdcloudProjectId },
        });
        break;
      case APPROVE_TYPE.CROPPING:
        router.push({
          path: '/project/cropping/audit',
          query: { bizId, id, pageType: 'approve' },
        });
        break;
      default:
        // router.push('/resource');
        break;
    }
  };
</script>

<template>
  <AButton
    v-if="isApprove"
    key="approve"
    class="link-small"
    type="link"
    :disabled="props.disabled"
    @click="changeRouterToApprove"
  >
    审批
  </AButton>
</template>
