<script setup lang="tsx">
  import type { ListResponseItem } from '@/apis/code-generate/approval';
  import { APPROVE_TYPE } from '@/apis/code-generate/approval';

  const props = defineProps<{
    record?: ListResponseItem;
    disabled?: boolean;
  }>();

  const router = useRouter();
  const changeRouterToDetail = () => {
    const { id, bizId, bizType, bizParam } = props.record as ListResponseItem;
    const queryParams = { bizId, bizType };
    const programmeId = bizParam ? JSON.parse(bizParam).programmeId : ''; // 直接提取项目id
    const srdcloudProjectId = bizParam ? JSON.parse(bizParam).srdcloudProjectId : ''; // 直接提取srdcloud项目id
    switch (bizType) {
      case APPROVE_TYPE.RESOURCE:
        router.push({
          path: `/resource/approve/${id}`,
          query: { ...queryParams, bizType: 'detail' },
        });
        break;
      case APPROVE_TYPE.PROCESS:
        router.push({
          path: '/project/advance/audit',
          query: { bizId, pageType: 'detail', programmeId, srdcloudProjectId },
        });
        break;
      case APPROVE_TYPE.CROPPING:
        router.push({
          path: '/project/cropping/audit',
          query: { bizId, id, pageType: 'detail' },
        });
        break;
      default:
        // router.push('/resource');
        break;
    }
  };
</script>

<template>
  <AButton
    key="detail"
    type="link"
    class="link-small"
    :disabled="props.disabled"
    @click="changeRouterToDetail"
  >
    详情
  </AButton>
</template>
