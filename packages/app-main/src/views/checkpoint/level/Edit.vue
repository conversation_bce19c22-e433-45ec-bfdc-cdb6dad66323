<script setup lang="ts">
  import { LeftCircleOutlined } from '@ant-design/icons-vue';
  import { message } from 'ant-design-vue';
  import type { FormInstance, Rule } from 'ant-design-vue/es/form';
  import { ref } from 'vue';
  import { useRouter } from 'vue-router';

  import {
    addAgileLevel,
    type AgileLevel,
    getAgileLevelDetail,
    updateAgileLevel,
  } from '@/apis/checkpoint/agileLevel';

  const router = useRouter();
  const route = useRoute();
  const defaultFormState: AgileLevel = {
    levelName: '',
    levelCode: '',
    levelDesc: '',
    levelRuleIds: '',
    levelStatus: 1,
  };

  const formState = ref<AgileLevel>({
    ...defaultFormState,
  });
  const ruleIds = computed({
    get: () => formState.value.levelRuleIds.split(','),
    set: (value: string[]) => {
      formState.value.levelRuleIds = value.filter((item) => item).join(',');
    },
  });
  // 表单验证规则
  const formRules: Record<string, Rule[]> = {
    levelName: [{ required: true, message: '请输入级别名称', trigger: 'blur' }],
    levelCode: [{ required: true, message: '请输入级别编码', trigger: 'blur' }],
    levelRuleIds: [{ required: true, message: '请选择管控卡点规则', trigger: 'change' }],
  };

  // 返回列表页
  const handleBack = () => {
    router.back();
  };

  const isDetail = computed(() => {
    return route.path.includes('/level/detail');
  });
  const isEdit = computed(() => {
    return route.path.includes('/level/edit');
  });
  const id = computed(() => {
    return route.params.id as string;
  });
  if (id.value) {
    getAgileLevelDetail(id.value).then((res) => {
      formState.value = res.data.data || { ...defaultFormState };
    });
  }
  const formRef = useTemplateRef<FormInstance>('formRef');
  const submitLoading = ref(false);
  // 保存表单
  const handleSubmit = () => {
    formRef.value?.validate().then(() => {
      const requestFn = isEdit.value ? updateAgileLevel : addAgileLevel; // 根据是否编辑选择不同的API函数
      submitLoading.value = true;
      requestFn({ ...formState.value })
        .then((res) => {
          console.log(res);
          message.success('提交成功');
          setTimeout(() => {
            handleBack(); // 提交成功后返回列表页
          }, 1000);
        })
        .finally(() => {
          submitLoading.value = false;
        });
    });
  };
</script>

<template>
  <ACard class="min-h-full">
    <template #title>
      <div
        class="cursor-pointer text-16px font-normal text-#565865"
        @click="handleBack"
      >
        <LeftCircleOutlined style="color: #d8d8d8; margin-right: 8px" />
        返回
      </div>
    </template>
    <div class="mx-auto mt-4 max-w-1300px">
      <AForm
        ref="formRef"
        :model="formState"
        :rules="formRules"
        :label-col="{ span: 4 }"
        :disabled="isDetail"
      >
        <ARow :gutter="120">
          <ACol :span="12">
            <AFormItem
              label="级别名称"
              name="levelName"
            >
              <AInput
                v-model:value.trim="formState.levelName"
                placeholder="请输入级别名称"
                :maxlength="20"
              />
            </AFormItem>
          </ACol>
          <ACol :span="12">
            <AFormItem
              label="级别编码"
              name="levelCode"
            >
              <AInput
                v-model:value.trim="formState.levelCode"
                placeholder="请输入级别编码"
                :maxlength="20"
              />
            </AFormItem>
          </ACol>
        </ARow>

        <ARow :gutter="120">
          <ACol :span="12">
            <AFormItem
              label="启用状态"
              name="levelStatus"
            >
              <ARadioGroup v-model:value="formState.levelStatus">
                <ARadio :value="1">启用</ARadio>
                <ARadio :value="0">禁用</ARadio>
              </ARadioGroup>
            </AFormItem>
          </ACol>
          <ACol :span="12">
            <AFormItem
              label="描述"
              name="levelDesc"
            >
              <ATextarea
                v-model:value.trim="formState.levelDesc"
                placeholder="请输入级别描述"
                :maxlength="200"
              />
            </AFormItem>
          </ACol>
        </ARow>

        <AFormItem
          label="管控卡点规则集"
          name="levelRuleIds"
          :label-col="{ span: 24 }"
          :wrapper-col="{ span: 24 }"
        >
          <!-- item-width自适应？？ -->
          <ClipTreeSelect v-model:value="ruleIds" mode="edit" :item-width="190"
            :tree-config="{ checkable: !isDetail }" />
        </AFormItem>

        <ASpace
          v-if="!isDetail"
          class="item-center flex justify-center"
        >
          <AButton @click="handleBack">取消</AButton>
          <AButton
            type="primary"
            :loading="submitLoading"
            @click="handleSubmit"
          >
            保存
          </AButton>
        </ASpace>
      </AForm>
    </div>
  </ACard>
</template>
