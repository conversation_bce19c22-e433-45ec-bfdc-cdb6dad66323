<script setup lang="tsx">
  import { message, type TableColumnsType } from 'ant-design-vue';
  import { onMounted, reactive, ref } from 'vue';
  import type { ComponentExposed } from 'vue-component-type-helpers';
  import { useRouter } from 'vue-router';

  import {
    type AgileLevel,
    deleteAgileLevel,
    getAgileLevelPageList,
    updateAgileLevel,
  } from '@/apis/checkpoint/agileLevel';
  import ServerPagination from '@/components/ServerPagination/index.vue';
  import TableList from '@/components/TableList/index.vue';
  import { getLevelColor } from '@/dicts/project';

  const router = useRouter();

  const formState = reactive<Partial<AgileLevel>>({
    levelName: '',
    levelCode: '',
    levelDesc: '',
    levelRuleIds: '',
    levelStatus: 1,
  });
  const serverPaginationRef = ref<ComponentExposed<typeof ServerPagination>>();

  const handleRefresh = () => {
    serverPaginationRef.value?.fetchData();
  };
  onMounted(handleRefresh);

  const listLoading = ref(false);
  const list = ref<AgileLevel[]>([]);
  const columns: TableColumnsType = [
    {
      title: '序号',
      dataIndex: 'index',
      width: 100,
      customRender: ({ index }) => index + 1,
    },
    {
      title: '管控级别',
      dataIndex: 'levelName',
      width: 120,
      ellipsis: true,
      customRender: ({ record }) => (
        <s-tag
          color={getLevelColor(record.levelName).color}
          bgColor={getLevelColor(record.levelName).bgColor}
          title={record.levelName}
        >
          {record.levelName}
        </s-tag>
      ),
    },
    {
      title: '管控级别Code',
      dataIndex: 'levelCode',
      width: 200,
      ellipsis: true,
    },
    {
      title: '描述',
      dataIndex: 'levelDesc',
      ellipsis: true,
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      width: 220,
    },
    {
      title: '规则集',
      dataIndex: 'levelRuleIds',
      ellipsis: true,
      customRender: ({ record }) => <span>{record.levelRuleNames.join(', ')}</span>,
    },
    {
      title: '状态',
      dataIndex: 'levelStatus',
      width: 120,
      customRender: ({ record }) => {
        return record.levelStatus === 1 ? (
          <a-badge
            color="#2db7f5"
            text="启用"
          />
        ) : (
          <a-badge
            color="#E94C4C"
            text="禁用"
          />
        );
      },
    },
    {
      title: '操作',
      key: 'actions',
      align: 'center',
      width: 220,
    },
  ];

  const handleAdd = () => {
    router.push('/checkpoint/level/add');
  };

  const handleEdit = (record: AgileLevel) => {
    router.push(`/checkpoint/level/edit/${record.id}`);
  };
  const handleChangeStatus = (record: AgileLevel) => {
    const targetStatus = record.levelStatus === 1 ? 0 : 1;
    updateAgileLevel({
      ...record,
      id: record.id,
      levelStatus: targetStatus,
    }).then(() => {
      message.success(`操作成功`);
      handleRefresh();
    });
  };
  const handleDetail = (record: AgileLevel) => {
    router.push('/checkpoint/level/detail/' + record.id);
  };
  const handleDelete = (record: AgileLevel) => {
    record.id &&
      deleteAgileLevel(record.id).then(() => {
        message.success(`操作成功`);
        handleRefresh();
      });
  };
  const handleReset = () => {
    formState.levelName = '';
  };
</script>

<template>
  <div class="content-box h-full">
    <TableList
      :filter-form-state="formState"
      :loading="listLoading"
      @query="serverPaginationRef?.fetchDataButResetPage"
      @reset="handleReset"
    >
      <template #filterForm>
        <AFormItem
          label="管控级别"
          name="name"
        >
          <AInput
            v-model:value.trim="formState.levelName"
            :disabled="listLoading"
            placeholder="请输入管控级别名称"
          />
        </AFormItem>
      </template>
      <template #tableActions>
        <AButton @click="handleAdd">
          <PlusOutlined />
          新增
        </AButton>
      </template>
      <template #table>
        <ATable
          row-key="id"
          :data-source="list"
          :columns="columns"
          :pagination="false"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'actions'">
              <AButton
                type="link"
                size="small"
                @click="handleDetail(record as AgileLevel)"
              >
                查看
              </AButton>
              <AButton
                type="link"
                size="small"
                @click="handleChangeStatus(record as AgileLevel)"
              >
                {{ record.levelStatus === 1 ? '禁用' : '启用' }}
              </AButton>
              <AButton
                type="link"
                size="small"
                @click="handleEdit(record as AgileLevel)"
              >
                编辑
              </AButton>
              <APopconfirm
                title="确定删除该数据吗？"
                ok-text="确定"
                cancel-text="取消"
                @confirm="handleDelete(record as AgileLevel)"
              >
                <AButton
                  type="link"
                  size="small"
                  class="text-#ff7064"
                >
                  删除
                </AButton>
              </APopconfirm>
            </template>
          </template>
        </ATable>
      </template>
      <template #pagination>
        <ServerPagination
          ref="serverPaginationRef"
          :request="
            ({ pageIndex, pageSize }) =>
              getAgileLevelPageList({
                pageIndex,
                pageSize,
                levelName: formState.levelName || undefined,
              })
          "
          :immediate="false"
          @loading-change="(val) => (listLoading = val)"
          @list-change="(val) => (list = val)"
        />
      </template>
    </TableList>
  </div>
</template>
