<script setup lang="ts">
  import { LeftCircleOutlined } from '@ant-design/icons-vue';
  import { message } from 'ant-design-vue';
  import type { FormInstance, Rule } from 'ant-design-vue/es/form';
  import { cloneDeep } from 'lodash-es';
  import { ref } from 'vue';
  import { useRouter } from 'vue-router';
  import { ColorPicker } from 'vue3-colorpicker';

  import {
    addAgileStage,
    type AgileStage,
    type CheckRuleGroup,
    type CheckRuleItem,
    getAgileStageDetail,
    type GuideInfo,
    updateAgileStage,
  } from '@/apis/checkpoint/agileStage';

  import CheckPointRuleSetter from './components/CheckPointRuleSetter.vue';

  import 'vue3-colorpicker/style.css';

  const router = useRouter();
  const route = useRoute();
  interface AgileStageObject extends Omit<AgileStage, 'agileStageItemDTOList'> {
    agileStageItemDTOList: CheckRuleItem[];
  }
  const defaultFormState: AgileStageObject = {
    agileStageItemDTOList: [
      // {
      //   itemGuideInfo: [
      //     {
      //       btn_name: '',
      //       btn_url: '',
      //     },
      //   ], // 检查项快速指引JSON数据，格式如GuideInfo
      //   itemName: '',
      //   itemOrder: 1,
      //   itemRules: [],
      //   itemRulesDesc: '',
      // },
    ],
    stageName: '',
    stageCode: '',
    // checkItemIds: '',
    stageDesc: '',
    stageOrder: 0,
    stageStatus: 1,
    stageColor: '#4cb050',
    stageIcon: '',
  };

  const formState = ref<AgileStageObject>(cloneDeep(defaultFormState));
  // 表单验证规则
  const formRules: Record<string, Rule[]> = {
    stageName: [{ required: true, message: '请输入阶段名称', trigger: 'blur' }],
    stageCode: [{ required: true, message: '请输入阶段编码', trigger: 'blur' }],
    // checkItemIds: [{ required: true, message: '请选择检查项', trigger: 'change' }],
  };

  // 返回列表页
  const handleBack = () => {
    router.back();
  };

  const isDetail = computed(() => {
    return route.path.includes('/detail');
  });
  const isEdit = computed(() => {
    return route.path.includes('/edit');
  });
  const id = computed(() => {
    return route.params.id as string;
  });
  if (id.value) {
    getAgileStageDetail(id.value).then((res) => {
      if (res.data.data) {
        const list = res.data?.data || {};

        formState.value = {
          ...list,
          agileStageItemDTOList: list.agileStageItemVOList?.map((item: AgileStageItem) => {
            if (item.itemGuideInfo) {
              item.itemGuideInfo = JSON.parse(item.itemGuideInfo || '[]') as GuideInfo[];
              if (item.itemGuideInfo?.length === 0) {
                item.itemGuideInfo = [
                  {
                    btn_name: '',
                    btn_url: '',
                  },
                ];
              }
              if (item.itemRules) {
                item.itemRules = JSON.parse(item.itemRules || '[]') as CheckRuleGroup[];
              }
              return item;
            }
          }),
        };
      }
    });
  }
  const formRef = useTemplateRef<FormInstance>('formRef');
  const submitLoading = ref(false);
  // 保存表单
  const handleSubmit = () => {
    formRef.value?.validate().then(() => {
      const requestFn = isEdit.value ? updateAgileStage : addAgileStage; // 根据是否编辑选择不同的API函数
      submitLoading.value = true;
      const formData = cloneDeep(formState.value);
      // 校验
      const errorList: string[] = [];
      formData.agileStageItemDTOList.forEach((item, index) => {
        if (!item.itemName) {
          errorList.push(`第 ${index + 1} 个检查项的名称不能为空。`);
        }
        if (item.itemRules.length) {
          item.itemRules.forEach((rule, ruleIndex) => {
            rule.rules.some((rule) => {
              if (!rule.ruleCategory || !rule.ruleLogicValue || !rule.ruleLogic) {
                errorList.push(
                  `第 ${index + 1} 个检查项的第 ${ruleIndex + 1} 个检查规则配置不完整。`,
                );
              }
            });
          });
        }
        item.itemGuideInfo = item.itemGuideInfo.filter((guide) => guide.btn_name || guide.btn_url);
        item.itemGuideInfo.forEach((guide) => {
          if (!guide.btn_name || !guide.btn_url) {
            errorList.push(`第 ${index + 1} 个检查项的指引按钮配置不完整，名称和url都必须配置。`);
          }
        });
      });
      if (errorList.length) {
        message.error([...new Set(errorList)].join('\n'));
        submitLoading.value = false;
        return;
      }
      const agileStageItemDTOList = formData.agileStageItemDTOList?.map((item) => ({
        ...item,
        stageId: isEdit.value ? formData.id : undefined,
        itemGuideInfo: JSON.stringify(item.itemGuideInfo),
        itemRules: JSON.stringify(item.itemRules),
      }));

      // const checkItemIds = agileStageItemDTOList.map((item) => item.id).join(',');
      requestFn({
        ...formData,
        agileStageItemDTOList,
        agileStageItemVOList: undefined,
        checkItemIds: undefined,
      })
        .then((res) => {
          console.log(res);
          message.success('提交成功');
          setTimeout(() => {
            handleBack(); // 提交成功后返回列表页
          }, 1000);
        })
        .finally(() => {
          submitLoading.value = false;
        });
    });
  };
</script>

<template>
  <ACard class="min-h-full">
    <template #title>
      <div
        class="cursor-pointer text-16px font-normal text-#565865"
        @click="handleBack"
      >
        <LeftCircleOutlined style="color: #d8d8d8; margin-right: 8px" />
        返回
      </div>
    </template>
    <div class="mx-auto mt-4 max-w-1000px">
      <AForm
        ref="formRef"
        :model="formState"
        :rules="formRules"
        :label-col="{ span: 5 }"
        :disabled="isDetail"
      >
        <ARow :gutter="120">
          <ACol :span="12">
            <AFormItem
              label="阶段名称"
              name="stageName"
            >
              <AInput
                v-model:value.trim="formState.stageName"
                placeholder="请输入阶段名称"
                :maxlength="20"
              />
            </AFormItem>
          </ACol>
          <ACol :span="12">
            <AFormItem
              label="编码"
              name="stageCode"
            >
              <AInput
                v-model:value.trim="formState.stageCode"
                placeholder="请输入阶段编码"
                :maxlength="20"
              />
            </AFormItem>
          </ACol>
        </ARow>
        <ARow :gutter="120">
          <!-- <ACol :span="12">
            <AFormItem
              label="阶段图标"
              name="stageIcon"
            >
              <AntIconSimpleSelect
                v-model:value="formState.stageIcon"
                :disabled="isDetail"
              />
            </AFormItem>
          </ACol> -->
          <ACol :span="12">
            <AFormItem
              label="排序"
              name="stageOrder"
            >
              <AInputNumber
                v-model:value="formState.stageOrder"
                class="w-full"
                placeholder="请输入排序"
                :min="0"
                :max="9999"
                :maxlength="4"
              />
            </AFormItem>
          </ACol>
          <ACol :span="12">
            <AFormItem
              label="启用状态"
              name="stageStatus"
            >
              <ARadioGroup v-model:value="formState.stageStatus">
                <ARadio :value="1">启用</ARadio>
                <ARadio :value="0">禁用</ARadio>
              </ARadioGroup>
            </AFormItem>
          </ACol>
        </ARow>

        <ARow :gutter="120">
          <ACol :span="12">
            <AFormItem
              label="阶段颜色"
              name="stageColor"
            >
              <ColorPicker
                v-model:pure-color="formState.stageColor"
                use-type="pure"
                format="hex"
                picker-type="chrome"
              />
            </AFormItem>
          </ACol>
          <ACol :span="12">
            <AFormItem
              label="描述"
              name="stageDesc"
            >
              <ATextarea
                v-model:value.trim="formState.stageDesc"
                placeholder="请输入阶段描述"
                :maxlength="200"
              />
            </AFormItem>
          </ACol>
        </ARow>

        <AFormItem
          label="检查项配置"
          name="stageRuleIds"
          :label-col="{ span: 3 }"
          :wrapper-col="{ span: 21 }"
        >
          <CheckPointRuleSetter
            v-model="formState.agileStageItemDTOList"
            :disabled="isDetail"
          />
        </AFormItem>

        <ASpace
          v-if="!isDetail"
          class="item-center flex justify-center"
        >
          <AButton @click="handleBack">取消</AButton>
          <AButton
            type="primary"
            :loading="submitLoading"
            @click="handleSubmit"
          >
            保存
          </AButton>
        </ASpace>
      </AForm>
    </div>
  </ACard>
</template>
