<script setup lang="ts">
  import { MinusCircleOutlined } from '@ant-design/icons-vue';
import { cloneDeep } from 'lodash-es';
import { ref } from 'vue';

  import {
  type CheckRuleItem,
  type CheckRulesOptions,
  getCheckRules,
} from '@/apis/checkpoint/agileStage';

  // import { getUUID } from '@/utils';
  import CheckRulesSetter from './CheckRulesSetter.vue';
import GuideInfoSetter from './GuideInfoSetter.vue';

  const props = withDefaults(
    defineProps<{
      modelValue: CheckRuleItem[];
      disabled?: boolean;
    }>(),
    {
      modelValue: () => [],
      disabled: false,
    },
  );
  const emit = defineEmits<{
    (e: 'update:modelValue', value: CheckRuleItem[]): void;
  }>();

  const checkRulesOptions = ref<CheckRulesOptions>({
    ruleCategoryVOList: [],
    ruleLogicConnectionList: [],
    ruleLogicVOList: [],
  });
  provide('checkRulesOptions', checkRulesOptions);
  const spinning = ref(false);
  // 获取下拉选项
  getCheckRules()
    .then((res) => {
      checkRulesOptions.value = res.data?.data || {
        ruleCategoryVOList: [],
        ruleLogicConnectionList: [],
        ruleLogicVOList: [],
      };
    })
    .finally(() => {
      spinning.value = false;
    });

  const defaultIteItem: CheckRuleItem = {
    // id: getUUID(),
    itemGuideInfo: [
      {
        btn_name: '',
        btn_url: '',
      },
    ], // 检查项快速指引JSON数据，格式如GuideInfo
    itemName: '',
    itemOrder: 1,
    itemRules: [],
    itemRulesDesc: '',
    isSprintItem: false,
  };

  const formState = computed<CheckRuleItem[]>({
    get: () => {
      return props.modelValue ? props.modelValue : [];
    },
    set: (value: CheckRuleItem[]) => {
      emit('update:modelValue', value);
    },
  });

  const handleAddCheckItem = () => {
    formState.value.push({
      ...cloneDeep(defaultIteItem),
      itemOrder: formState.value.length + 1,
      // id: getUUID(),
    });
    activeKey.value = [...activeKey.value, formState.value.length - 1];
  };
  const handleRemoveCheckItem = (index: number) => {
    formState.value.splice(index, 1);
  };
  const activeKey = ref<number[]>([0]);
</script>

<template>
  <ASpin
    v-if="spinning"
    :spinning="true"
    class="h-400 w-full"
  ></ASpin>
  <template v-else>
    <ACollapse
      v-if="formState.length > 0"
      v-model:active-key="activeKey"
      :bordered="false"
      ghost
      class="custom-collapse"
    >
      <ACollapsePanel
        v-for="(item, index) in formState"
        :key="index"
        :header="item.itemName || '检查项名称'"
        style="
          background: #fff;
          border-radius: 6px;
          margin-top: 16px;
          border: 1px solid #e8e8e8;
          overflow: hidden;
        "
      >
        <template #extra>
          <MinusCircleOutlined
            v-if="!disabled"
            style="font-size: 16px; color: #ff7064; cursor: pointer"
            @click="handleRemoveCheckItem(index)"
          />
        </template>
        <div class="px-5 py-2">
          <AForm
            :model="item"
            :label-col="{ span: 4 }"
            :wrapper-col="{ span: 20 }"
            label-align="right"
            required
          >
            <AFormItem label="检查项名称">
              <AInput
                v-model:value="item.itemName"
                placeholder="请输入检查项名称"
                :maxlength="20"
              />
            </AFormItem>
            <AFormItem label="是否为迭代检查项">
              <ARadioGroup
                v-model:value="item.isSprintItem"
                :disabled="disabled"
              >
                <ARadio :value="1">是</ARadio>
                <ARadio :value="0">否</ARadio>
              </ARadioGroup>
            </AFormItem>
            <AFormItem label="排序">
              <AInputNumber
                v-model:value="item.itemOrder"
                style="width: 100%"
                :min="0"
                :max="9999"
                :maxlength="4"
              />
            </AFormItem>
            <AFormItem label="指引按钮">
              <GuideInfoSetter
                v-model="item.itemGuideInfo"
                :disabled="disabled"
              />
            </AFormItem>
            <AFormItem label="检查规则描述">
              <AInput
                v-model:value="item.itemRulesDesc"
                placeholder="请输入检查规则描述"
                :maxlength="200"
              />
            </AFormItem>
            <AFormItem
              label="检查规则"
              class="mb-0"
            >
              <CheckRulesSetter
                v-model="item.itemRules"
                :disabled="disabled"
              />
            </AFormItem>
          </AForm>
        </div>
      </ACollapsePanel>
    </ACollapse>
    <AButton
      v-if="!disabled"
      type="dashed"
      class="mt-4 bg-#fafafa text-#336cf6"
      block
      size="large"
      @click="handleAddCheckItem"
    >
      <template #icon><PlusCircleOutlined /></template>
      添加检查项
    </AButton>
  </template>
</template>

<style scoped lang="less">
  .custom-collapse {
    :deep(.ant-collapse-header) {
      background: #fafafa;
      border-bottom: 1px solid #e8e8e8;
    }
  }
</style>
