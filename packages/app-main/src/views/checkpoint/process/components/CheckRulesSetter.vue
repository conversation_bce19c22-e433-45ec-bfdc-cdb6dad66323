<script setup lang="ts">
  import { MinusCircleOutlined, PlusCircleOutlined } from '@ant-design/icons-vue';

  import { type CheckRuleGroup, type CheckRulesOptions } from '@/apis/checkpoint/agileStage';
import { getUUID } from '@/utils';

  const { disabled = false } = defineProps<{
    disabled?: boolean;
  }>();
  const checkRulesOptions = inject<Ref<CheckRulesOptions>>(
    'checkRulesOptions',
    ref<CheckRulesOptions>({
      ruleCategoryVOList: [],
      ruleLogicConnectionList: [],
      ruleLogicVOList: [],
    }),
  );
  const ruleGroups = defineModel<CheckRuleGroup[]>('modelValue', { default: [] });
  const getGroupDefault = () =>
    [
      {
        group: getUUID(),
        rules: [
          {
            ruleCategory: '',
            ruleLogic: '==',
            ruleLogicValue: '',
            ruleConnection: undefined,
          },
        ],
        ruleConnection: undefined,
      },
    ] as CheckRuleGroup[];
  // if (ruleGroups.value.length === 0) {
  //   ruleGroups.value = getGroupDefault();
  // }

  const handleAddGroup = () => {
    ruleGroups.value[ruleGroups.value.length - 1] &&
      (ruleGroups.value[ruleGroups.value.length - 1].ruleConnection = '||');
    ruleGroups.value.push(getGroupDefault()[0]);
  };
  const handleRemoveGroup = (index: number) => {
    ruleGroups.value.splice(index, 1);
  };
  // Adds a new rule to a specific group
  const addRule = (group: CheckRuleGroup) => {
    group.rules[group.rules.length - 1] &&
      (group.rules[group.rules.length - 1].ruleConnection = '||');
    group.rules.push({
      ruleCategory: '',
      ruleLogic: '==',
      ruleLogicValue: '',
      ruleConnection: undefined,
    });
  };

  // Removes a rule from a specific group
  const removeRule = (group: CheckRuleGroup, index: number) => {
    group.rules.splice(index, 1);
  };
  const filterOption = (input: string, option: any) => {
    return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
  };
</script>

<template>
  <div class="check-rule-setter">
    <div
      v-for="(group, groupIndex) in ruleGroups"
      :key="groupIndex"
      class="rule-group-wrapper"
    >
      <div
        v-if="groupIndex < ruleGroups.length - 1"
        class="group-connector-wrapper"
      >
        <ASelect
          v-model:value="group.ruleConnection"
          class="!w-full"
          size="small"
          :options="checkRulesOptions.ruleLogicConnectionList"
          :disabled="disabled"
        ></ASelect>
      </div>

      <div class="rule-group">
        <CloseCircleOutlined
          v-if="!disabled"
          class="absolute cursor-pointer text-#ff7064 -right-2 -top-2"
          @click="handleRemoveGroup(groupIndex)"
        />
        <template v-if="group?.rules.length > 0">
          <div
            v-for="(rule, ruleIndex) in group.rules"
            :key="ruleIndex"
            class="rule-item-wrapper"
          >
            <div
              v-if="ruleIndex < group.rules.length - 1"
              class="group-connector-wrapper"
            >
              <ASelect
                v-model:value="rule.ruleConnection"
                class="!w-full"
                size="small"
                :options="checkRulesOptions.ruleLogicConnectionList"
                :disabled="disabled"
              ></ASelect>
            </div>

            <div class="rule-item">
              <ASelect
                v-model:value="rule.ruleCategory"
                style="width: 120px"
                :options="checkRulesOptions.ruleCategoryVOList"
                :disabled="disabled"
                show-search
                :filter-option="filterOption"
              ></ASelect>
              <ASelect
                v-model:value="rule.ruleLogic"
                style="width: 120px"
                :options="checkRulesOptions.ruleLogicVOList"
                :disabled="disabled"
              ></ASelect>
              <AInput
                v-model:value="rule.ruleLogicValue"
                placeholder="请输入"
                :disabled="disabled"
              />
              <div
                v-if="!disabled"
                class="rule-actions"
              >
                <MinusCircleOutlined
                  v-if="group.rules.length > 1"
                  class="cursor-pointer text-#ff7064"
                  @click="removeRule(group, ruleIndex)"
                />
                <PlusCircleOutlined
                  v-if="ruleIndex === group.rules.length - 1"
                  class="cursor-pointer text-#3173FF"
                  @click="addRule(group)"
                />
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>

    <AButton
      v-if="!disabled"
      type="dashed"
      class="bg-#fafafa text-#336cf6"
      block
      @click="handleAddGroup"
    >
      <template #icon><PlusCircleOutlined /></template>
      添加条件分组
    </AButton>
  </div>
</template>

<style scoped lang="less">
  @connector-line-color: #d9d9d9;
  @connector-select-bg: #ffffff;
  @connector-select-border-color: #d9d9d9;
  @rule-group-bg: #fafafa;

  .check-rule-setter {
    width: 100%;
    position: relative;
    padding-left: 80px;
  }

  .rule-group-wrapper {
    position: relative;
    margin: 12px 0;
    &::before {
      content: '';
      width: 44px;
      height: 50%;
      border: 1px solid @connector-line-color;
      border-right: none;
      border-top: none;
      position: absolute;
      transform: translateX(-100%);
      top: 0;
      margin-top: -2px;
    }
    &:first-of-type {
      &::before {
        display: none;
      }
    }
    &::after {
      content: '';
      width: 44px;
      height: 50%;
      border: 1px solid @connector-line-color;
      border-right: none;
      border-bottom: none;
      position: absolute;
      transform: translateX(-100%);
      top: 50%;
      margin-top: 2px;
    }
    &:last-of-type {
      &::after {
        display: none;
      }
    }
  }
  .group-connector-wrapper {
    position: absolute;
    left: -70px;
    top: 100%;
    width: 52px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 3;
    transform: translateY(-2px);
  }

  .rule-group {
    background-color: @rule-group-bg;
    border: 1px solid #e8e8e8;
    border-radius: 6px;
    padding: 6px 0;
    padding-left: 80px;
    padding-right: 12px;
    position: relative;
    margin-left: 0;
  }

  .rule-item-wrapper {
    position: relative;
    margin: 12px 0;
    &::before {
      content: '';
      width: 52px;
      height: 50%;
      border: 1px solid @connector-line-color;
      border-right: none;
      border-top: none;
      position: absolute;
      transform: translateX(-100%);
      top: 0;
      margin-top: -2px;
    }
    &:first-of-type {
      &::before {
        display: none;
      }
    }
    &::after {
      content: '';
      width: 52px;
      height: 50%;
      border: 1px solid @connector-line-color;
      border-right: none;
      border-bottom: none;
      position: absolute;
      transform: translateX(-100%);
      top: 50%;
      margin-top: 2px;
    }
    &:last-of-type {
      &::after {
        display: none;
      }
    }
  }

  .rule-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 4px 0;

    .ant-select {
      flex-shrink: 0;
    }

    .ant-input {
      flex: 1;
    }
  }

  .rule-actions {
    display: flex;
    align-items: center;
    gap: 4px;
    flex-shrink: 0;
  }
</style>
