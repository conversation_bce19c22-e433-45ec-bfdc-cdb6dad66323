<script setup lang="ts">
  import { MinusCircleOutlined } from '@ant-design/icons-vue';

  import type { GuideInfo } from '@/apis/checkpoint/agileStage';

  const { disabled = false } = defineProps<{
    disabled?: boolean;
  }>();
  const emit = defineEmits(['change']);
  const data = defineModel<GuideInfo[]>('modelValue', {
    default: [
      {
        btn_name: '',
        btn_url: '',
      },
    ],
  });
  // if (data.value.length === 0) {
  //   data.value.push({
  //     btn_name: '',
  //     btn_url: '',
  //   });
  // }
  function handleAddGuideInfo() {
    data.value.push({
      btn_name: '',
      btn_url: '',
    });
    emit('change', data.value);
  }
  function handleRemoveGuideInfo(index: number) {
    data.value.splice(index, 1);
    emit('change', data.value);
  }
</script>

<template>
  <div>
    <div
      v-for="(item, index) in data"
      :key="index"
      class="mb-2 flex items-center"
    >
      <AInput
        v-model:value="item.btn_name"
        placeholder="请输入按钮名称"
        class="flex-1"
        :disabled="disabled"
        :maxlength="20"
      />
      <div class="mx-4 h-1px w-5 bg-#D9D9D9"></div>
      <AInput
        v-model:value="item.btn_url"
        placeholder="请输入按钮URL"
        class="flex-1"
        :disabled="disabled"
      />
      <div
        v-if="!disabled"
        class="w-10 flex justify-center gap-1 text-4"
      >
        <MinusCircleOutlined
          v-if="data.length > 1"
          class="cursor-pointer text-#ff7064"
          @click="handleRemoveGuideInfo(index)"
        />
        <PlusCircleOutlined
          v-if="index === data.length - 1"
          class="cursor-pointer text-#3173FF"
          @click="handleAddGuideInfo"
        />
      </div>
    </div>
  </div>
</template>
