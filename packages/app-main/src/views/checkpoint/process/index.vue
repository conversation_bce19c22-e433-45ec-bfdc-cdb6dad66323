<script setup lang="tsx">
  import { message, type TableColumnsType } from 'ant-design-vue';
  import { onMounted, reactive, ref } from 'vue';
  import type { ComponentExposed } from 'vue-component-type-helpers';
  import { useRouter } from 'vue-router';

  import {
    type AgileStage,
    deleteAgileStage,
    enableAgileStage,
    getAgileStagePageList,
    updateAgileStage,
  } from '@/apis/checkpoint/agileStage';
  import ServerPagination from '@/components/ServerPagination/index.vue';
  import TableList from '@/components/TableList/index.vue';

  const router = useRouter();

  const formState = reactive<Partial<AgileStage>>({
    stageName: '',
  });
  const serverPaginationRef = ref<ComponentExposed<typeof ServerPagination>>();

  const handleRefresh = () => {
    serverPaginationRef.value?.fetchData();
  };
  onMounted(handleRefresh);

  const listLoading = ref(false);
  const list = ref<AgileStage[]>([]);
  const columns: TableColumnsType = [
    {
      title: '序号',
      dataIndex: 'index',
      width: 100,
      customRender: ({ index }) => index + 1,
    },
    {
      title: '阶段名称',
      dataIndex: 'stageName',
      width: 220,
    },
    {
      title: '描述',
      dataIndex: 'stageDesc',
      ellipsis: true,
      // customRender: ({ record }) => <span>{record.levelRuleNames.join(', ')}</span>,
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      width: 220,
    },
    {
      title: '排序',
      dataIndex: 'stageOrder',
      width: 120,
    },
    {
      title: '状态',
      dataIndex: 'stageStatus',
      width: 120,
      customRender: ({ record }) => {
        return record.stageStatus === 1 ? (
          <a-badge
            color="#2db7f5"
            text="启用"
          />
        ) : (
          <a-badge
            color="#E94C4C"
            text="禁用"
          />
        );
      },
    },
    {
      title: '操作',
      key: 'actions',
      align: 'center',
      width: 220,
    },
  ];

  const handleAdd = () => {
    router.push('/checkpoint/process/add');
  };

  const handleEdit = (record: AgileStage) => {
    router.push(`/checkpoint/process/edit/${record.id}`);
  };
  const handleChangeStatus = (record: AgileStage) => {
    const targetStatus = record.stageStatus === 1 ? 0 : 1;
    enableAgileStage(record.id as string, targetStatus).then(() => {
      message.success(`操作成功`);
      handleRefresh();
    });
  };
  const handleDetail = (record: AgileStage) => {
    router.push('/checkpoint/process/detail/' + record.id);
  };
  const handleDelete = (record: AgileStage) => {
    if (record.stageStatus === 1) {
      message.error('启用状态不能删除');
      return;
    }
    record.id &&
      deleteAgileStage(record.id).then(() => {
        message.success(`操作成功`);
        handleRefresh();
      });
  };
</script>

<template>
  <div class="content-box h-full">
    <TableList
      :filter-form-state="formState"
      :loading="listLoading"
      @query="serverPaginationRef?.fetchDataButResetPage"
    >
      <template #filterForm>
        <AFormItem
          label="阶段名称"
          name="stageName"
        >
          <AInput
            v-model:value.trim="formState.stageName"
            :disabled="listLoading"
            placeholder="请输入阶段名称"
          />
        </AFormItem>
      </template>
      <template #tableActions>
        <AButton @click="handleAdd">
          <PlusOutlined />
          新增
        </AButton>
      </template>
      <template #table>
        <ATable
          row-key="id"
          :data-source="list"
          :columns="columns"
          :pagination="false"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'actions'">
              <AButton
                type="link"
                size="small"
                @click="handleDetail(record as AgileStage)"
              >
                查看
              </AButton>
              <AButton
                type="link"
                size="small"
                @click="handleChangeStatus(record as AgileStage)"
              >
                {{ record.stageStatus === 1 ? '禁用' : '启用' }}
              </AButton>
              <AButton
                type="link"
                size="small"
                @click="handleEdit(record as AgileStage)"
              >
                编辑
              </AButton>
              <APopconfirm
                title="确定删除该数据吗？"
                ok-text="确定"
                cancel-text="取消"
                @confirm="handleDelete(record as AgileStage)"
              >
                <AButton
                  type="link"
                  size="small"
                  class="text-#ff7064"
                >
                  删除
                </AButton>
              </APopconfirm>
            </template>
          </template>
        </ATable>
      </template>
      <template #pagination>
        <ServerPagination
          ref="serverPaginationRef"
          :request="
            ({ pageIndex, pageSize }) =>
              getAgileStagePageList({
                pageIndex,
                pageSize,
                stageName: formState.stageName,
              })
          "
          :immediate="false"
          @loading-change="(val) => (listLoading = val)"
          @list-change="(val) => (list = val)"
        />
      </template>
    </TableList>
  </div>
</template>
