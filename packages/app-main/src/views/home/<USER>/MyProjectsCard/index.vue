<script setup lang="ts">
  import { getProjectList, type Project } from '@/apis/project/project';
  import { useBreakpoint } from '@/hooks/useBreakpoint';
  import ProjectItem from '@/views/project/components/ProjectItem.vue';
  import { transformProjectList } from '@/views/project/helper/index';

  const dataSource = ref([] as Project[]);
  const loading = ref(true);

  // 使用自定义断点 hook
  const { isCustom } = useBreakpoint();

  // 根据断点计算网格列数
  const gridCols = computed(() => {
    if (isCustom('1920') || isCustom('1600')) {
      return 'grid-cols-2';
    }
    return 'grid-cols-1';
  });

  getProjectList({
    pageIndex: 1,
    pageSize: 4,
  }).then((res) => {
    // 这里对数据结构进行转换
    dataSource.value = transformProjectList(res.data.data || []);
    loading.value = false;
  });
  const router = useRouter();
  const handleItemDetail = (item: Project) => {
    router.push(`/project/detail/${item.id}/check`);
  };
</script>

<template>
  <CardPane
    title="项目列表"
    :loading="loading"
  >
    <div :class="`grid ${gridCols} gap-6 pt-4`">
      <ProjectItem
        v-for="item in dataSource"
        :key="item.id"
        :can-iedit="false"
        :item="item"
        @item-detail="handleItemDetail(item)"
      />
    </div>
    <template #extra>
      <AButton
        type="link"
        size="small"
        @click="router.push('/project')"
      >
        更多>>
      </AButton>
    </template>
  </CardPane>
</template>
