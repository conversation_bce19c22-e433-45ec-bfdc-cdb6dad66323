<script setup lang="tsx">
  import { AppstoreOutlined, BarsOutlined } from '@ant-design/icons-vue';
  import { Button, Flex, Progress } from 'ant-design-vue';
  import { useRouter } from 'vue-router';

  import { getProjectList, type Project } from '@/apis/project/project';
  import ProjectItem from '@/views/project/components/ProjectItem.vue';
  import { transformProjectList } from '@/views/project/helper';

  const router = useRouter();

  defineProps<{
    height?: string | number;
  }>();

  const dataSource = ref([] as Project[]);
  const loading = ref(true);
  getProjectList({
    pageIndex: 1,
    pageSize: 6,
  }).then((res) => {
    // 这里对数据结构进行转换
    dataSource.value = transformProjectList(res.data.data || []);
    loading.value = false;
  });

  const getProgressColor = (value: number) => {
    if (value >= 80) return '#49CE20';
    if (value >= 60) return '#FF7102';
    return '#EE6060';
  };

  const columns = [
    { title: '项目/产品', dataIndex: 'programmeName', ellipsis: true },
    { title: '组件复用数', dataIndex: 'description', ellipsis: true },
    { title: '是否使用技术底座', dataIndex: 'createTime', ellipsis: true },
    {
      title: 'AI使用情况',
      dataIndex: 'processPercent',
      ellipsis: true,
      align: 'center',
    },
    {
      title: '是否逾期',
      dataIndex: 'processPercent',
      ellipsis: true,
      align: 'center',
    },
    {
      title: '研发进度',
      dataIndex: 'processPercent',
      width: 120,
      align: 'center',
      customRender: ({ record }: { record: any }) => {
        const progress = record.processPercent || 0;
        return (
          <Flex
            align="center"
            style="width: 100%;"
          >
            <Progress
              percent={progress}
              strokeColor={getProgressColor(progress)}
              showInfo={false}
              size="small"
              style="flex: 1;margin-right: 5px;margin-bottom: 0;"
            />
            <span style="flex-shrink: 0;color: #ff7102;font-size: 12px;font-weight:600;">
              {progress}%
            </span>
          </Flex>
        );
      },
    },
    {
      title: '过程符合度',
      dataIndex: 'processPercent',
      ellipsis: true,
      align: 'center',
    },
  ];

  const isCardMode = ref(true);
</script>

<template>
  <CardPane
    title="研发中的项目"
    :height="height"
    tip=""
    :loading="loading"
  >
    <template #prefix>
      <ACheckbox
        v-model:checked="isCardMode"
        class="modeBtn"
      >
        <BarsOutlined
          v-if="isCardMode"
          class="color-[var(--ant-colorPrimary)]"
        />
        <AppstoreOutlined
          v-else
          class="color-[var(--ant-colorPrimary)]"
        />
        {{ isCardMode ? '列表视图' : '卡片视图' }}
      </ACheckbox>
    </template>
    <template #extra>
      <AButton
        type="link"
        size="small"
        @click="router.push('/project')"
      >
        全部项目>>
      </AButton>
    </template>

    <div
      v-if="isCardMode"
      class="grid grid-cols-3 gap-6 pt-4"
    >
      <ProjectItem
        v-for="item in dataSource"
        :key="item.id"
        :can-iedit="false"
        :item="item"
      />
    </div>

    <ATable
      v-else
      class="stat-table"
      :data-source="dataSource"
      :columns="columns"
      :pagination="false"
      size="small"
      :bordered="false"
      row-key="project"
    />
  </CardPane>
</template>

<style scoped lang="less"></style>
