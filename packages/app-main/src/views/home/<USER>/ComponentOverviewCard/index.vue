<!--
 * 组件概览卡片
 * 展示已上架组件数量趋势图表
 -->
<script setup lang="ts">
  import type { EChartsOption } from 'echarts';
  import { LineChart } from 'echarts/charts';
  import { GridComponent, TooltipComponent } from 'echarts/components';
  import { use } from 'echarts/core';
  import { CanvasRenderer } from 'echarts/renderers';
  import { provide, ref } from 'vue';
  import VChart, { THEME_KEY } from 'vue-echarts';

  import { CardPane } from '@/components/Layout';

  // 注册必须的组件
  use([CanvasRenderer, LineChart, GridComponent, TooltipComponent]);

  // 设置主题
  provide(THEME_KEY, 'light');

  // 模拟数据
  const totalComponents = ref(284);
  const loading = ref(false);

  const option = ref<EChartsOption>({
    tooltip: {
      trigger: 'axis',
      backgroundColor: '#fff',
      borderColor: '#E5E6EB',
      borderWidth: 1,
      borderRadius: 8,
      textStyle: {
        color: '#333',
        fontSize: 12,
      },
      extraCssText: 'box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#4080FF',
        },
      },
      formatter: (params: any) => {
        const param = params[0];
        return `${param.axisValue}<br/>• 上架组件：${param.value}个`;
      },
    },
    grid: {
      top: 80,
      left: 20,
      right: 20,
      bottom: 20,
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['2025.05.01', '2025.05.02', '2025.05.03', '2025.05.04', '2025.05.05', '2025.05.06'],
      axisLine: {
        show: true,
        lineStyle: {
          color: '#E5E6EB',
          width: 1,
        },
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: '#999',
        fontSize: 11,
        margin: 15,
      },
    },
    yAxis: {
      type: 'value',
      show: true,
      min: 0,
      max: 350,
      splitNumber: 6,
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        show: true,
        color: '#999',
        fontSize: 11,
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
          color: '#E5E6EB',
          width: 1,
        },
      },
    },
    series: [
      {
        name: '上架组件',
        type: 'line',
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        showSymbol: false,
        data: [110, 160, 250, 200, 290, 180],
        itemStyle: {
          color: '#4080FF',
        },
        lineStyle: {
          color: '#4080FF',
          width: 2,
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(64, 128, 255, 0.15)',
              },
              {
                offset: 1,
                color: 'rgba(64, 128, 255, 0.02)',
              },
            ],
          },
        },
      },
    ],
  });
</script>

<template>
  <CardPane
    title="组件总览"
    :loading="loading"
    class="component-overview-card"
  >
    <div class="relative h-full">
      <div class="component-info-card">
        <div class="component-label">已上架组件</div>
        <div class="component-number">{{ totalComponents }}</div>
      </div>

      <!-- 图表容器 -->
      <VChart
        class="chart-container"
        :option="option"
        autoresize
      />
    </div>
  </CardPane>
</template>

<style scoped lang="less">
  .component-overview-card {
    .chart-container {
      width: 100%;
      height: 100%;
      min-height: 250px;
    }

    // 确保卡片有足够的高度
    :deep(.card-pane__body) {
      min-height: 280px;
    }

    .component-info-card {
      position: absolute;
      padding: 6px 16px;
      background: linear-gradient(270deg, rgba(151, 182, 255, 0) 7%, rgba(106, 149, 255, 0.1) 100%);
      border-left: 2px solid #3171ff;
      width: 100%;
      display: flex;
      align-items: center;

      .component-label {
        font-size: 12px;
        color: #666;
        font-weight: 400;
        margin-right: 10px;
      }

      .component-number {
        font-size: 28px;
        font-weight: bold;
        color: #4080ff;
        line-height: 1;
      }
    }
  }
</style>
