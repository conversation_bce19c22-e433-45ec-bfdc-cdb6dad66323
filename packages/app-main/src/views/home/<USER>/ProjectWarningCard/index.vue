<script setup lang="ts">
  import { ref } from 'vue';

  import {
  getProjectCheckWarningPageList,
  type GuideInfo,
  type ProjectCheckWarning,
} from '@/apis/checkpoint/agileStage';
import { getAllProjectList, type Project } from '@/apis/project/project';
import { renderTemplate } from '@/utils';

  const { size = 5, isProcessManager = false } = defineProps<{
    size?: number;
    isProcessManager?: boolean;
  }>();
  const loading = ref(false);
  const warningList = ref<ProjectCheckWarning[]>([
    // {
    //   programmeName: '河北省12345政务服务便民热线升级改造项目',
    //   programmeId: '1',
    //   supervised: 1,
    //   warningGuideInfo: '[{"btn_name":"去创建","btn_url":""}]',
    //   warningInfo: '缺少成果物',
    // },
    // {
    //   programmeName: '星辰·灵智AI服务平台',
    //   programmeId: '2',
    //   supervised: 1,
    //   warningGuideInfo: '[{"btn_name":"去创建","btn_url":""}]',
    //   warningInfo: '缺少架构设计文档',
    // },
    // {
    //   programmeName: '智胜·软件工厂',
    //   programmeId: '3',
    //   supervised: 0,
    //   warningGuideInfo: '[{"btn_name":"去创建","btn_url":""}]',
    //   warningInfo: '缺少测试申请',
    // },
    // {
    //   programmeName: '星辰·灵智AI服务平台',
    //   programmeId: '2',
    //   supervised: 0,
    //   warningGuideInfo: '[{"btn_name":"去创建","btn_url":""}]',
    //   warningInfo: '缺少需求规格说明书',
    // },
    // {
    //   programmeName: '智胜·多模态数据标注平台',
    //   programmeId: '4',
    //   supervised: 0,
    //   warningGuideInfo: '[{"btn_name":"去创建","btn_url":""}]',
    //   warningInfo: '缺少需求规格说明书',
    // },
    // {
    //   programmeName: '智胜·软件工厂',
    //   programmeId: '3',
    //   supervised: 0,
    //   warningGuideInfo: '[{"btn_name":"去创建","btn_url":""}]',
    //   warningInfo: '缺少架构设计文档',
    // },
    // {
    //   programmeName: '河北省12345政务服务便民热线升级改造项目',
    //   programmeId: '1',
    //   supervised: 0,
    //   warningGuideInfo: '[{"btn_name":"去创建","btn_url":""}]',
    //   warningInfo: '缺少设计评审',
    // },
    // {
    //   programmeName: '智胜·多模态数据标注平台',
    //   programmeId: '4',
    //   supervised: 0,
    //   warningGuideInfo: '[{"btn_name":"去创建","btn_url":""}]',
    //   warningInfo: '缺少数据库设计文档',
    // },
    // {
    //   programmeName: '智胜·软件工厂',
    //   programmeId: '3',
    //   supervised: 0,
    //   warningGuideInfo: '[{"btn_name":"去创建","btn_url":""}]',
    //   warningInfo: '缺少概要设计文档',
    // },
  ]);

  function getData() {
    loading.value = true;
    getAllProjectList()
      .then((res) => {
        const list = res.data.data || [];
        const ids = list?.map((item: Project) => item.id);
        getProjectCheckWarningPageList({
          programmeIds: ids.join(','),
          pageIndex: 1,
          pageSize: size,
        })
          .then((res) => {
            // test 模拟数据
            const list = res.data.data || [];
            warningList.value = list.length > 0 ? list : warningList.value;

            // warningList.value = list.filter((item: ProjectCheckWarning) => item.warningInfo);
          })
          .finally(() => {
            loading.value = false;
          });
      })
      .finally(() => {
        loading.value = false;
      });
  }
  getData();
  function getWarningGuideInfo(warningGuideInfo: string, index: number) {
    try {
      const list = JSON.parse(warningGuideInfo) as GuideInfo[];
      return list[index] || { btn_name: '', btn_url: '' };
    } catch (error) {
      return { btn_name: '', btn_url: '' };
    }
  }
  function getWarningUrl(data: ProjectCheckWarning, index: number) {
    const btnInfo = getWarningGuideInfo(data.warningGuideInfo, index);
    return renderTemplate(btnInfo.btn_url, { projectId: data.programmeId });
  }
</script>

<template>
  <CardPane
    title="项目预警列表"
    :loading="loading"
  >
    <div class="flex flex-col">
      <div
        v-for="item in warningList.slice(0, size)"
        :key="item.programmeName"
        class="flex cursor-pointer items-center rounded px-2 py-13px hover:shadow-[0px_0px_6px_0px_rgba(0,0,0,0.0706)]"
      >
        <div class="flex flex-1 items-center truncate">
          <span
            v-if="item.supervised === 1"
            class="h-20px w-34px flex-shrink-0 rounded-6px bg-[rgba(254,194,195,35)] text-center font-bold line-height-20px color-#AA1712"
          >
            督办
          </span>
          <img
            v-else
            class="h-2 w-6px"
            src="@/assets/images/list_icon.png"
          />
          <span
            class="ml-4 mr-2 truncate truncate text-[#4B5071]"
            :title="item.programmeName"
          >
            {{ item.programmeName }}
          </span>
          <span
            class="flex-1 truncate text-sm text-[#1890ff]"
            :title="item.warningInfo"
          >
            {{ item.warningInfo }}
          </span>
        </div>
        <a
          :href="getWarningUrl(item, 0)"
          target="_blank"
          class="text-sm text-[#FF7A45] hover:text-[#ff4d4f]"
        >
          {{ isProcessManager ? '督办' : getWarningGuideInfo(item.warningGuideInfo, 0)?.btn_name }}
        </a>
      </div>
    </div>
  </CardPane>
</template>

<style scoped lang="less"></style>
