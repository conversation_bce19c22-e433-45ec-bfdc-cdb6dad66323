<!--
  热门组件复用TOP5卡片组件
  展示组件使用情况的水平条形图
-->
<script setup lang="ts">
  import type { EChartsOption } from 'echarts';
  import { BarChart } from 'echarts/charts';
  import { GridComponent, TooltipComponent } from 'echarts/components';
  import { use } from 'echarts/core';
  import { CanvasRenderer } from 'echarts/renderers';
  import VChart from 'vue-echarts';

  // 注册必须的组件
  use([CanvasR<PERSON><PERSON>, BarChart, GridComponent, TooltipComponent]);

  defineProps<{
    height?: string | number;
  }>();

  const loading = ref(false);

  // 模拟数据
  const chartData = ref([
    { name: '附件组件', value: 85 },
    { name: 'AI中台', value: 68 },
    { name: '智能体平台', value: 45 },
    { name: '工作流组件', value: 38 },
    { name: '日志组件', value: 28 },
    { name: '消息组件', value: 25 },
  ]);

  const chartOption = computed<EChartsOption>(() => ({
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      formatter: (params: any) => {
        const data = params[0];
        return `${data.name}<br/>使用次数: ${data.value}`;
      },
    },
    grid: {
      left: 80,
      right: 30,
      top: 20,
      bottom: 20,
      containLabel: false,
    },
    xAxis: {
      type: 'value',
      max: 100,
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        show: true,
        color: '#999',
        fontSize: 12,
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
          color: '#E5E6EB',
        },
      },
    },
    yAxis: {
      type: 'category',
      data: chartData.value.map((item) => item.name),
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: '#333',
        fontSize: 12,
        margin: 10,
      },
      inverse: true, // 反转Y轴，让第一项在顶部
    },
    series: [
      {
        type: 'bar',
        data: chartData.value.map((item) => item.value),
        barWidth: 15,
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 1,
            y2: 0,
            colorStops: [
              { offset: 0, color: '#4080FF' },
              { offset: 1, color: '#A2E6FF' },
            ],
          },
          borderRadius: [0, 8, 8, 0],
        },
        label: {
          show: false,
        },
      },
    ],
  }));
</script>

<template>
  <CardPane
    title="热门组件复用TOP5"
    :loading="loading"
    :height="height"
  >
    <VChart
      :option="chartOption"
      autoresize
      style="height: 100%; width: 100%"
    />
  </CardPane>
</template>

<style scoped lang="less"></style>
