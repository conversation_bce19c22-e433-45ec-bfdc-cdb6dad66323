<script setup lang="ts">
  import { ref } from 'vue';

  import { getProjectUpCloudList, type ProjectUpCloud } from '@/apis/checkpoint/agileStage';
import { getAllProjectList, type Project } from '@/apis/project/project';

  const { size = 5 } = defineProps<{
    size?: number;
  }>();

  const cloudProjects = ref<ProjectUpCloud[]>([]);
  const loading = ref(false);
  function getData() {
    loading.value = true;
    getAllProjectList()
      .then((res) => {
        const list = res.data.data || [];
        const ids = list?.map((item: Project) => item.id);
        getProjectUpCloudList({
          programmeIds: ids.join(','),
          pageIndex: 1,
          pageSize: size,
        })
          .then((res) => {
            // test 模拟数据
            const list = res.data.data || [];
            cloudProjects.value = list.length > 0 ? list.slice(0, size) : [];

            // warningList.value = list.filter((item: ProjectCheckWarning) => item.warningInfo);
          })
          .finally(() => {
            loading.value = false;
          });
      })
      .finally(() => {
        loading.value = false;
      });
  }
  getData();

  const getComplianceColor = (compliance: number): string => {
    if (compliance >= 80) return 'text-[#52c41a]';
    if (compliance >= 60) return 'text-[#faad14]';
    return 'text-[#ff4d4f]';
  };
</script>

<template>
  <CardPane
    title="项目全流程上云概览"
    :loading="loading"
  >
    <div class="flex flex-col">
      <!-- 表头 -->
      <div class="flex items-center bg-[#F8F9FE] px-6 py-4">
        <div class="flex-1 text-[#4B5071]">项目名称</div>
        <div class="w-20 text-center text-[#5C6B8A]">符合度</div>
        <div class="w-32 text-center text-[#5C6B8A]">不符合项</div>
      </div>
      <!-- 列表内容 -->
      <div class="flex flex-col">
        <div
          v-for="(project, index) in cloudProjects"
          :key="index"
          class="flex items-center border-b border-[#E5E6EB] px-6 py-4"
        >
          <div
            class="flex-1 truncate text-[#4B5071]"
            :title="project.programmeName"
          >
            {{ project.programmeName }}
          </div>
          <div
            class="w-20 text-center"
            :class="getComplianceColor(project.srdProcessConformity)"
          >
            <!-- {{ project.compliance }}% -->
            -
          </div>
          <div class="w-32 text-center text-[#FF7D00]">
            {{ project.srdProcessNotConformityItems?.split?.(',') || '-' }}
          </div>
        </div>
      </div>
    </div>
  </CardPane>
</template>

<style lang="less" scoped>
  :deep(.ant-card-head) {
    min-height: 48px;
    padding: 0 24px;
    border-bottom: 1px solid #e5e6eb;
  }
</style>
