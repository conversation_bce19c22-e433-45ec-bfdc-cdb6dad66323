<script setup lang="tsx">
const columns = [
  { title: '序号', dataIndex: 'index', key: 'index', align: 'center' },
  { title: '能力类型', dataIndex: 'type', key: 'type', align: 'center' },
  { title: '能力名称', dataIndex: 'name', key: 'name', align: 'center' },
  { title: '复用次数', dataIndex: 'reuse', key: 'reuse', align: 'center' },
  { title: '累计节约研发成本（万元）', dataIndex: 'cost', key: 'cost', align: 'center' },
  {
    title: '能力评价（满分10分）', dataIndex: 'score', key: 'score', align: 'center', customRender: ({ text }: { text: string }) => {
      const score = Number(text);
      let textColor = '#979797';
      if (score === 10) {
        textColor = '#49CE20';
      } else if (score >= 5 && score < 10) {
        textColor = '#FF7102';
      } else {
        textColor = '#FF545B';
      }
      return <span style={{ color: textColor }} > {score} 分 </span>;
    }
  },
];
const dataSource = [
  { index: 1, type: '代码助手', name: 'CodeFree', reuse: 342, cost: 34, score: '10' },
  { index: 2, type: '文档助手', name: 'CodeFree代码助手', reuse: 289, cost: 31, score: '7' },
  { index: 3, type: '测试助手', name: '智能体', reuse: 276, cost: 23, score: '9' },
  { index: 4, type: '安全助手', name: '文档助手', reuse: 265, cost: 24, score: '2' },
  { index: 5, type: '工程助手', name: '测试助手', reuse: 254, cost: 14, score: '5' },
];
</script>
<template>
  <CardPane title="能力超市概览">
    <a-table :bordered="false" class="stat-table" :columns="columns" :data-source="dataSource" :pagination="false"
      bordered row-key="index" />
  </CardPane>
</template>