<script setup lang="tsx">
  import { CaretDownOutlined, CaretUpOutlined } from '@ant-design/icons-vue';

  import { getAiUsedColor } from '@/dicts/project';
  import { useAntProgressAnimate } from '@/hooks/useAntProgressAnimate';

  const percent = 59.5;
  const usedPercent = 59.5;
  const usedCount = 1192;
  const unusedPercent = 40.5;
  const unusedCount = 813;
  const weekRate = 12;
  const dayRate = 10;
  const aiUsedColor = getAiUsedColor(percent);

  // 使用动画 hook
  const { animatedValue: animatedPercent } = useAntProgressAnimate({
    targetValue: percent,
    duration: 600
  });
</script>

<template>
  <CardPane title="研发人员AI使用情况">
    <div class="ai-used-card">
      <div class="progress-wrapper py-6">
        <AProgress
          type="circle"
          :percent="animatedPercent"
          :stroke-color="'#FF7102'"
          :size="190"
          :stroke-width="8"
        >
          <template #format>
            <div class="progress-center">
              <div
                class="percent"
                :style="{ color: aiUsedColor }"
              >
                {{ animatedPercent }}%
              </div>
              <div class="desc">AI使用率</div>
            </div>
          </template>
        </AProgress>
      </div>
      <div class="mt-[22px]">
        <AFlex
          vertical
          :gap="20"
        >
          <div>
            <ABadge :color="aiUsedColor"></ABadge>
            <ASpace :size="20">
              <span>已使用人数</span>
              <span class="orange">{{ usedPercent }}%</span>
              <span class="orange">{{ usedCount }}人</span>
            </ASpace>
          </div>

          <div class="row">
            <ABadge color="rgba(236, 236, 236, 0.85)"></ABadge>
            <ASpace :size="20">
              <span>未使用人数</span>
              <span class="gray">{{ unusedPercent }}%</span>
              <span class="gray">{{ unusedCount }}人</span>
            </ASpace>
          </div>
        </AFlex>
      </div>
      <div class="ai-used-footer">
        <span>
          周同比
          <span class="red ml-2">
            {{ weekRate }}%
            <CaretUpOutlined class="arrow" />
          </span>
        </span>
        <span class="divider"></span>
        <span>
          日环比
          <span class="green ml-2">
            {{ dayRate }}%
            <CaretDownOutlined class="arrow" />
          </span>
        </span>
      </div>
    </div>
  </CardPane>
</template>

<style scoped lang="less">
  .ai-used-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    .progress-wrapper {
      .percent {
        font-size: 36px;
        font-weight: bold;
        margin-bottom: 10px;
      }
      .desc {
        font-size: 18px;
        color: var(--zion-text-base1);
      }
    }
    .ai-used-footer {
      margin-top: 35px;
      background: rgba(239, 243, 254, 1);
      border-radius: 4px;
      padding: 6px 10px;
      padding-left: 12px;
      color: var(--zion-text-base1);
      display: flex;
      align-items: center;
      .divider {
        width: 1px;
        height: 12px;
        background: #d5d5d5;
        margin: 0 6px;
        margin-right: 10px;
        display: inline-block;
      }
      .red {
        color: #ff7102;
      }
      .green {
        color: #49ce20;
      }
      .arrow {
        font-size: 12px;
        margin-left: 2px;
      }
    }
  }
</style>
