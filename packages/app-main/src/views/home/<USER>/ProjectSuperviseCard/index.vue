<script setup lang="tsx">
  import { message } from 'ant-design-vue';

  defineProps<{
    height?: string | number;
  }>();

  // 督办列表 mock 数据
  const superviseList = [
    { name: '渔业信息化管理服务平台（一期）', item: '无cd', date: '2025.05.12', overdue: true },
    { name: '宜昌市兴山智慧旅游平台项目', item: '缺少文档', date: '2024.08.23', overdue: false },
    { name: '天翼云图', item: '无cd', date: '2025.03.08', overdue: true },
    { name: '甘肃机电职业技术学院OA项目维保', item: '无文档', date: '2023.11.15', overdue: false },
    {
      name: '甘肃省智慧博物馆项目"数字甘博"平台单纯购置项目',
      item: '无cd',
      date: '2024.12.30',
      overdue: false,
    },
    { name: '陇南市教育局协同办公项目', item: '缺少文档', date: '2022.06.18', overdue: false },
    {
      name: '河北省12345政务服务便民热线升级改造项目',
      item: '缺少文档',
      date: '2024.02.14',
      overdue: false,
    },
    { name: '民生诉求一体化平台', item: '无cd', date: '2021.09.05', overdue: false },
  ];

  const superviseColumns = [
    { title: '项目名称', dataIndex: 'name', key: 'name', ellipsis: true },
    { title: '督办事项', dataIndex: 'item', key: 'item', ellipsis: true },
    { title: '督办时间', dataIndex: 'date', key: 'date', ellipsis: true },
    {
      title: '是否超期',
      dataIndex: 'overdue',
      key: 'overdue',
      align: 'center' as const,
      customRender: ({ text }: any) => {
        return text ? <span style={{ color: '#FF7102' }}>是</span> : <span>否</span>;
      },
    },
    {
      title: '操作',
      key: 'action',
      align: 'center' as const,
      customRender: () => {
        return (
          <a
            style={{ color: '#2664F6', cursor: 'pointer' }}
            onClick={() => message.info('待开发')}
          >
            取消督办
          </a>
        );
      },
    },
  ];
</script>

<template>
  <CardPane title="督办列表">
    <template #extra>
      <AButton
        type="link"
        @click="message.info('待开发')"
      >
        更多>>
      </AButton>
    </template>
    <ATable
      class="stat-table"
      :data-source="superviseList"
      :columns="superviseColumns"
      :pagination="false"
      size="small"
      :bordered="false"
      row-key="name"
    />
  </CardPane>
</template>

<style scoped lang="less">
  .stat-table {
    :deep(.ant-table-thead > tr > th) {
      background: #fafafa;
      font-weight: 500;
    }
    :deep(.ant-table-tbody > tr > td) {
      font-size: 15px;
    }
    :deep(.ant-table-tbody > tr > td:nth-child(4)) {
      font-weight: 500;
    }
    :deep(.ant-table-tbody > tr > td:nth-child(1)),
    :deep(.ant-table-tbody > tr > td:nth-child(2)),
    :deep(.ant-table-tbody > tr > td:nth-child(3)) {
      color: #4b5071;
    }
    :deep(.ant-table-tbody > tr > td:nth-child(5) a) {
      color: #2664f6;
    }
  }
</style>
