<script setup lang="tsx">
  import { Button, Flex, Progress } from 'ant-design-vue';
  import { useRouter } from 'vue-router';

  const router = useRouter();

  defineProps<{
    height?: string | number;
  }>();

  const dataSource = [
    {
      project: '渔业信息化管理服务平台（一期）',
      version: 'v.2.1.3',
      content: '新增用户权限管理模块，优化数据统计功能',
      date: '2024-12-15',
      demand: '12/45',
      task: '8/32',
      loophole: '2/15',
      defect: '3/18',
      progress: 78,
    },
    {
      project: '宜昌市兴山智慧旅游平台项目',
      version: 'v.1.5.2',
      content: '修复地图显示异常，增加景点推荐算法',
      date: '2024-12-10',
      demand: '6/28',
      task: '5/22',
      loophole: '1/8',
      defect: '2/12',
      progress: 65,
    },
    {
      project: '天翼云图',
      version: 'v.3.0.1',
      content: '重构核心架构，提升系统性能30%',
      date: '2024-12-08',
      demand: '18/67',
      task: '15/54',
      loophole: '4/23',
      defect: '6/29',
      progress: 82,
    },
    {
      project: '甘肃机电职业技术学院OA项目维保',
      version: 'v.1.2.8',
      content: '修复考勤统计bug，新增请假审批流程',
      date: '2024-12-05',
      demand: '3/12',
      task: '2/8',
      loophole: '0/3',
      defect: '1/5',
      progress: 45,
    },
    {
      project: '甘肃省智慧博物馆项目"数字甘博"平台单纯购置项目',
      version: 'v.1.0.5',
      content: '完善文物展示功能，优化移动端体验',
      date: '2024-12-03',
      demand: '8/35',
      task: '6/28',
      loophole: '2/11',
      defect: '3/16',
      progress: 58,
    },
    {
      project: '陇南市教育局协同办公项目',
      version: 'v.1.3.4',
      content: '新增文件在线预览，优化消息通知系统',
      date: '2024-11-28',
      demand: '5/19',
      task: '4/15',
      loophole: '1/6',
      defect: '2/9',
      progress: 72,
    },
    {
      project: '河北省12345政务服务便民热线升级改造项目',
      version: 'v.2.0.2',
      content: '升级语音识别引擎，新增智能分流功能',
      date: '2024-11-25',
      demand: '15/52',
      task: '12/41',
      loophole: '3/18',
      defect: '4/22',
      progress: 89,
    },
    {
      project: '民生诉求一体化平台',
      version: 'v.1.8.1',
      content: '优化工单处理流程，新增满意度评价功能',
      date: '2024-11-20',
      demand: '10/38',
      task: '8/31',
      loophole: '2/13',
      defect: '3/17',
      progress: 67,
    },
  ];
  const getProgressColor = (value: number) => {
    if (value >= 80) return '#49CE20';
    if (value >= 60) return '#FF7102';
    return '#EE6060';
  };

  const columns = [
    { title: '项目/产品', dataIndex: 'project', key: 'project', ellipsis: true },
    { title: '待发布版本/里程碑', dataIndex: 'version', key: 'version', ellipsis: true },
    { title: '发布内容', dataIndex: 'content', key: 'content', ellipsis: true },
    { title: '发布日期', dataIndex: 'date', key: 'date', ellipsis: true },
    { title: '研发需求', dataIndex: 'demand', key: 'demand', ellipsis: true },
    { title: '研发任务', dataIndex: 'task', key: 'task', ellipsis: true },
    { title: '源码漏洞', dataIndex: 'loophole', key: 'loophole', ellipsis: true },
    { title: '测试缺陷', dataIndex: 'defect', key: 'defect', ellipsis: true },
    {
      title: '研发进度',
      dataIndex: 'progress',
      key: 'progress',
      width: 120,
      customRender: ({ record }: { record: any }) => {
        const progress = record.progress || 0;
        return (
          <Flex
            align="center"
            style="width: 100%;"
          >
            <Progress
              percent={progress}
              strokeColor={getProgressColor(progress)}
              showInfo={false}
              size="small"
              style="flex: 1;margin-right: 5px;margin-bottom: 0;"
            />
            <span style="flex-shrink: 0;color: #ff7102;font-size: 12px;font-weight:600;">
              {record.progress}%
            </span>
          </Flex>
        );
      },
    },
    {
      title: '操作',
      dataIndex: 'actions',
      key: 'actions',
      width: 120,
      algin: 'center' as const,
      customRender: ({ record }: { record: any }) => {
        return (
          <Button
            type="link"
            size="small"
            class={'important-text-12px'}
          >
            申请放行
          </Button>
        );
      },
    },
  ];
</script>

<template>
  <CardPane
    title="待发布的版本/里程碑"
    :height="height"
    tip=""
  >
    <template #extra>
      <AButton
        type="link"
        size="small"
        @click="router.push('/project')"
      >
        更多>>
      </AButton>
    </template>
    <ATable
      class="stat-table"
      :data-source="dataSource"
      :columns="columns"
      :pagination="false"
      size="small"
      :bordered="false"
      row-key="project"
    />
  </CardPane>
</template>

<style scoped lang="less"></style>
