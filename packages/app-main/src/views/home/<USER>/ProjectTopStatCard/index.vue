<script setup lang="ts">
  import { CaretDownOutlined, CaretUpOutlined, InfoCircleOutlined } from '@ant-design/icons-vue';
  import CountUp from 'vue-countup-v3';

  import { useBreakpoint } from '@/hooks/useBreakpoint';

  // 使用自定义断点 hook
  const { isCustom } = useBreakpoint();

  // 根据断点计算网格列数
  const statMlClass = computed(() => {
    if (isCustom('1920')) {
      return 'ml-4';
    }
    return '';
  });
  const topStats = [
    {
      title: '人均研发贡献',
      value: 92,
      suffix: '分',
      valueColor: '#49CE20',
      info: '人均研发贡献分数说明',
      week: { value: 10, up: true, color: '#EE6060' },
      day: { value: 8, up: false, color: '#49CE20' },
    },
    {
      title: 'AI提效比',
      value: 49,
      suffix: '%',
      valueColor: '#49CE20',
      unitColor: '#49CE20',
      info: 'AI提效比说明',
      week: { value: 16, up: true, color: '#EE6060' },
      day: { value: 20, up: false, color: '#49CE20' },
    },
    {
      title: '组件复用节约研发成本',
      value: 226,
      suffix: '万元',
      valueColor: '#FF7102',
      info: '组件复用节约研发成本说明',
      hideContrast: true,
      // week: { value: 12, up: true, color: '#EE6060' },
      // day: { value: 12, up: false, color: '#49CE20' },
    },
    {
      // type: 'double',
      // items: [
      // {
      //   title: '人均研发贡献',
      //   value: 150,
      //   suffix: '分',
      //   valueColor: '#3173FF',
      //   unitColor: '#3173FF',
      //   info: '人均研发贡献分数说明',
      // },
      // {
      title: '研发岗人员',
      value: 598,
      suffix: '人',
      valueColor: '#3173FF',
      unitColor: '#3173FF',
      info: '研发岗人员说明',
      hideContrast: true,
      // },
      // ],
      // week: { value: 12, up: true, color: '#EE6060' },
      // day: { value: 12, up: false, color: '#49CE20' },
    },
    {
      title: '整体研发符合度',
      value: 52,
      suffix: '%',
      valueColor: '#49CE20',
      unitColor: '#49CE20',
      info: '整体研发符合度说明',
      week: { value: 21, up: true, color: '#EE6060' },
      day: { value: 16, up: false, color: '#49CE20' },
    },
  ];
</script>

<template>
  <div class="top-stats-flex">
    <div
      v-for="(stat, idx) in topStats"
      :key="stat.title || stat.type"
      class="stat-card"
      :class="{ 'double-stat-card': stat.type === 'double' }"
    >
      <!-- 单项卡片 -->
      <template v-if="stat.type !== 'double'">
        <div class="stat-title">
          <span
            class="truncate"
            :title="stat.title"
          >
            {{ stat.title }}
          </span>
          <ATooltip
            :title="stat.info"
            color="#fff"
            :overlay-inner-style="{
              color: '#000',
            }"
          >
            <InfoCircleOutlined class="ml-2 align-middle text-gray-400" />
          </ATooltip>
        </div>
        <div
          class="stat-value"
          :style="{ marginTop: stat.hideContrast ? '40px' : '20px' }"
        >
          <CountUp
            :end-val="Number(stat.value)"
            :duration="1.2"
            :style="{ color: stat.valueColor }"
            class="main-num"
          />
          <span
            class="stat-suffix"
            :style="{ color: stat.unitColor }"
          >
            {{ stat.suffix }}
          </span>
        </div>
      </template>
      <!-- 双项卡片 -->
      <div
        v-else
        class="double-stat-info-wrapper"
      >
        <div
          v-for="(item, index) in stat.items"
          :key="item.title"
          class="double-stat-item"
        >
          <div :class="['stat-title', index === 1 && 'right']">
            <span
              class="truncate"
              :title="item.title"
            >
              {{ item.title }}
            </span>
            <ATooltip
              :title="item.info"
              color="#fff"
              :overlay-inner-style="{
                color: '#000',
              }"
            >
              <InfoCircleOutlined class="ml-1 align-middle text-gray-400" />
            </ATooltip>
          </div>
          <div class="stat-value">
            <CountUp
              :end-val="Number(item.value)"
              :duration="1.2"
              :style="{ color: item.valueColor }"
              class="main-num"
            />
            <span
              class="stat-suffix"
              :style="{ color: item.unitColor }"
            >
              {{ item.suffix }}
            </span>
          </div>
        </div>
      </div>
      <div
        v-if="!stat.hideContrast"
        class="stat-divider"
      ></div>
      <div class="stat-trend-row">
        <ASpace v-if="stat.week">
          <span class="stat-trend-label">周同比</span>
          <span
            :style="{ color: stat.week?.color }"
            class="whitespace-nowrap"
          >
            {{ stat.week?.value }}%
            <CaretUpOutlined v-if="stat.week?.up" />
            <CaretDownOutlined v-else />
          </span>
        </ASpace>
        <ASpace v-if="stat.day">
          <span :class="['stat-trend-label', statMlClass]">日环比</span>
          <span
            :style="{ color: stat.day?.color }"
            class="whitespace-nowrap"
          >
            {{ stat.day?.value }}%
            <CaretUpOutlined v-if="stat.day?.up" />
            <CaretDownOutlined v-else />
          </span>
        </ASpace>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
  .top-stats-flex {
    display: flex;
    gap: 16px;
  }
  .stat-card {
    flex: 1 1 20%;
    min-width: 0;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.04);
    padding: 20px;
    padding-bottom: 16px;
    display: flex;
    flex-direction: column;
  }
  .double-stat-card {
    justify-content: space-between;
    .double-stat-info-wrapper {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 10px;
      .double-stat-item {
        flex: 1;
        display: flex;
        flex-direction: column;
        min-width: 0;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        &:first-child {
          align-items: flex-start;
        }
        &:last-child {
          align-items: flex-end;
        }
      }
    }
  }
  .stat-title {
    font-size: 15px;
    color: #222;
    font-weight: 500;
    display: flex;
    align-items: center;
    width: 100%;
    &.right {
      justify-content: flex-end;
    }
  }
  .stat-value {
    font-size: 36px;
    font-weight: bold;
    margin-top: 20px;
    margin-bottom: 20px;
    display: flex;
    align-items: baseline;
    line-height: 1;
    justify-content: center;
  }
  .stat-value .main-num {
    line-height: 1;
    display: inline-block;
  }
  .stat-suffix {
    font-size: 18px;
    margin-left: 2px;
    font-weight: normal;
  }
  .stat-divider {
    width: 100%;
    border-bottom: 1px dashed #e5e6eb;
    margin: 12px 0 8px 0;
  }
  .stat-trend-row {
    width: 100%;
    display: flex;
    // padding-left: 10px;
    // padding-right: 10px;
    align-items: center;
    justify-content: space-between;
    font-size: 13px;
    margin-top: 10px;
    gap: 8px;
  }
  .stat-trend-label {
    color: #888;
    margin-right: 2px;
    white-space: nowrap;
  }
  @media (max-width: 1366px) {
    .top-stats-flex {
      gap: 8px;
    }
    .stat-card {
      padding: 10px 4px 6px 4px;
    }
    .stat-title {
      font-size: 13px;
    }
    .stat-value {
      font-size: 22px;
    }
    .stat-suffix {
      font-size: 12px;
    }
  }
</style>
