<script setup lang="ts">
  import dayjs from 'dayjs';

  import { getDateOfAgo } from '@/utils';

  const props = withDefaults(
    defineProps<{
      defaultDays?: string | number;
    }>(),
    {
      defaultDays: '30',
    },
  );
  const date = ref<string | number>(props.defaultDays || '30');
  const endTime = dayjs().format('YYYY-MM-DD HH:mm:ss');
  const dateList: Record<string, { startTime: string; endTime: string }> = {
    '7': {
      startTime: getDateOfAgo(7),
      endTime,
    },
    '30': {
      startTime: getDateOfAgo(30),
      endTime,
    },
    '60': {
      startTime: getDateOfAgo(60),
      endTime,
    },
    '90': {
      startTime: getDateOfAgo(90),
      endTime,
    },
  };
  const data = defineModel<{
    startTime: string;
    endTime: string;
  }>('modelValue', {
    required: true,
  });
  const emit = defineEmits<{
    (
      e: 'change',
      value: { date: string | number; data: { startTime: string; endTime: string } },
    ): void;
  }>();
  const handleChange = (value: string | number) => {
    date.value = value;
    data.value = dateList[value as string];
    emit('change', {
      date: value,
      data: dateList[value as string],
    });
  };
  handleChange(props.defaultDays || '30');
  function getPopupContainer(triggerNode: HTMLElement) {
    return triggerNode.parentElement || document.body;
  }
</script>

<template>
  <ASelect
    ref="select"
    v-model:value="date"
    class="rounded-select w-137px"
    :get-popup-container="getPopupContainer"
    @change="handleChange"
  >
    <ASelectOption
      v-for="(item, key) in dateList"
      :key="key"
      :value="key"
    >
      近{{ key }}天
    </ASelectOption>
  </ASelect>
</template>
