<script setup lang="tsx">
  import { CaretDownOutlined, CaretUpOutlined } from '@ant-design/icons-vue';
  import { Flex, Progress } from 'ant-design-vue';

  defineProps<{
    height?: string | number;
  }>();

  const timeRange = ref('30');

  const aiEfficiencyRank = [
    {
      dept: '代码助手',
      score: 693,
      efficiencyPerson: 70,
      totalPerson: 48510,
      progress: 8,
      progressType: 'down',
    },
    {
      dept: '文档助手',
      score: 138,
      efficiencyPerson: 6,
      totalPerson: 828,
      progress: 2,
      progressType: 'up',
    },
    {
      dept: '测试助手',
      score: 86,
      efficiencyPerson: 11,
      totalPerson: 964,
      progress: 14,
      progressType: 'up',
    },
    {
      dept: '工程助手',
      score: 265,
      efficiencyPerson: 4,
      totalPerson: 1060,
      progress: 3,
      progressType: 'down',
    },
    {
      dept: '问答',
      score: 862,
      efficiencyPerson: 15,
      totalPerson: 12930,
      progress: 21,
      progressType: 'up',
    },
  ];
  const handleTimeRangeChange = (value: string) => {
    timeRange.value = value;
  };
  const getProgressColor = (value: number) => {
    if (value >= 80) return '#49CE20';
    if (value >= 60) return '#FF7102';
    return '#EE6060';
  };

  const aiEfficiencyColumns = [
    { title: '能力类型', dataIndex: 'dept', key: 'dept', width: 120 },
    { title: '应用人数', dataIndex: 'score', key: 'score', width: 120, align: 'center' },
    {
      title: '应用次数',
      dataIndex: 'totalPerson',
      key: 'totalPerson',
      width: 120,
      align: 'center',
    },
    {
      title: '人均使用',
      dataIndex: 'efficiencyPerson',
      key: 'efficiencyPerson',
      width: 120,
      align: 'center',
    },
    {
      title: '月环比变化',
      key: 'progress',
      align: 'center' as const,
      width: 120,
      customRender: ({ record }: { record: any }) => {
        const progressColor = record.progressType === 'up' ? '#49CE20' : '#EE6060';
        return (
          <span>
            <span style="display:inline-block;width: 40px;text-align:right;margin-right: 4px;">
              {record.progress}%
            </span>
            {record.progressType === 'up' ? (
              <CaretUpOutlined style={{ color: progressColor }} />
            ) : (
              <CaretDownOutlined style={{ color: progressColor }} />
            )}
          </span>
        );
      },
    },
  ];
</script>

<template>
  <CardPane
    title="AI能力分类统计"
    :height="height"
  >
    <ATable
      class="stat-table stat-table-compact"
      :data-source="aiEfficiencyRank"
      :columns="aiEfficiencyColumns"
      :pagination="false"
      :bordered="false"
      row-key="dept"
    />
  </CardPane>
</template>

<style scoped lang="less">
  // .stat-table {
  //   :deep(.ant-table-thead > tr > th) {
  //     background: #fafafa;
  //     font-weight: 500;
  //   }
  // }
</style>
