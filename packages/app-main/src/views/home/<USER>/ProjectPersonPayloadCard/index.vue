<script setup lang="tsx">
  import medal1 from '@/assets/images/rank_1.png';
  import medal2 from '@/assets/images/rank_2.png';
  import medal3 from '@/assets/images/rank_3.png';
  import medalOther from '@/assets/images/rank_other.png';

  defineProps<{
    height?: string | number;
  }>();

  const personRankTableRaw = [
    { dept: '数智事业部', contribution: 99, actual: 201, total: 299, load: 135 },
    { dept: '工业互联网', contribution: 90, actual: 15, total: 63, load: 121 },
    { dept: '农村农业事业部', contribution: 85, actual: 56, total: 142, load: 111 },
    { dept: '要客部', contribution: 84, actual: 13, total: 42, load: 99 },
    { dept: 'AI及大数据事业部', contribution: 84, actual: 196, total: 254, load: 98 },
    { dept: '标品业务部', contribution: 83, actual: 12, total: 52, load: 96 },
    { dept: '文商旅事业部', contribution: 82, actual: 99, total: 166, load: 95 },
    { dept: '视频服务部', contribution: 80, actual: 38, total: 56, load: 92 },
    // 可扩展更多 mock 数据
    // { dept: '新事业部A', contribution: 91, actual: 20, total: 100, load: 85 },
    // { dept: '新事业部B', contribution: 90, actual: 18, total: 90, load: 80 },
    // { dept: '新事业部C', contribution: 89, actual: 16, total: 80, load: 75 },
  ];
  const personRankTable = personRankTableRaw.slice(0, 10);
  const medalImgs = [medal1, medal2, medal3];
  const getLoadColor = (load: number) => {
    if (load > 100) return '#EE6060';
    return '#888';
  };
  const personRankColumns = [
    {
      title: '排名',
      key: 'rank',
      align: 'center' as const,
      width: 60,
      customRender: ({ index }: { index: number }) => {
        return (
          <a-flex
            vertical
            align="center"
            class="relative"
          >
            <img
              src={index < 3 ? medalImgs[index] : medalOther}
              alt={`medal${index + 1}`}
              style="width:16px;vertical-align:middle;"
            />
            <span class="absolute bottom-[0] text-[10px] text-[var(--ant-colorBgBase)]">
              {index + 1}
            </span>
          </a-flex>
        );
      },
    },
    { title: '部门', dataIndex: 'dept', key: 'dept' },
    { title: '研发贡献', dataIndex: 'contribution', key: 'contribution', align: 'center' as const },
    {
      title: '实际研发人数',
      key: 'actual',
      align: 'center' as const,
      customRender: ({ record }: { record: any }) => {
        return [
          <span style="color:#3173FF;font-weight:bold;">{record.actual}</span>,
          ' / ',
          <span>{record.total}</span>,
        ];
      },
    },
    {
      title: '研发负荷',
      key: 'load',
      align: 'center' as const,
      customRender: ({ record }: { record: any }) => {
        const color = getLoadColor(record.load);
        return <span style={{ color, fontWeight: 'bold' }}>{record.load}%</span>;
      },
    },
  ];
</script>

<template>
  <CardPane
    title="人员负荷部门排行"
    :height="height"
  >
    <ATable
      class="stat-table"
      :data-source="personRankTable"
      :columns="personRankColumns"
      :pagination="false"
      size="small"
      row-key="dept"
    />
  </CardPane>
</template>

<style scoped lang="less">
  .rank-num {
    display: inline-block;
    width: 24px;
    text-align: center;
    color: #888;
    font-weight: bold;
  }
</style>
