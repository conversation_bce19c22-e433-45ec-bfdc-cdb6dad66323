<script setup lang="ts">
import AbilityItemIcon from '@/assets/images/ablility_item_icon.svg';
import DateSelect from '../DateSelect/index.vue';
const date = ref({
  startTime: '',
  endTime: '',
});
// 这里可以根据实际需要引入props或api
const appGroups = [
  {
    groupName: 'CodeFree能力应用',
    apps: [
      { name: '代码助手', users: 693, times: 48510 },
      { name: '文档助手', users: 138, times: 828 },
      { name: '测试助手', users: 86, times: 946 },
      { name: '安全助手', users: 265, times: 1060 },
      { name: '工程助手', users: 862, times: 12930 },
    ],
    groupCount: 41,
    groupId: 1,
    totalUsers: 27,
    totalTimes: 1225,
  },
  {
    groupName: '智能体辅助应用',
    apps: [
      { name: '智能预警类', users: 12, times: 986 },
      { name: '文本生成类', users: 15, times: 2795 },
      { name: '辅助研发类', users: 8, times: 1592 },
    ],
  },
];

</script>
<template>
  <CardPane title="智能化应用">
    <template #extra>
      <DateSelect v-model="date" />
    </template>
    <div class="pt-[40px]">
      <div v-for="group in appGroups" :key="group.groupId" class="mb-4">
        <TitleCard1 :title="group.groupName" />

        <div class="flex gap-[25px] mt-2 mb-6">
          <div v-for="app in group.apps" :key="app.name"
            class="flex gap-[14px] border border-solid border-[var(--zion-border-color)] border-opacity-20 rounded-lg p-4 w-[20%] bg-[#fdfeff]">

            <img :src="AbilityItemIcon" alt="" class="w-[48px] h-auto">
            <a-flex vertical class="flex-1">
              <div class="font-bold text-base mb-2">{{ app.name }}</div>
              <div class="text-xs text-gray-500">使用人数: <span class="text-[16px] text-[#000]">{{ app.users }}</span>人
              </div>
              <div class="text-xs text-gray-500">使用人次: <span class="text-[16px] text-[#000]">{{ app.times }}</span>人次
              </div>
            </a-flex>
          </div>
          <!-- 如果数量不足5个，补空div占位 -->
          <template v-if="group.apps.length < 5">
            <div v-for="n in 5 - group.apps.length" class="w-[20%]" :key="n"></div>
          </template>
        </div>
      </div>
    </div>
  </CardPane>
</template>