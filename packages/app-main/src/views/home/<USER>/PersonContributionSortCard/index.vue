<script setup lang="ts">
  import avatar from '@/layouts/assets/images/user-default.jpg';

  import ProjectSelect from '../ProjectSelect/index.vue';

  const userList = ref([
    {
      name: '王恒',
      value: 98,
      avatar: avatar,
    },
    {
      name: '王乾',
      value: 97,
      avatar: avatar,
    },
    {
      name: '刘伟',
      value: 95,
      avatar: avatar,
    },
    {
      name: '潘荣安',
      value: 94,
      avatar: avatar,
    },
    {
      name: '王冬',
      value: 94,
      avatar: avatar,
    },
    {
      name: '付家明',
      value: 93,
      avatar: avatar,
    },
    {
      name: '王文兵',
      value: 92,
      avatar: avatar,
    },
    {
      name: '雷尚林',
      value: 91,
      avatar: avatar,
    },
    {
      name: '杨磊',
      value: 89,
      avatar: avatar,
    },
    {
      name: '王成飞',
      value: 88,
      avatar: avatar,
    },
    {
      name: '祁永刚',
      value: 87,
      avatar: avatar,
    },
    {
      name: '陈超',
      value: 84,
      avatar: avatar,
    },
  ]);
</script>

<template>
  <CardPane title="研发贡献排名">
    <template #extra>
      <!-- <ProjectSelect v-model="project" /> -->
    </template>
    <div class="flex flex-col">
      <div class="rank-list w-full pl-4">
        <div class="grid grid-cols-2 gap-x-20px gap-y-3px">
          <div
            v-for="(item, index) in userList"
            :key="index"
            class="rank-item relative flex items-center justify-between gap-3px"
          >
            <span
              class="flex items-center justify-between"
              :title="`${item.name} - ${item.value}`"
            >
              <span :class="['rank-number', index <= 3 ? 'top-' + (index + 1) : '']">
                {{ index + 1 }}
              </span>
              <AAvatar
                :size="20"
                :src="item.avatar"
                class="m-x-12px"
              >
                {{ item.name.charAt(0) }}
              </AAvatar>
              <span class="mr-3px truncate color-#666">{{ item.name }}</span>
            </span>
            <span class="truncate color-#666">{{ item.value }}</span>
          </div>
        </div>
      </div>
    </div>
  </CardPane>
</template>

<style scoped lang="less">
  .rank-number {
    width: 20px;
    height: 20px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    padding-top: 4px;
    color: #fff;
    background: url('@/assets/images/rank_other.png') no-repeat center center;
    background-size: auto 100%;
    &.top-1 {
      background: url('@/assets/images/rank_1.png') no-repeat center center;
      background-size: auto 100%;
    }

    &.top-2 {
      background: url('@/assets/images/rank_2.png') no-repeat center center;
      background-size: auto 100%;
    }

    &.top-3 {
      background: url('@/assets/images/rank_3.png') no-repeat center center;
      background-size: auto 100%;
    }
  }

  .rank-item {
    padding: 4px 7px;
    padding-right: 18px;
    margin: 4px 0;
    border-radius: 4px;
    transition: all 0.3s;

    &:nth-child(odd) {
      position: relative;
      &::after {
        border-right: solid 1px rgba(0, 0, 0, 0.05);
        position: absolute;
        top: -6px;
        right: -10px;
        width: 0;
        height: 38px;
        content: '';
      }
    }

    &:nth-child(even) {
      padding-left: 18px;
      padding-right: 14px;
    }

    &:hover {
    }
  }
</style>
