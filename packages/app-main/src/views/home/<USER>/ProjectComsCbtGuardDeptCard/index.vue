<script setup lang="tsx">
  import DateSelect from '../DateSelect/index.vue';

  defineProps<{
    height?: string | number;
  }>();

  const timeRange = ref('30');

  const reuseRank = [
    { dept: '数智事业部', isRepeat: false, reuse: 120, contribution: 120, cost: 50.5 },
    { dept: '工业互联网', isRepeat: true, reuse: 98, contribution: 98, cost: 42.3 },
    { dept: '农村农业事业部', isRepeat: false, reuse: 85, contribution: 85, cost: 38.7 },
    { dept: '要客部', isRepeat: true, reuse: 76, contribution: 76, cost: 35.2 },
    { dept: 'AI及大数据事业部', isRepeat: false, reuse: 65, contribution: 65, cost: 28.9 },
    { dept: '标品业务部', isRepeat: false, reuse: 58, contribution: 58, cost: 25.6 },
    { dept: '文商旅事业部', isRepeat: true, reuse: 45, contribution: 45, cost: 22.1 },
    { dept: '视频服务部', isRepeat: false, reuse: 32, contribution: 32, cost: 18.4 },
    { dept: '战新业务事业部', isRepeat: false, reuse: 12, contribution: 15, cost: 20.4 },
    { dept: '平台服务部', isRepeat: false, reuse: 21, contribution: 17, cost: 16.4 },
  ];

  const reuseRankColumns = [
    {
      title: '排名',
      key: 'rank',
      align: 'center' as const,
      width: 60,
      customRender: ({ index }: { index: number }) => {
        return index + 1;
      },
    },
    { title: '部门名称', dataIndex: 'dept', key: 'dept', width: 100, ellipsis: true },
    {
      title: '是否存在重复建设',
      key: 'isRepeat',
      align: 'center' as const,
      width: 120,
      customRender: ({ record }: { record: any }) => {
        return (
          <span style={{ color: record.isRepeat ? '#FF7102' : '#090C20' }}>
            {record.isRepeat ? '是' : '否'}
          </span>
        );
      },
    },
    {
      title: '复用组件',
      dataIndex: 'reuse',
      key: 'reuse',
      align: 'center' as const,
      width: 80,
      customRender: ({ record }: { record: any }) => {
        return <span>{record.reuse}</span>;
      },
    },
    {
      title: '贡献组件',
      dataIndex: 'contribution',
      key: 'contribution',
      align: 'center' as const,
      width: 80,
      customRender: ({ record }: { record: any }) => {
        return <span>{record.contribution}</span>;
      },
    },
    {
      title: '节约研发成本(万)',
      dataIndex: 'cost',
      key: 'cost',
      align: 'center' as const,
      width: 100,
      customRender: ({ record }: { record: any }) => {
        return <span style={{ fontWeight: 'bold' }}>{record.cost}</span>;
      },
    },
  ];
  const handleTimeRangeChange = (value: string) => {
    timeRange.value = value;
  };
</script>

<template>
  <CardPane
    title="组件贡献复用得分部门排行"
    :height="height"
    tip="组件复用"
  >
    <template #extra>
      <!-- <ASelect
        v-model:value="timeRange"
        class="rounded-select w-[180px]"
        :options="[
          { label: '近30天', value: '30' },
          { label: '近60天', value: '60' },
          { label: '近90天', value: '90' },
        ]"
        @change="handleTimeRangeChange"
      ></ASelect> -->
      <DateSelect
        v-model="timeRange"
        @change="handleTimeRangeChange"
      />
    </template>
    <ATable
      class="stat-table stat-table-compact"
      :data-source="reuseRank"
      :columns="reuseRankColumns"
      :pagination="false"
      size="small"
      :bordered="false"
      row-key="dept"
    />
  </CardPane>
</template>

<style scoped lang="less">
  .stat-table {
    :deep(.ant-table-thead > tr > th) {
      background: #fafafa;
      font-weight: 500;
    }
  }
</style>
