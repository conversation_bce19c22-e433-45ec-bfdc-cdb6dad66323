<script setup lang="ts">
  import dayjs from 'dayjs';
  import type { EChartsOption } from 'echarts';
  import { LineChart } from 'echarts/charts';
  import {
    GridComponent,
    LegendComponent,
    MarkLineComponent,
    TooltipComponent,
  } from 'echarts/components';
  import { use } from 'echarts/core';
  import { CanvasRenderer } from 'echarts/renderers';
  import { ref } from 'vue';
  import VChart from 'vue-echarts';

  import { getProjectQualityOfSM } from '@/apis/workbench';

  import ProjectSelect from '../ProjectSelect/index.vue';

  const project = ref('');

  // 注册必须的组件
  use([
    CanvasRenderer,
    LineChart,
    GridComponent,
    TooltipComponent,
    LegendComponent,
    MarkLineComponent,
  ]);
  const loading = ref(false);
  const data = ref({
    xAxis: [] as string[],
    series: [] as number[],
  });
  // 获取数据
  function getData() {
    loading.value = true;
    project.value &&
      getProjectQualityOfSM(project.value)
        .then((res) => {
          const list = res.data?.data || [];
          list.forEach((item) => {
            data.value.xAxis.push(dayjs(item.statisticsTime).format('YYYY-MM-DD'));
            data.value.series.push(Number(item.qualityScore || '0'));
          });
        })
        .finally(() => {
          loading.value = false;
        });
  }
  watch(
    () => project.value,
    () => {
      data.value.xAxis = [];
      data.value.series = [];
      getData();
    },
  );
  const option = computed<EChartsOption>(() => ({
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        return `${params[0].axisValue} ${Number(params[0].value).toFixed(2)}分`;
      },
    },
    legend: {
      show: false,
      // data: ['项目1', '项目2'],
      // right: 0,
      // top: 0,
    },
    grid: {
      top: 10,
      left: 10,
      right: 10,
      bottom: 10,
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: data.value.xAxis,
      axisLine: {
        lineStyle: {
          color: '#E5E6EB',
        },
      },
      axisTick: {
        show: false,
      },
    },
    yAxis: {
      type: 'value',
      max: 100,
      splitNumber: 5,
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: '#E5E6EB',
        },
      },
    },
    series: [
      {
        // name: '项目1',
        type: 'line',
        smooth: true,
        symbol: 'circle',
        symbolSize: 8,
        data: data.value.series,
        itemStyle: {
          color: '#4080FF',
        },

        markLine: {
          symbol: 'none',
          data: [{ yAxis: 60, name: '及格线' }],
          lineStyle: {
            color: '#D54941',
          },
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(64,128,255,0.2)',
              },
              {
                offset: 1,
                color: 'rgba(64,128,255,0)',
              },
            ],
          },
        },
      },
    ],
  }));
</script>

<template>
  <CardPane
    title="项目研发质量趋势"
    :loading="loading"
  >
    <template #extra>
      <ProjectSelect v-model="project" />
    </template>

    <VChart
      class="chart-container"
      :option="option"
      autoresize
    />
  </CardPane>
</template>

<style scoped lang="less">
  .chart-container {
    width: 100%;
    height: 100%;
  }
</style>
