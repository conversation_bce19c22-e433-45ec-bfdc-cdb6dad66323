<script setup lang="ts">
  import type { EChartsOption } from 'echarts';
  import { LineChart } from 'echarts/charts';
  import { GridComponent, LegendComponent, TooltipComponent } from 'echarts/components';
  import { use } from 'echarts/core';
  import { CanvasRenderer } from 'echarts/renderers';
  import { provide, ref } from 'vue';
  import VChart, { THEME_KEY } from 'vue-echarts';

  import { CardPane } from '@/components/Layout';

  // 注册必须的组件
  use([CanvasRenderer, LineChart, GridComponent, TooltipComponent, LegendComponent]);

  // 设置主题
  provide(THEME_KEY, 'light');

  // 过程符合度分析数据
  const processData = [
    {
      name: '需求阶段不符合项目',
      count: 52,
      // color: '#52c41a',
    },
    {
      name: '设计阶段不符合项目',
      count: 38,
      // color: '#1890ff',
    },
    {
      name: '开发阶段不符合项目',
      count: 165,
      color: '#fa8c16',
    },
    {
      name: '测试阶段不符合项目',
      count: 79,
      color: '#fa8c16',
    },
    {
      name: '发布阶段不符合项目',
      count: 32,
      // color: '#722ed1',
    },
  ];

  // 图表数据 - 模拟项目数据
  const projectData = [
    '2025.4.26',
    '2025.5.3',
    '2025.5.10',
    '2025.5.17',
    '2025.5.24',
    '2025.5.31',
    '2025.6.7',
    '2025.6.14',
    '2025.6.21',
    '2025.6.28',
  ];

  // 得分值数据 (蓝色线)
  const scoreData = [60, 64, 63, 65, 70, 75, 80, 85, 87, 90];

  // 符合率数据 (橙色线) - 与得分值有所差异，避免重复
  const complianceData = [63, 65, 64, 64, 65, 70, 75, 80, 85, 90];

  const chartOption = ref<EChartsOption>({
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        const data = params.map((item: any) => {
          return `${item.seriesName}: ${item.value}${item.seriesName === '符合率' ? '%' : '分'}`;
        });
        return `${params[0].axisValue}<br/>${data.join('<br/>')}`;
      },
    },
    // legend: {
    //   show: true,
    //   data: ['得分值: 98%', '符合率: 88%'],
    //   right: 20,
    //   top: 10,
    //   textStyle: {
    //     fontSize: 12,
    //     color: '#666',
    //   },
    // },
    grid: {
      top: 20,
      left: 40,
      right: 40,
      bottom: 20,
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: projectData,
      axisLine: {
        lineStyle: {
          color: '#E5E6EB',
        },
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        fontSize: 12,
        color: '#666',
        interval: 0,
        overflow: 'truncate',
        width: 140,
      },
    },
    yAxis: {
      type: 'value',
      max: 100,
      min: 0,
      splitNumber: 5,
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        lineStyle: {
          type: 'solid',
          color: '#f0f0f0',
        },
      },
      axisLabel: {
        fontSize: 11,
        color: '#666',
      },
    },
    series: [
      {
        name: '得分值',
        type: 'line',
        smooth: true,
        symbol: 'circle',
        symbolSize: 4,
        data: scoreData,
        itemStyle: {
          color: '#4080FF',
        },
        lineStyle: {
          width: 2,
          color: '#4080FF',
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(64,128,255,0.3)',
              },
              {
                offset: 1,
                color: 'rgba(64,128,255,0.05)',
              },
            ],
          },
        },
      },
      {
        name: '符合率',
        type: 'line',
        smooth: true,
        symbol: 'circle',
        symbolSize: 4,
        data: complianceData,
        itemStyle: {
          color: '#FF8C42',
        },
        lineStyle: {
          width: 2,
          color: '#FF8C42',
        },
        markLine: {
          symbol: 'none',
          data: [{ yAxis: 60, name: '及格线' }],
          lineStyle: {
            color: '#D54941',
          },
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(255,140,66,0.3)',
              },
              {
                offset: 1,
                color: 'rgba(255,140,66,0.05)',
              },
            ],
          },
        },
      },
    ],
  });
</script>

<template>
  <CardPane title="过程符合度分析">
    <!-- 原有的过程符合度数据展示 -->
    <div class="process-compliance-container">
      <div class="process-card">
        <div class="process-items">
          <div
            v-for="(item, index) in processData"
            :key="item.name"
            class="process-item"
          >
            <div
              class="process-count"
              :style="{ color: item.color }"
            >
              {{ item.count }}
              <span class="count-unit">个</span>
            </div>
            <div class="process-name">{{ item.name }}</div>
            <!-- 分隔线，最后一项不显示 -->
            <div
              v-if="index < processData.length - 1"
              class="process-divider"
            ></div>
          </div>
        </div>
      </div>
    </div>
    <div class="mb-4 flex items-center text-4 font-500">
      <img
        src="@/assets/images/icon_card.png"
        alt=""
        class="mr-3 h-22px w-22px"
      />
      过程符合趋势
    </div>
    <!-- 新增的图表区域 -->
    <div class="chart-section">
      <VChart
        class="chart-container"
        :option="chartOption"
        autoresize
      />
    </div>
  </CardPane>
</template>

<style lang="less" scoped>
  .process-compliance-container {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px 0;
    margin-bottom: 20px;
  }

  .process-card {
    background-color: rgba(230, 233, 244, 0.14);
    border-radius: 10px;
    padding: 30px 20px;
    width: 100%;
    border: 1px solid rgba(174, 174, 174, 0.14);
  }

  .process-items {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    position: relative;
  }

  .process-item {
    flex: 1;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    position: relative;
  }

  .process-count {
    font-size: 32px;
    font-weight: 600;
    line-height: 1;
    display: flex;
    align-items: baseline;
    justify-content: center;
    gap: 4px;
  }

  .count-unit {
    font-size: 14px;
    color: #999;
    font-weight: normal;
  }

  .process-name {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.65);
    line-height: 1.4;
    white-space: nowrap;
  }

  .process-divider {
    position: absolute;
    right: -20px;
    top: 50%;
    transform: translateY(-50%);
    width: 1px;
    height: 60px;
    background-color: #d9d9d9;
  }

  .chart-section {
  }

  .chart-container {
    width: 100%;
    height: 320px;
  }

  @media (max-width: 1200px) {
    .process-card {
      padding: 25px 15px;
    }

    .process-count {
      font-size: 28px;
    }

    .process-name {
      font-size: 13px;
    }

    .process-divider {
      right: -15px;
      height: 50px;
    }
  }

  @media (max-width: 768px) {
    .process-card {
      padding: 20px 15px;
    }

    .process-items {
      flex-direction: column;
      gap: 20px;
    }

    .process-item {
      flex-direction: row;
      justify-content: space-between;
      width: 100%;
      text-align: left;
      padding: 15px 0;
      border-bottom: 1px solid #d9d9d9;
    }

    .process-item:last-child {
      border-bottom: none;
    }

    .process-count {
      font-size: 24px;
    }

    .process-divider {
      display: none;
    }
  }
</style>
