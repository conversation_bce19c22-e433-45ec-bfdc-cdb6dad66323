<script setup lang="ts">
  import { SwapOutlined } from '@ant-design/icons-vue';
  import type { Descriptions } from 'ant-design-vue';

  import api, { type UserInfoFull } from '@/apis';
  import { getTodos, type Todo } from '@/apis/workbench';
  import userDefaultAvatar from '@/layouts/assets/images/user-default.jpg';

  interface ICustom {
    isWarning?: boolean;
    icon?: string;
    unit?: string;
    todoMatterSubName?: string;
    todoMatterSubNum?: number;
    todoMatterSubUnit?: string;
    todoCompare?: {
      label?: string;
      value?: number;
      unit?: string;
    }[];
  }

  const props = withDefaults(
    defineProps<{
      title?: string;
      userInfo?: boolean;
      todos?: number[];
    }>(),
    {
      title: '', // 设置默认值
      userInfo: false,
      todos: () => [], // 卡片id集合
    },
  );

  const slots = defineSlots<{
    default: (props: any) => any;
    title?: (props: any) => any;
    content?: (props: any) => any;
    extraContent?: (props: any) => any;
    extra?: (props: any) => any;
    footer?: (props: any) => any;
  }>();

  // const currentUser = ref({} as UserInfoFull);
  // api.getUserAllInfo().then((res) => {
  //   currentUser.value = res.data.data || ({} as UserInfoFull);
  //   // currentUser.value.roles = currentUser.value.roles?.split(',')[0] || '';
  // });

  const defaultUnit = '%';
  const upColor = '#EE6060';
  const downColor = '#49CE20';
  const todosMap = ref<Record<string, Todo & ICustom>>({
    0: {
      todoMatterId: 0,
      todoMatterName: '逾期迭代',
      todoMatterNum: 0,
      isWarning: false,
      icon: new URL('../../icons/yqdd.svg', import.meta.url).href,
    },
    1: {
      todoMatterId: 1,
      todoMatterName: '逾期需求',
      todoMatterNum: 0,
      isWarning: false,
      icon: new URL('../../icons/yqxq.svg', import.meta.url).href,
    },
    2: {
      todoMatterId: 2,
      todoMatterName: '逾期任务',
      todoMatterNum: 0,
      isWarning: false,
      icon: new URL('../../icons/yqrw.svg', import.meta.url).href,
    },
    3: {
      todoMatterId: 3,
      todoMatterName: '安全漏洞',
      todoMatterNum: 0,
      isWarning: false,
      icon: new URL('../../icons/aqld.svg', import.meta.url).href,
    },
    4: {
      todoMatterId: 4,
      todoMatterName: '未关闭缺陷',
      todoMatterNum: 0,
      isWarning: false,
      icon: new URL('../../icons/wgbqx.svg', import.meta.url).href,
    },

    5: {
      todoMatterId: 5,
      todoMatterName: '项目',
      todoMatterNum: 93,
      isWarning: false,
      icon: new URL('../../icons/xm.svg', import.meta.url).href,
    },
    6: {
      todoMatterId: 6,
      todoMatterName: '产品',
      todoMatterNum: 33,
      isWarning: false,
      icon: new URL('../../icons/cp.svg', import.meta.url).href,
    },
    7: {
      todoMatterId: 7,
      todoMatterName: '里程碑',
      todoMatterNum: 1,
      isWarning: false,
      icon: new URL('../../icons/lcb.svg', import.meta.url).href,
    },
    8: {
      todoMatterId: 8,
      todoMatterName: '逾期的需求',
      todoMatterNum: 3,
      isWarning: false,
      icon: new URL('../../icons/yqdxq.svg', import.meta.url).href,
    },
    9: {
      todoMatterId: 9,
      todoMatterName: '未修复缺陷',
      todoMatterNum: 2,
      isWarning: false,
      icon: new URL('../../icons/wgbqx.svg', import.meta.url).href,
    },

    10: {
      todoMatterId: 10,
      todoMatterName: '逾期项目',
      todoMatterNum: 2,
      isWarning: false,
      icon: new URL('../../icons/xm.svg', import.meta.url).href,
    },
    11: {
      todoMatterId: 11,
      todoMatterName: 'AI使用得分',
      todoMatterNum: 78,
      isWarning: false,
      icon: new URL('../../icons/cp.svg', import.meta.url).href,
    },
    12: {
      todoMatterId: 12,
      todoMatterName: 'AI使用排名',
      todoMatterNum: 3,
      isWarning: false,
      icon: new URL('../../icons/lcb.svg', import.meta.url).href,
    },
    13: {
      todoMatterId: 13,
      todoMatterName: '组件复用得分',
      todoMatterNum: 21,
      isWarning: false,
      icon: new URL('../../icons/yqdxq.svg', import.meta.url).href,
    },
    14: {
      todoMatterId: 14,
      todoMatterName: '组件复用排名',
      todoMatterNum: 7,
      isWarning: false,
      icon: new URL('../../icons/wgbqx.svg', import.meta.url).href,
    },
    15: {
      todoMatterId: 15,
      todoMatterName: '人均研发贡献得分',
      todoMatterNum: 55,
      isWarning: false,
      icon: new URL('../../icons/yqdxq.svg', import.meta.url).href,
    },
    16: {
      todoMatterId: 16,
      todoMatterName: '研发贡献排名',
      todoMatterNum: 6,
      isWarning: false,
      icon: new URL('../../icons/wgbqx.svg', import.meta.url).href,
    },

    17: {
      todoMatterId: 17,
      todoMatterName: '过程符合度',
      todoMatterNum: 22,
      todoCompare: [
        {
          label: '周同比',
          value: 12,
        },
        {
          label: '日环比',
          value: -10,
        },
      ],
      isWarning: false,
      icon: new URL('../../icons/gcfhd.svg', import.meta.url).href,
    },
    18: {
      todoMatterId: 18,
      todoMatterName: '研发质量',
      todoMatterNum: 7,
      todoCompare: [
        {
          label: '周同比',
          value: 12,
        },
        {
          label: '日环比',
          value: -10,
        },
      ],
      isWarning: false,
      icon: new URL('../../icons/yfzl.svg', import.meta.url).href,
    },
    19: {
      todoMatterId: 19,
      todoMatterName: '逾期督办',
      todoMatterNum: 0,
      isWarning: false,
      icon: new URL('../../icons/yqdb.svg', import.meta.url).href,
    },
    20: {
      todoMatterId: 20,
      todoMatterName: '未登记工时',
      todoMatterNum: 0,
      isWarning: false,
      icon: new URL('../../icons/wdjgs.svg', import.meta.url).href,
    },

    21: {
      todoMatterId: 21,
      todoMatterName: '逾期项目',
      todoMatterNum: 0,
      isWarning: false,
      unit: '分',
      icon: new URL('../../icons/xm.svg', import.meta.url).href,
      todoCompare: [
        {
          label: '周同比',
          value: 12,
        },
        {
          label: '日环比',
          value: -10,
        },
      ],
    },
    22: {
      todoMatterId: 22,
      todoMatterName: 'AI提效比',
      todoMatterNum: 93,
      isWarning: false,
      unit: '%',
      icon: new URL('../../icons/aitxb.svg', import.meta.url).href,
      todoCompare: [
        {
          label: '周同比',
          value: 12,
        },
        {
          label: '日环比',
          value: -10,
        },
      ],
    },
    23: {
      todoMatterId: 23,
      todoMatterName: '组件复用节约',
      todoMatterNum: 0,
      isWarning: false,
      unit: '万元',
      icon: new URL('../../icons/zjfyjy.svg', import.meta.url).href,
      todoCompare: [
        {
          label: '周同比',
          value: 12,
        },
        {
          label: '日环比',
          value: -10,
        },
      ],
    },
    24: {
      todoMatterId: 24,
      todoMatterName: '人均研发贡献',
      todoMatterNum: 0,
      isWarning: false,
      unit: '分',
      icon: new URL('../../icons/rjyfgx.svg', import.meta.url).href,
      todoMatterSubName: '研发岗人员',
      todoMatterSubNum: 930,
      todoMatterSubUnit: '人',
      todoCompare: [
        {
          label: '周同比',
          value: 12,
        },
        {
          label: '日环比',
          value: -10,
        },
      ],
    },
    25: {
      todoMatterId: 25,
      todoMatterName: '整体研发符合度',
      todoMatterNum: 0,
      isWarning: false,
      unit: '%',
      icon: new URL('../../icons/ztyffhd.svg', import.meta.url).href,
      todoCompare: [
        {
          label: '周同比',
          value: 12,
        },
        {
          label: '日环比',
          value: -10,
        },
      ],
    },
  });

  const todoLoading = ref(false);
  const todoCardsData = ref<(Todo & ICustom)[]>([]);
  const getTodoCardsData = async () => {
    todoLoading.value = true;
    let todosData: (Todo & ICustom)[] = [];
    try {
      const res = await getTodos();
      todosData = res.data?.data || [];
    } catch (error) {
      console.error('Failed to fetch todos:', error);
    }
    todoLoading.value = false;
    todoCardsData.value = props.todos.map((id) => {
      const todoMap = todosMap.value[id];
      const data = todosData.find((item) => item.todoMatterId === id) || null;
      const todoData = {
        ...todoMap,
        ...data,
      };
      todoData.isWarning = Number(todoData?.todoMatterNum) > 10;
      return todoData;
    });
  };

  // 获取卡片数据
  getTodoCardsData().then(() => {
    console.log('todoCardsData', todoCardsData.value);
  });
</script>

<template>
  <div class="pageContainer m-unset max-w-unset w-full">
    <div
      class="mb-[24px] min-h-147px flex items-center justify-between rounded-[10px] bg-[rgba(255,255,255,0.6)] p-[24px] shadow-[0px_0px_6px_0px_rgba(0,0,0,0.1037)]"
    >
      <div>
        <slot name="extra" />
      </div>
      <div class="h-full w-full flex items-center justify-between pl-24px">
        <!-- <div
          v-if="userInfo"
          class="max-w-[30%] flex-auto"
        >
          <slot name="content">
            <div class="headerContent">
              <div class="avatar">
                <AAvatar
                  size="large"
                  :src="currentUser.avatar || userDefaultAvatar"
                />
              </div>
              <div class="content">
                <div class="contentTitle">你好，{{ currentUser.nickName }}</div>
                <div>
                  <span class="mr-[16px]">
                    {{ currentUser.roles }}
                  </span>
                  <span>{{ currentUser.deptName }}</span>
                </div>
              </div>
            </div>
          </slot>
        </div> -->
        <div
          class="headerExtraContent flex flex-1 items-center"
          :class="[userInfo ? 'justify-end' : 'justify-between flex-wrap w-full']"
        >
          <slot
            v-if="!todoLoading"
            name="extraContent"
          >
            <AStatistic
              v-for="item in todoCardsData"
              :key="item.todoMatterId"
              :value="item.todoMatterNum"
              :style="{ width: `${(1 / todos.length) * 100}%` }"
            >
              <template #title>
                <div class="h-52px w-52px rounded-50% bg-[rgba(0,0,0,.1)]">
                  <img
                    :src="item.icon"
                    class="h-5/5 w-full object-cover"
                  />
                </div>
              </template>
              <template #prefix>
                <span
                  :style="{
                    color: item.isWarning ? '#FF7102' : '#4B5070',
                  }"
                >
                  {{ item.todoMatterNum }}
                  <span
                    v-if="item.unit"
                    class="relative text-14px"
                    :class="{ 'text-30px left-[-5px]': item.unit == '%' }"
                  >
                    {{ item.unit }}
                  </span>
                </span>

                <span
                  v-if="item.todoMatterSubNum"
                  class="pl-24px"
                  :style="{
                    color: item.isWarning ? '#FF7102' : '#4B5070',
                  }"
                >
                  {{ item.todoMatterSubNum }}
                  <span
                    v-if="item.unit"
                    class="relative text-14px"
                    :class="{ 'text-30px left-[-5px]': item.unit == '%' }"
                  >
                    {{ item.todoMatterSubUnit }}
                  </span>
                </span>
              </template>
              <template #formatter>
                <span>
                  {{ item.todoMatterName }}
                </span>
                <span
                  v-if="item.todoMatterSubName"
                  class="pl-24px"
                >
                  {{ item.todoMatterSubName }}
                </span>
              </template>
              <template
                v-if="item.todoCompare?.length"
                #suffix
              >
                <ASpace
                  v-for="(cp, index) in item.todoCompare"
                  :key="index"
                >
                  <span class="stat-trend-label">{{ cp.label }}</span>
                  <span
                    :style="{ color: Number(cp.value) > 0 ? upColor : downColor }"
                    class="whitespace-nowrap"
                  >
                    {{ cp.value }}{{ cp.unit || defaultUnit }}
                    <CaretUpOutlined v-if="Number(cp.value) > 0" />
                    <CaretDownOutlined v-else />
                  </span>
                </ASpace>
              </template>
            </AStatistic>
          </slot>
          <div
            v-if="todoLoading"
            class="h-full w-full flex flex-wrap justify-between"
          >
            <ASkeleton
              v-for="todoId in todos"
              :key="todoId"
              active
              avatar
              :paragraph="{ rows: 1 }"
              class="flex-1 pl-5%"
              :style="{ maxWidth: `${(1 / todos.length) * 100}%` }"
            />
          </div>
        </div>
      </div>
      <slot name="footer" />
    </div>
    <div class="flex flex-1 flex-col">
      <slot />
    </div>
  </div>
</template>

<style lang="less" scoped>
  .textOverflow() {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    word-break: break-all;
  }

  .clearfix() {
    zoom: 1;

    &::before,
    &::after {
      display: table;
      content: ' ';
    }

    &::after {
      clear: both;
      height: 0;
      font-size: 0;
      visibility: hidden;
    }
  }

  .pageContainer {
    :deep(.headerContent) {
      display: flex;

      .avatar {
        flex: 0 1 72px;

        & > span {
          display: block;
          width: 72px;
          height: 72px;
          border-radius: 72px;
        }
      }

      .content {
        position: relative;
        top: 4px;
        flex: 1 1 auto;
        margin-left: 24px;
        line-height: 22px;
        min-width: 200px;

        .contentTitle {
          margin-bottom: 12px;
          font-weight: 500;
          font-size: 20px;
          line-height: 28px;
        }
      }
    }

    :deep(.headerExtraContent) {
      .statItem,
      .ant-statistic {
        border-radius: 4px;
        height: 100%;
        position: relative;
        padding: 2px 7px;
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;

        &::after {
          border-left: solid 1px #cacacf;
          position: absolute;
          top: 50%;
          left: 0;
          width: 0;
          height: 31px;
          margin-top: -16px;
          content: '';
        }

        &:first-of-type {
          padding-left: 0;

          &::after {
            display: none;
          }
        }

        .ant-statistic-title {
          font-size: 16px;
          font-weight: normal;
          line-height: 16px;
          letter-spacing: normal;
          color: #4b5071;
          margin: 0;
          // margin-left: -52px;
          margin-right: 7%;
        }

        .ant-statistic-content {
          font-size: 14px;
          letter-spacing: normal;
          color: #4b5071;
          display: flex;
          align-items: baseline;
          justify-content: flex-start;
          flex-direction: column;

          > span {
            line-height: 24px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            display: block;
          }

          .ant-statistic-content-prefix {
            width: 100%;
            font-size: 30px;
            font-weight: 500;
            line-height: 36px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 0;
          }

          .ant-statistic-content-value {
            width: 100%;
            font-size: 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
          }

          .ant-statistic-content-suffix {
            font-size: 12px;
            line-height: 22px;
            margin-bottom: -7px;

            .ant-space {
              font-size: inherit;

              &:first-of-type {
                margin-right: 12px;
              }
            }
          }
        }
      }
    }
  }

  @media screen and (max-width: 1600px) {
    .pageContainer {
      :deep(.headerExtraContent) {
        .ant-statistic {
          .ant-statistic-title {
            font-size: 12px;
          }
        }
      }
    }
  }
</style>
