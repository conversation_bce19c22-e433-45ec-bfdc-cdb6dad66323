<script setup lang="ts">
  import type { EChartsOption } from 'echarts';
  import { RadarChart } from 'echarts/charts';
  import {
    GridComponent,
    LegendComponent,
    TitleComponent,
    TooltipComponent,
  } from 'echarts/components';
  import { use } from 'echarts/core';
  import { CanvasRenderer } from 'echarts/renderers';
  import VChart from 'vue-echarts';

  import { type DevContributionOfProject, getDevContributionOfProject } from '@/apis/workbench';

  import ProjectSelect from '../ProjectSelect/index.vue';

  use([
    RadarChart,
    TitleComponent,
    TooltipComponent,
    LegendComponent,
    GridComponent,
    CanvasRenderer,
  ]);
  const loading = ref(false);
  const project = ref('');
  const userList = ref<DevContributionOfProject[]>([]);
  let color = ['#5B8FF9', '#FFAB1A', '#4ACE21'];
  // 获取数据
  function getData() {
    loading.value = true;
    project.value &&
      getDevContributionOfProject({
        pageIndex: 1,
        pageSize: 12,
        programmeId: project.value,
      })
        .then((res) => {
          userList.value = res.data?.data || [];
          selectUser.value = userList.value.slice(0, 3).map((item, index) => ({
            ...item,
            color: color[index],
          }));
        })
        .finally(() => {
          loading.value = false;
        });
  }
  watch(
    () => project.value,
    () => {
      userList.value = [];
      selectUser.value = [];
      color = ['#5B8FF9', '#FFAB1A', '#4ACE21'];
      getData();
    },
  );
  const selectUser = ref<(DevContributionOfProject & { color: string })[]>([]);
  function handleSelect(user: DevContributionOfProject) {
    const findIndex = selectUser.value.findIndex((item) => item.id === user.id);
    if (findIndex !== -1) {
      selectUser.value.splice(findIndex, 1);
      color.push(color.splice(findIndex, 1)[0]);
    } else {
      if (selectUser.value.length >= 3) {
        // _color = color[0];
        selectUser.value.shift();
        color.push(color.splice(0, 1)[0]);
      }
      let _color = color[selectUser.value.length];
      selectUser.value.push({
        ...user,
        color: _color,
      });
    }
  }
  // 维度
  const indicator = [
    { name: '研发任务', max: 10, color: '#027FFF' },
    { name: '修复Bug', max: 10, color: '#30C25B' },
    { name: '有效代码', max: 10, color: '#FF00ED' },
    { name: '构建流水线', max: 10, color: '#8F7EFF' },
  ];

  const chartOption = computed<EChartsOption>(() => {
    let data = [] as any[];
    selectUser.value.forEach((item, index) => {
      let itemData = {
        value: [item.tasksCount, item.fixBugsCount, item.effectiveCodeCount, item.pipelineCount],
        name: item.userName,
        itemStyle: {
          color: item.color,
        },
      };
      itemData.value.forEach((_item, _index) => {
        indicator[_index].max = Math.max(indicator[_index].max, _item);
      });
      data.push(itemData);
    });
    return {
      tooltip: {
        trigger: 'item',
        borderWidth: 0,
        formatter: (params: any) => {
          const indicators = indicator || [];
          const values = params.value;
          let html = `<div style="font-weight: bold; margin-bottom: 10px;border-bottom: 1px solid #E5E8EF;padding-bottom: 4px;text-align: center;">${params.name}</div>`;
          indicators.forEach((indicator: any, index: number) => {
            html += `
            <div style="display: flex;  margin: 4px 0;">
              <span style="color: ${indicator.color};transform: scale(0.4);">●</span>
              <span>${indicator.name}:</span>
              <span style="font-weight: bold;margin-left: 4px;">${values[index]}</span>
            </div>
          `;
          });
          return html;
        },
      },
      radar: {
        indicator: indicator.map((item) => ({
          name: item.name,
          max: item.max,
        })),
        radius: '70%',
        shape: 'circle',
        splitNumber: 4,
        axisLine: {
          show: true,
          lineStyle: {
            color: '#E5E8EF',
            width: 1,
          },
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#E5E8EF',
            width: 1,
            type: 'solid',
          },
        },
        splitArea: {
          show: false,
        },
        axisName: {
          color: '#090C20',
          fontSize: 12,
        },
      },
      series: [
        {
          type: 'radar',
          areaStyle: {
            opacity: 0,
          },
          lineStyle: {
            shadowColor: 'rgba(16, 46, 117, 0.4)',
            shadowBlur: 8,
            shadowOffsetY: 4,
          },
          data,
        },
      ],
    };
  });
  function getColor(item: DevContributionOfProject) {
    const findItem = selectUser.value.find((i) => i.id === item.id);
    return findItem?.color || 'none';
  }
</script>

<template>
  <CardPane
    title="研发人员贡献度"
    :loading="loading"
  >
    <template #extra>
      <ProjectSelect v-model="project" />
    </template>
    <div class="flex flex-col">
      <div class="chart-container flex-1">
        <VChart
          class="h-320px"
          :option="chartOption"
          autoresize
        />
      </div>
      <div class="rank-list w-full pl-4">
        <div class="grid grid-cols-3 gap-2">
          <div
            v-for="(item, index) in userList"
            :key="index"
            class="rank-item flex cursor-pointer items-center gap-1"
            @click="handleSelect(item)"
          >
            <span :class="['rank-number', index <= 3 ? 'top-' + (index + 1) : '']">
              {{ index + 1 }}
            </span>
            <AAvatar
              :size="20"
              :src="item.userAvatar"
            >
              {{ item.userName?.charAt(0) }}
            </AAvatar>
            <span class="truncate color-#666">{{ item.userName }}</span>
            <span
              class="h-2 w-2 rounded-full"
              :style="`background: ${getColor(item)}`"
            ></span>
          </div>
        </div>
      </div>
    </div>
  </CardPane>
</template>

<style scoped lang="less">
  .rank-number {
    width: 20px;
    height: 20px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    padding-top: 4px;
    color: #fff;
    background: url('@/assets/images/rank_other.png') no-repeat center center;
    background-size: auto 100%;

    &.top-1 {
      background: url('@/assets/images/rank_1.png') no-repeat center center;
      background-size: auto 100%;
    }

    &.top-2 {
      background: url('@/assets/images/rank_2.png') no-repeat center center;
      background-size: auto 100%;
    }

    &.top-3 {
      background: url('@/assets/images/rank_3.png') no-repeat center center;
      background-size: auto 100%;
    }
  }

  .rank-item {
    padding: 4px;
    margin: 2px 0;
    border-radius: 4px;
    transition: all 0.3s;

    &:hover {
    }
  }
</style>
