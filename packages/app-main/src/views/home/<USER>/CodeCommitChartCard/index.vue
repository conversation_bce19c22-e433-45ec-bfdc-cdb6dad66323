<script setup lang="ts">
  import type { EChartsOption } from 'echarts';
  import { LineChart } from 'echarts/charts';
  import {
    GridComponent,
    LegendComponent,
    MarkLineComponent,
    TooltipComponent,
  } from 'echarts/components';
  import { use } from 'echarts/core';
  import { CanvasRenderer } from 'echarts/renderers';
  import { provide, ref } from 'vue';
  import VChart, { THEME_KEY } from 'vue-echarts';

  import DateSelect from '../DateSelect/index.vue';

  // 注册必须的组件
  use([
    CanvasRenderer,
    LineChart,
    GridComponent,
    TooltipComponent,
    LegendComponent,
    MarkLineComponent,
  ]);

  // 设置主题
  provide(THEME_KEY, 'light');

  const timeRange = ref('30');
  const handleTimeRangeChange = (value: any) => {
    timeRange.value = value;
  };

  const option = ref<EChartsOption>({
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        const data = params.map((item: any) => {
          return `${item.seriesName}: ${item.value}`;
        });
        return `${params[0].axisValue}<br/>${data.join('<br/>')}`;
      },
    },
    legend: {
      show: false,
      // data: ['项目1', '项目2'],
      // right: 0,
      // top: 0,
    },
    grid: {
      top: 10,
      left: 10,
      right: 10,
      bottom: 10,
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['2025.05.01', '2025.05.15', '2025.05.20', '2025.05.25', '2025.05.30'],
      axisLine: {
        lineStyle: {
          color: '#E5E6EB',
        },
      },
      axisTick: {
        show: false,
      },
    },
    yAxis: {
      type: 'value',
      max: 100,
      splitNumber: 5,
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: '#E5E6EB',
        },
      },
    },
    series: [
      {
        name: '有效代码行数',
        type: 'line',
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        data: [40, 70, 50, 80, 70],
        itemStyle: {
          color: '#4080FF',
        },
        // markLine: {
        //   data: [{ type: 'average', name: 'Avg' }],
        // },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(64,128,255,0.2)',
              },
              {
                offset: 1,
                color: 'rgba(64,128,255,0)',
              },
            ],
          },
        },
      },
    ],
  });
</script>

<template>
  <CardPane
    title="代码提交趋势图"
    class="quality-card"
  >
    <template #extra>
      <DateSelect
        v-model="timeRange"
        @change="handleTimeRangeChange"
      />
    </template>

    <VChart
      class="chart-container"
      :option="option"
      autoresize
    />
  </CardPane>
</template>

<style scoped lang="less">
  .chart-container {
    width: 100%;
    height: 100%;
  }
</style>
