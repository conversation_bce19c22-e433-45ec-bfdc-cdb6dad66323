<script setup lang="ts">
  import VChart from 'vue-echarts';

  defineProps<{
    height?: string | number;
  }>();

  const trendOption = {
    tooltip: {},
    xAxis: {
      type: 'category',
      data: ['代码助手', '文档助手', '测试助手', '工程助手', '问答'],
      axisLabel: { fontSize: 12, margin: 16 },
      axisLine: {
        lineStyle: {
          color: 'rgba(196,197,197,1)',
        },
      },
    },
    yAxis: {
      type: 'value',
      axisLabel: { fontSize: 12 },
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
          color: 'rgba(196,197,197,1)',
        },
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(196,197,197,1)',
        },
      },
    },
    series: [
      {
        data: [80, 70, 60, 50, 100],
        type: 'bar',
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#A2E6FF' },
              { offset: 1, color: '#3173FF' },
            ],
          },
          borderRadius: [6, 6, 0, 0],
        },
        // markLine: {
        //   data: [{ type: 'average', name: 'Avg' }],
        // },
        barWidth: 15,
      },
    ],
    grid: { left: 30, right: 20, top: 30, bottom: 30 },
  };
</script>

<template>
  <CardPane
    title="应用人数分布"
    :height="height"
  >
    <VChart
      :option="trendOption"
      autoresize
      style="height: 100%; width: 100%"
    />
  </CardPane>
</template>
