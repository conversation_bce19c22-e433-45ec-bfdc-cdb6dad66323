<script setup lang="ts">
  import { CaretDownOutlined, CaretUpOutlined, InfoCircleOutlined } from '@ant-design/icons-vue';
  import CountUp from 'vue-countup-v3';

  import { useBreakpoint } from '@/hooks/useBreakpoint';

  // 使用自定义断点 hook
  const { isCustom } = useBreakpoint();

  // 根据断点计算网格列数
  const statMlClass = computed(() => {
    if (isCustom('1920')) {
      return 'ml-4';
    }
    return '';
  });
  const topStats = [
    {
      title: '总应用人数',
      value: 1291,
      suffix: '人',
      valueColor: '#1677ff',
      info: '总应用人数说明',
      month: { value: 12, up: true, color: '#52c41a' },
    },
    {
      title: '总应用次数',
      value: 89672,
      suffix: '次',
      valueColor: '#1677ff',
      // unitColor: '#1677ff',
      info: '总应用次数说明',
      month: { value: 8.3, up: true, color: '#52c41a' },
    },
    {
      title: '平均使用频率',
      value: 69,
      suffix: '次/月',
      valueColor: '#FF7102',
      info: '平均使用频率说明',
      month: { value: -2.1, up: true, color: '#52c41a' },
    },
    {
      title: '活跃度指数',
      value: 86.5,
      suffix: '分',
      valueColor: '#49CE20',
      // unitColor: '#49CE20',
      info: '活跃度指数说明',
      month: { value: 5.2, up: true, color: '#52c41a' },
    },
  ];
</script>

<template>
  <div class="top-stats-flex">
    <div
      v-for="(stat, idx) in topStats"
      :key="idx"
      class="stat-card base-card-pane"
    >
      <!-- 单项卡片 -->
      <div>
        <div class="stat-title">
          <span
            class="truncate"
            :title="stat.title"
          >
            {{ stat.title }}
          </span>
          <ATooltip
            :title="stat.info"
            color="#fff"
            :overlay-inner-style="{
              color: '#000',
            }"
          >
            <InfoCircleOutlined class="ml-2 align-middle text-gray-400" />
          </ATooltip>
        </div>
        <div class="stat-value">
          <CountUp
            :end-val="Number(stat.value)"
            :duration="1.2"
            :style="{ color: stat.valueColor }"
            class="main-num"
          />
          <span
            class="stat-suffix"
            :style="{ color: stat.unitColor }"
          >
            {{ stat.suffix }}
          </span>
        </div>
      </div>
      <div class="stat-divider"></div>
      <div class="stat-trend-row">
        <ASpace>
          <span class="stat-trend-label">较上月{{ stat.month.value >= 0 ? '增长' : '下降' }}</span>
          <span
            :style="{ color: stat.month.color }"
            class="whitespace-nowrap"
          >
            {{ Math.abs(stat.month.value) }}%
            <CaretUpOutlined v-if="stat.month.up" />
            <CaretDownOutlined v-else />
          </span>
        </ASpace>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
  .top-stats-flex {
    display: flex;
    gap: 16px;
  }

  .stat-card {
    flex: 1 1 20%;
    min-width: 0;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 12px;
    box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.04);
    padding: 20px;
    padding-bottom: 16px;
    display: flex;
    flex-direction: column;
  }

  .double-stat-card {
    justify-content: space-between;

    .double-stat-info-wrapper {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 20px;

      .double-stat-item {
        flex: 1;
        display: flex;
        flex-direction: column;
        min-width: 0;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;

        &:first-child {
          align-items: flex-start;
        }

        &:last-child {
          align-items: flex-end;
        }
      }
    }
  }

  .stat-title {
    font-size: 15px;
    color: #222;
    font-weight: 500;
    display: flex;
    align-items: center;
    width: 100%;

    &.right {
      justify-content: flex-end;
    }
  }

  .stat-value {
    font-size: 36px;
    font-weight: bold;
    margin-top: 20px;
    margin-bottom: 20px;
    display: flex;
    align-items: baseline;
    line-height: 1;
    justify-content: start;
  }

  .stat-value .main-num {
    line-height: 1;
    display: inline-block;
  }

  .stat-suffix {
    font-size: 18px;
    margin-left: 2px;
    font-weight: normal;
  }

  .stat-divider {
    width: 100%;
    border-bottom: 1px dashed #e5e6eb;
    margin: 12px 0 8px 0;
  }

  .stat-trend-row {
    width: 100%;
    display: flex;
    // padding-left: 10px;
    // padding-right: 10px;
    align-items: center;
    justify-content: space-between;
    font-size: 13px;
    margin-top: 10px;
    gap: 8px;
  }

  .stat-trend-label {
    color: #888;
    margin-right: 2px;
    white-space: nowrap;
  }

  @media (max-width: 1366px) {
    .top-stats-flex {
      gap: 8px;
    }

    .stat-card {
      padding: 10px 4px 6px 4px;
    }

    .stat-title {
      font-size: 13px;
    }

    .stat-value {
      font-size: 22px;
    }

    .stat-suffix {
      font-size: 12px;
    }
  }
</style>
