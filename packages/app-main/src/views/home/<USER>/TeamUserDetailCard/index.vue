<script setup lang="tsx">
  import { ref } from 'vue';

  import { getProjectList } from '@/apis/project/project';

  const data = ref([
    {
      name: '王文兵',
      tool: 'CodeFree',
      monthUseCount: 342,
      mainUseScene: '产品文档撰写',
      useTime: 23.5,
    },
    {
      name: '潘荣安',
      tool: 'CodeFree代码助手',
      monthUseCount: 289,
      mainUseScene: '代码开发、BUG修复',
      useTime: 51,
    },
    {
      name: '赖勇',
      tool: '智能体',
      monthUseCount: 276,
      mainUseScene: '市场分析、数据可视化',
      useTime: 60,
    },
    {
      name: '雷尚林',
      tool: '文档助手',
      monthUseCount: 234,
      mainUseScene: '需求分析、方案撰写',
      useTime: 30,
    },
    {
      name: '刘伟',
      tool: '测试助手',
      monthUseCount: 254,
      mainUseScene: '测试用例编写、单元测试',
      useTime: 56,
    },
    {
      name: '游开兴',
      tool: '安全助手',
      monthUseCount: 56,
      mainUseScene: '安全漏洞扫描、漏洞修复',
      useTime: 12.5,
    },
  ]);
  const columns = ref([
    {
      title: '成员姓名',
      dataIndex: 'name',
    },
    {
      title: '常用AI工具',
      dataIndex: 'tool',
    },
    {
      title: '本月使用次数',
      dataIndex: 'monthUseCount',
    },
    {
      title: '主要使用场景',
      dataIndex: 'mainUseScene',
    },
    {
      title: '使用时长',
      dataIndex: 'useTime',
      customRender: ({ record }) => {
        return (
          <span style={{ color: record.useTime >= 50 ? '#49CE20' : '#4B5071' }}>
            {record.useTime} 小时
          </span>
        );
      },
    },
    // {
    //   title: '操作',
    //   dataIndex: 'action',
    // },
  ]);
  const loading = ref(false);
  function getData() {
    loading.value = true;
    getProjectList({
      pageIndex: 1,
      pageSize: 5,
    })
      .then((res) => {
        const list = res.data?.data || [];
        data.value = list.map((item) => ({
          name: item.programmeName,
          compliance: item.compliance || 100,
          nonCompliance: item.nonCompliance || '-',
        }));
      })
      .finally(() => {
        loading.value = false;
      });
  }
  // getData();

  const getComplianceColor = (compliance: number): string => {
    if (compliance >= 80) return 'text-[#52c41a]';
    if (compliance >= 60) return 'text-[#faad14]';
    return 'text-[#ff4d4f]';
  };
</script>

<template>
  <CardPane
    title="团队成员AI能力使用概览"
    :loading="loading"
  >
    <ATable
      :data-source="data"
      size="small"
      :columns="columns"
      class="my-table stat-table"
      :pagination="false"
    />
  </CardPane>
</template>

<style lang="less" scoped>
  :deep(.ant-card-head) {
    min-height: 48px;
    padding: 0 24px;
    border-bottom: 1px solid #e5e6eb;
  }
  .my-table {
    :deep(.ant-table-thead .ant-table-cell) {
      font-weight: normal;
      color: #4b5071;
      background: #f4f6fe;
    }
    :deep(.ant-table-tbody .ant-table-cell) {
      color: #4b5071;
      border: none !important;
      padding: 10px 8px !important;
    }
  }
</style>
