<script setup lang="ts">
  import type { EChartsOption } from 'echarts';
  import { LineChart } from 'echarts/charts';
  import {
    GridComponent,
    MarkLineComponent,
    TitleComponent,
    TooltipComponent,
  } from 'echarts/components';
  import { use } from 'echarts/core';
  import { CanvasRenderer } from 'echarts/renderers';
  import VChart from 'vue-echarts';

  import DateSelect from '../DateSelect/index.vue';

  import 'echarts-liquidfill';

  use([
    CanvasRenderer,
    LineChart,
    TitleComponent,
    TooltipComponent,
    GridComponent,
    MarkLineComponent,
  ]);

  const date = ref('30');
  const dateList = ref([
    {
      name: '30天',
      value: '30',
    },
    {
      name: '60天',
      value: '60',
    },
    {
      name: '90天',
      value: '90',
    },
  ]);

  // 模拟数据
  const chartData = ref({
    xAxis: ['6.1', '6.2', '6.3', '6.4', '6.5'],
    values: [50, 60, 70, 80, 85],
  });

  const stats = ref({
    rank: 3,
    gap: 6,
    score: 80,
    projects: 3,
    tasks: 12,
    codeLines: 32145,
    reviewTime: 304,
    bugs: 12,
    buildFlow: 24,
  });

  const liquidOption = computed(() => ({
    // @ts-ignore
    series: [
      {
        type: 'liquidFill',
        data: [stats.value.score / 100, (stats.value.score + 2) / 100],
        radius: '80%',
        center: ['50%', '50%'],
        waveLength: '100%',
        color: ['#C8F0BB', '#75DA56'],
        itemStyle: {
          shadowBlur: 0,
        },
        backgroundStyle: {
          // color: '#f7f7f7',
          color: 'transparent',
        },

        label: {
          formatter: stats.value.score + '分',
          fontSize: 28,
          fontWeight: 'bold',
          color: '#75DA56',
          insideColor: '#fff',
        },
        outline: {
          show: true,
          borderDistance: 0,
          itemStyle: {
            borderColor: '#EAF6E7',
            shadowBlur: 0,
          },
        },
      },
    ],
  }));

  const option = computed<EChartsOption>(() => ({
    grid: {
      top: 10,
      right: 10,
      bottom: 24,
      left: 0,
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: chartData.value.xAxis,
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: '#999',
      },
    },
    yAxis: {
      type: 'value',
      splitLine: {
        lineStyle: {
          color: '#f0f0f0',
          type: 'dashed',
        },
      },
      axisLabel: {
        color: '#999',
      },
    },
    tooltip: {
      trigger: 'axis',
    },
    series: [
      {
        data: chartData.value.values,
        type: 'line',
        smooth: true,
        symbol: 'circle',
        symbolSize: 0,
        itemStyle: {
          color: '#ff6b03',
        },
        lineStyle: {
          color: '#FFBF52',
          width: 2,
        },
        // markLine: {
        //   data: [{ type: 'average', name: 'Avg' }],
        // },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(255, 180, 93, 0.2)',
              },
              {
                offset: 1,
                color: 'rgba(255, 180, 93, 0)',
              },
            ],
          },
        },
      },
    ],
  }));
</script>

<template>
  <CardPane
    title="项目贡献得分"
    tip="计算公式：研发任务数得分+有效代码得分+.."
    class="quality-card"
  >
    <template #extra>
      <!-- <ASelect
        ref="select"
        v-model:value="date"
        class="rounded-select w-[137px]"
      >
        <ASelectOption
          v-for="item in dateList"
          :key="item.value"
          :value="item.value"
        >
          {{ item.name }}
        </ASelectOption>
      </ASelect> -->
      <DateSelect v-model="date" />
    </template>
    <div class="flex gap-20">
      <div class="flex flex-col">
        <div class="ranking-card relative mb-4 inline-block h-41px px-4 leading-41px -ml-6">
          <span class="text-[#666]">当前团队内排名第</span>
          <span class="mx-1 text-2xl font-bold text-[#ff6b03]">{{ stats.rank }}</span>
          <span class="text-[#666]">，较上一名次差距</span>
          <span class="mx-1 text-2xl font-bold text-[#ff6b03]">{{ stats.gap }}</span>
          <span class="text-[#666]">分</span>
        </div>
        <div class="flex items-center gap-2">
          <!-- 左侧水球图 -->
          <div class="h-[200px] w-[200px] shrink-0">
            <VChart
              class="chart"
              :option="liquidOption"
              autoresize
            />
          </div>
          <!-- 中间统计数据 -->
          <div class="flex-1 px-4 leading-11 text-#090C20">
            <div class="truncate">研发任务: {{ stats.tasks }}</div>
            <!-- <div class="truncate">审记工时: {{ stats.reviewTime }}</div> -->
            <div class="truncate">修复Bug: {{ stats.bugs }}</div>
            <div class="truncate">有效代码: {{ stats.codeLines }}</div>
            <div class="truncate">构建流水线: {{ stats.buildFlow }}</div>
          </div>
        </div>
      </div>
      <!-- 右侧趋势图 -->
      <div class="h-full flex flex-1 flex-col gap-2">
        <div class="text-center text-#090C20">研发贡献趋势</div>
        <div class="h-100px w-full flex-1">
          <VChart
            class="h-280px w-full flex-1"
            :option="option"
            autoresize
          />
        </div>
      </div>
    </div>
  </CardPane>
</template>

<style scoped lang="less">
  .ranking-card {
    background: linear-gradient(270deg, rgba(51, 108, 246, 0.1) 0%, rgba(51, 108, 246, 0.03) 100%);
    &::after {
      position: absolute;
      top: 0;
      right: -21px;
      content: '';
      width: 0;
      height: 0;
      border-top: 41px solid rgba(51, 108, 246, 0.1);
      border-right: 21px solid transparent;
    }
  }
</style>
