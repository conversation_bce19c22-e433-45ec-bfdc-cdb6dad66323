<script setup lang="ts">
  import CloudListCard from './components/CloudListCard/index.vue';
  import MyProjectsCard from './components/MyProjectsCard/index.vue';
  import PageContainer from './components/PageContainer/index.vue';
  import PersonContributionCard from './components/PersonContributionCard/index.vue';
  import ProjectAiCard from './components/ProjectAiCard/index.vue';
  import ProjectQualityCard from './components/ProjectQualityCard/index.vue';
  import ProjectWarningCard from './components/ProjectWarningCard/index.vue';
  import QuickLinkCard from './components/QuickLinkCard/index.vue';
  import TeamUserDetailCard from './components/TeamUserDetailCard/index.vue';
</script>

<template>
  <!-- <PageContainer :todos="[0, 1, 2, 3, 4]">
    <div class="card-container grid grid-rows-[480px_290px_165px_100px_357px] grid-cols-3 gap-6">
      <MyProjectsCard
        class="grid-col-start-1 grid-row-start-1 grid-col-end-3 grid-row-end-2"
      ></MyProjectsCard>
      <ProjectWarningCard :size="9"></ProjectWarningCard>
      <PersonContributionCard class="grid-row-start-2 grid-row-end-5"></PersonContributionCard>
      <ProjectQualityCard></ProjectQualityCard>
      <CloudListCard
        class="grid-col-start-3 grid-row-start-2 grid-row-end-4"
        :size="8"
      ></CloudListCard>
      <ProjectAiCard class="grid-row-start-3 grid-row-end-5"></ProjectAiCard>
      <QuickLinkCard class="grid-row-start-4 grid-row-end-6"></QuickLinkCard>
      <TeamUserDetailCard
        class="grid-col-start-1 grid-row-start-5 grid-col-end-3 grid-row-end-6"
      ></TeamUserDetailCard>
    </div>
  </PageContainer> -->
  <PageContainer :todos="[0, 1, 2, 3, 4]">
    <div class="card-container grid grid-rows-[auto_90px_270px_270px] grid-cols-3 gap-6">
      <MyProjectsCard
        class="grid-col-start-1 grid-row-start-1 grid-col-end-3 grid-row-end-3 min-h-290px"
      ></MyProjectsCard>
      <ProjectWarningCard :size="7"></ProjectWarningCard>
      <PersonContributionCard class="grid-row-start-3 grid-row-end-5"></PersonContributionCard>
      <ProjectQualityCard class="grid-row-start-3 grid-row-end-4"></ProjectQualityCard>
      <CloudListCard
        class="grid-col-start-3 grid-row-start-2 grid-row-end-4"
        :size="6"
      ></CloudListCard>
      <ProjectAiCard class="grid-row-start-4 grid-row-end-5"></ProjectAiCard>
      <QuickLinkCard class="grid-row-start-4 grid-row-end-5"></QuickLinkCard>
      <!-- <TeamUserDetailCard
        class="grid-col-start-1 grid-row-start-5 grid-col-end-3 grid-row-end-6"
      ></TeamUserDetailCard> -->
    </div>
  </PageContainer>
</template>

<style scoped lang="less"></style>
