<script setup lang="ts">
  import { ReloadOutlined } from '@ant-design/icons-vue';
  import { useWindowSize } from '@vueuse/core';
  import { h, ref, watchEffect } from 'vue';
  import VChart, { THEME_KEY } from 'vue-echarts';

  import CapacityTypsCard from './components/AiCapacityTypsCard/index.vue';
  import PersonsTrendCard from './components/AiPersonsTrendCard/index.vue';
  import AiTopStat from './components/AiTopStatCard/index.vue';
  import UseCountsTrendCard from './components/AiUseCountsTrendCard/index.vue';

  const { width } = useWindowSize();
  const isNarrow = ref(false);
  watchEffect(() => {
    isNarrow.value = width.value <= 1366;
  });

  // 设置主题
  provide(THEME_KEY, 'light');

  const timeRange = ref('30');
  const handleTimeRangeChange = (value: any) => {
    timeRange.value = value;
  };
</script>

<template>
  <div
    :key="timeRange"
    class="company-dashboard"
  >
    <ATabs
      class="top-tabs base-card-pane"
      default-active-key="1"
    >
      <ATabPane
        key="1"
        tab="全部公司"
      />
      <template #rightExtra>
        <AButton
          type="text"
          class="mr-7px"
          :icon="h(ReloadOutlined)"
          @click="timeRange = '30'"
        ></AButton>
        <ASelect
          v-model:value="timeRange"
          class="rounded-select w-137px"
          :options="[
            { label: '近30天', value: '30' },
            { label: '近60天', value: '60' },
            { label: '近90天', value: '90' },
          ]"
          @change="handleTimeRangeChange"
        ></ASelect>
      </template>
    </ATabs>
    <AiTopStat />
    <ARow
      :gutter="20"
      wrap
    >
      <ACol
        :xs="24"
        :sm="24"
        :md="16"
        :lg="16"
        :xl="16"
      >
        <PersonsTrendCard :height="400" />
      </ACol>
      <ACol
        :xs="24"
        :sm="24"
        :md="8"
        :lg="8"
        :xl="8"
      >
        <UseCountsTrendCard :height="400" />
      </ACol>
    </ARow>
    <ARow
      :gutter="20"
      wrap
    >
      <ACol
        :xs="24"
        :sm="24"
        :md="24"
        :lg="24"
        :xl="24"
      >
        <CapacityTypsCard style="height: 320px" />
      </ACol>
    </ARow>
  </div>
</template>

<style lang="less" scoped>
  .company-dashboard {
    display: flex;
    flex-direction: column;
    gap: 24px;

    .top-tabs {
      padding: 0 24px;
      border-radius: 8px;
      background: rgba(255, 255, 255, 0.8);

      :deep(.ant-tabs-nav) {
        margin-bottom: 0;
      }
    }
  }
</style>
