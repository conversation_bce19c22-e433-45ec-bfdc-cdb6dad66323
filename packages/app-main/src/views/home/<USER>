<script setup lang="tsx">
  import CloudListCard from './components/CloudListCard/index.vue';
  import MilestoneCard from './components/MilestoneCard/index.vue';
  import PageContainer from './components/PageContainer/index.vue';
  import ProjectProcessCard from './components/ProjectProcessCard/index.vue';
  import ProjectUpCloudCard from './components/ProjectUpCloudCard/index.vue';
  import ProjectWarningCard from './components/ProjectWarningCard/index.vue';
  import QuickLinkCard from './components/QuickLinkCard/index.vue';
</script>

<template>
  <PageContainer :todos="[5, 6, 7, 8, 9]">
    <div class="card-container grid grid-rows-[370px_80px_320px_300px] grid-cols-3 gap-6">
      <MilestoneCard class="grid-col-start-1 grid-row-start-1 grid-col-end-3 grid-row-end-3" />
      <ProjectWarningCard :size="7" />
      <!-- <ProjectQualityCard class="grid-row-start-2 grid-row-end-4" /> -->
      <CloudListCard
        class="grid-row-start-2 grid-row-end-4"
        :size="7"
      />
      <ProjectProcessCard class="grid-col-start-1 grid-row-start-3 grid-col-end-3 grid-row-end-4" />
      <ProjectUpCloudCard class="grid-col-start-1 grid-col-end-3" />
      <QuickLinkCard />
    </div>
  </PageContainer>
</template>

<style scoped lang="less"></style>
