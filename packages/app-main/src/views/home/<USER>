<script setup lang="ts">
  import api from '@/apis';

  // import CompanyManager from './CompanyManager.vue';
  // import DepartmentManager from './DepartmentManager.vue';
  // import Developer from './Developer.vue';
  // import ProcessManager from './ProcessManager.vue';
  // import ProjectManager from './ProjectManager.vue';
  // import ScrumMaster from './ScrumMaster.vue';

  // const currentRole = ref<keyof typeof roles>('scrumMaster');
  // const roles = {
  //   companyManager: {
  //     component: CompanyManager,
  //     name: '公司管理',
  //   },
  //   departmentManager: {
  //     component: DepartmentManager,
  //     name: '部门经理',
  //   },
  //   projectManager: {
  //     component: ProjectManager,
  //     name: '项目经理',
  //   },
  //   scrumMaster: {
  //     component: ScrumMaster,
  //     name: 'SM',
  //   },
  //   processManager: {
  //     component: ProcessManager,
  //     name: '流程经理',
  //   },
  //   developer: {
  //     component: Developer,
  //     name: '开发人员',
  //   },
  // };
  const router = useRouter();
  const loading = ref(true);
  api.tree().then((res) => {
    const firstUrl = res.data.data?.[0]?.children?.[0]?.path;
    if (firstUrl) {
      router.push(firstUrl);
    }
    loading.value = false;
  });
</script>

<template>
  <!-- <ASelect
    v-model:value="currentRole"
    class="absolute left-270px top-10px z-101 mb-4 w-50"
  >
    <ASelectOption
      v-for="(role, key) in roles"
      :key="key"
      :value="key"
    >
      {{ role.name }}
    </ASelectOption>
  </ASelect> -->
  <!-- <component :is="roles[currentRole].component" /> -->
  <ASpin
    :spinning="loading"
    class="h-full w-full flex items-center justify-center"
  />
</template>
