<script setup lang="ts">
  import AiToolUsedCard from './components/AiToolUsedCard/index.vue';
import CodeCommitChartCard from './components/CodeCommitChartCard/index.vue';
import CodeQualityChartCard from './components/CodeQualityChartCard/index.vue';
import ContributionScoreCard from './components/ContributionScoreCard/index.vue';
import PageContainer from './components/PageContainer/index.vue';
import ProjectWarningCard from './components/ProjectWarningCard/index.vue';
import QuickLinkCard from './components/QuickLinkCard/index.vue';
</script>

<template>
  <PageContainer :todos="[1, 20, 2, 3, 4]">
    <div class="card-container grid grid-rows-[370px_370px_366px] grid-cols-6 gap-6">
      <ContributionScoreCard class="grid-col-start-1 grid-col-end-5"></ContributionScoreCard>
      <ProjectWarningCard class="grid-col-start-5 grid-col-end-7"></ProjectWarningCard>
      <AiToolUsedCard class="grid-col-start-1 grid-col-end-5"></AiToolUsedCard>
      <QuickLinkCard class="grid-col-start-5 grid-col-end-7"></QuickLinkCard>
      <CodeCommitChartCard class="grid-col-start-1 grid-col-end-4"></CodeCommitChartCard>
      <CodeQualityChartCard class="grid-col-start-4 grid-col-end-7"></CodeQualityChartCard>
    </div>
  </PageContainer>
</template>

<style scoped lang="less"></style>
