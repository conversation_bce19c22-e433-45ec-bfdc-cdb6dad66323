<script setup lang="ts">
  import CloudListCard from './components/CloudListCard/index.vue';
  import PageContainer from './components/PageContainer/index.vue';
  import ProjectAiUsedCard from './components/ProjectAiUsedCard/index.vue';
  import ProjectProcessCard from './components/ProjectProcessCard/index.vue';
  import ProjectQualityCard from './components/ProjectQualityCard/index.vue';
  import ProjectSuperviseCard from './components/ProjectSuperviseCard/index.vue';
  import ProjectSuperviseChartCard from './components/ProjectSuperviseChartCard/index.vue';
  import ProjectUpCloudCard from './components/ProjectUpCloudCard/index.vue';
  import ProjectWarningCard from './components/ProjectWarningCard/index.vue';
  import QuickLinkCard from './components/QuickLinkCard/index.vue';
</script>

<template>
  <PageContainer :todos="[5, 6, 17, 18, 19]">
    <div class="card-container grid grid-rows-[370px_80px_320px_300px] grid-cols-3 gap-6">
      <ProjectAiUsedCard class="grid-row-start-1 grid-row-end-3"></ProjectAiUsedCard>
      <ProjectSuperviseCard class="grid-row-start-1 grid-row-end-3"></ProjectSuperviseCard>
      <ProjectWarningCard
        :is-process-manager="true"
        :size="7"
      ></ProjectWarningCard>
      <CloudListCard
        class="grid-row-start-2 grid-row-end-4"
        :size="7"
      ></CloudListCard>
      <ProjectQualityCard class="grid-row-start-3 grid-row-end-4"></ProjectQualityCard>
      <ProjectProcessCard class="grid-row-start-3 grid-row-end-4"></ProjectProcessCard>
      <ProjectUpCloudCard></ProjectUpCloudCard>
      <ProjectSuperviseChartCard></ProjectSuperviseChartCard>
      <QuickLinkCard></QuickLinkCard>
    </div>
  </PageContainer>
</template>

<style scoped lang="less"></style>
