<script setup lang="ts">
  import icon1 from '@/assets/images/icon_1.png';
  import icon2 from '@/assets/images/icon_2.png';
  import icon3 from '@/assets/images/icon_3.png';
  import icon4 from '@/assets/images/icon_4.png';
  import icon5 from '@/assets/images/icon_5.png';
  import icon6 from '@/assets/images/icon_6.png';

  import AbilityMarketOverview from './components/AbilityMarketOverview/index.vue';
  import CodeQualityChartCard from './components/CodeQualityChartCard/index.vue';
  import IntelligentApps from './components/IntelligentAppsCard/index.vue';
  import ProcessComplianceChartCard from './components/ProcessComplianceChartCard/index.vue';
  import ProjectOverviewCard from './components/ProjectOverviewCard/index.vue';
  import TaskCompletedChartCard from './components/TaskCompletedChartCard/index.vue';

  const headData = [
    { icon: icon1, title: '在建项目', value: 313, unit: '个' },
    { icon: icon2, title: '初验项目', value: 219, unit: '个' },
    { icon: icon3, title: '终验项目', value: 632, unit: '个' },
    { icon: icon4, title: '已结项项目', value: 654, unit: '个' },
    { icon: icon5, title: '其他项目', value: 235, unit: '个' },

    // { icon: icon2, title: 'SM', value: 89, unit: '人' },
    // { icon: icon3, title: '项目经理', value: 52, unit: '人' },
    // { icon: icon4, title: '开发工程师', value: 673, unit: '人' },
    // { icon: icon5, title: '测试工程师', value: 21, unit: '人' },
    // { icon: icon6, title: '过程专员', value: 11, unit: '人' },
  ];
</script>

<template>
  <div
    class="flex base-card-pane bg-white rounded-3 items-center justify-between px-15 py-10"
  >
    <div
      v-for="item in headData"
      :key="item.title"
      class="item"
    >
      <div class="flex items-start gap-2">
        <img
          :src="item.icon"
          class="h-48px w-48px"
        />
        <div class="flex flex-col pl-6">
          <span class="text-30px font-bold text-#1B1D29">
            {{ item.value }}
            <span class="text-4 font-normal">{{ item.unit }}</span>
          </span>
          <span class="mt-2 text-4 text-#4B5071">{{ item.title }}</span>
        </div>
      </div>
    </div>
  </div>

  <ARow
    :gutter="24"
    class="mt-6"
  >
    <ACol :span="8">
      <ProjectOverviewCard height="280px" />
    </ACol>
    <ACol :span="8">
      <TaskCompletedChartCard />
    </ACol>
    <ACol :span="8">
      <CodeQualityChartCard height="280px" />
    </ACol>
  </ARow>
  <ProcessComplianceChartCard
    class="mt-6"
    height="610px"
  />
  <ARow
    class="mt-6"
    :gutter="24"
  >
    <ACol :span="24">
      <IntelligentApps />
    </ACol>
  </ARow>
  <ARow
    class="mt-6"
    :gutter="24"
  >
    <ACol :span="24">
      <AbilityMarketOverview />
    </ACol>
  </ARow>
</template>

<style scoped lang="less">
  .item {
    padding-right: 50px;
    border-right: 1px solid rgba(86, 88, 101, 0.2);

    &:last-child {
      border-right: none;
    }
  }
</style>
