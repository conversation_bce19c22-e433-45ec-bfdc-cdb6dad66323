<script setup lang="ts">
  import { useWindowSize } from '@vueuse/core';
  import { ref, watchEffect } from 'vue';

  import ComponentOfTopCard from './components/ComponentOfTopCard/index.vue';
  import ComponentOverviewCard from './components/ComponentOverviewCard/index.vue';
  import PageContainer from './components/PageContainer/index.vue';
  import AiPromote from './components/ProjectAiPromoteCard/index.vue';
  import ComsCbtGuardDept from './components/ProjectComsCbtGuardDeptCard/index.vue';
  import DevTrend from './components/ProjectDevTrendCard/index.vue';
  import PersonPayload from './components/ProjectPersonPayloadCard/index.vue';

  const { width } = useWindowSize();
  const isNarrow = ref(false);
  watchEffect(() => {
    isNarrow.value = width.value <= 1366;
  });
</script>

<template>
  <PageContainer :todos="[21, 22, 23, 24, 25]">
    <ARow
      :gutter="isNarrow ? 8 : 24"
      class="mb-6"
      wrap
    >
      <ACol
        :xs="24"
        :sm="24"
        :md="16"
        :lg="16"
        :xl="16"
      >
        <DevTrend :height="450" />
      </ACol>
      <ACol
        :xs="24"
        :sm="24"
        :md="8"
        :lg="8"
        :xl="8"
      >
        <PersonPayload :height="450" />
      </ACol>
    </ARow>
    <ARow
      :gutter="isNarrow ? 8 : 24"
      class="mb-6"
      wrap
    >
      <ACol
        :xs="24"
        :sm="24"
        :md="12"
        :lg="12"
        :xl="12"
      >
        <ComponentOfTopCard style="height: 366px" />
      </ACol>
      <ACol
        :xs="24"
        :sm="24"
        :md="12"
        :lg="12"
        :xl="12"
      >
        <ComponentOverviewCard style="height: 366px" />
      </ACol>
    </ARow>
    <ARow
      :gutter="isNarrow ? 8 : 24"
      wrap
    >
      <ACol
        :xs="24"
        :sm="24"
        :md="12"
        :lg="12"
        :xl="12"
      >
        <AiPromote style="height: 482px" />
      </ACol>
      <ACol
        :xs="24"
        :sm="24"
        :md="12"
        :lg="12"
        :xl="12"
      >
        <ComsCbtGuardDept style="height: 482px" />
      </ACol>
    </ARow>
  </PageContainer>
</template>

<style lang="less" scoped>
  .company-dashboard {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }
</style>
