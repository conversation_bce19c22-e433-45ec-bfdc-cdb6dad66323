<script setup lang="ts">
  import { ExclamationCircleOutlined } from '@ant-design/icons-vue';

  import AiUseTrendCard from './components/AiUseTrendCard/index.vue';
  import ComponentOfTopCard from './components/ComponentOfTopCard/index.vue';
  import PageContainer from './components/PageContainer/index.vue';
  import PersonAiUseSortCard from './components/PersonAiUseSortCard/index.vue';
  import PersonContributionSortCard from './components/PersonContributionSortCard/index.vue';
  import DevTrend from './components/ProjectDevTrendCard/index.vue';
  import ProjectListToCard from './components/ProjectListToCard/index.vue';
</script>

<template>
  <PageContainer :todos="[10, 11, 12, 13, 14, 15, 16]">
    <ARow :gutter="24">
      <ACol
        :xl="24"
        :lg="24"
        :md="24"
        :sm="24"
        :xs="24"
        class="mb-[24px]"
      >
        <ProjectListToCard class="min-h-290px" />
      </ACol>
    </ARow>

    <ARow :gutter="24">
      <ACol
        :xl="17"
        :lg="24"
        :md="24"
        :sm="24"
        :xs="24"
        class="h-max"
      >
        <DevTrend
          class="mb-[24px]"
          :height="295"
        />
        <AiUseTrendCard
          class="mb-[24px]"
          :height="295"
        />
      </ACol>
      <ACol
        :xl="7"
        :lg="24"
        :md="24"
        :sm="24"
        :xs="24"
        class="h-max"
      >
        <PersonContributionSortCard
          only-sort
          class="mb-[24px]"
          :height="295"
        />
        <PersonAiUseSortCard
          only-sort
          class="mb-[24px]"
          :height="295"
        />
      </ACol>
    </ARow>
  </PageContainer>
</template>

<style scoped lang="less">
  .card-container {
    .col {
      background-color: #fff;
      border-radius: 10px;
    }
  }
</style>
