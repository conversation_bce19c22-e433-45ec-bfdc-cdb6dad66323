<script setup lang="ts">
  import type { EChartsOption } from 'echarts';
  import { LineChart } from 'echarts/charts';
  import { GridComponent, LegendComponent, TooltipComponent } from 'echarts/components';
  import { use } from 'echarts/core';
  import { CanvasRenderer } from 'echarts/renderers';
  import { provide, ref } from 'vue';
  import VChart, { THEME_KEY } from 'vue-echarts';

  import DateSelect from '../DateSelect/index.vue';

  // 注册必须的组件
  use([Can<PERSON><PERSON>enderer, LineChart, GridComponent, TooltipComponent, LegendComponent]);

  // 设置主题
  provide(THEME_KEY, 'light');

  const date = ref<string>('30');

  const option = ref<EChartsOption>({
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        const data = params.map((item: any) => {
          return `${item.seriesName}: ${item.value}`;
        });
        return `${params[0].axisValue}<br/>${data.join('<br/>')}`;
      },
    },
    legend: {
      show: false,
    },
    grid: {
      top: 20,
      left: 15,
      right: 15,
      bottom: 20,
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['2025.05.31', '2025.06.07', '2025.06.14', '2025.06.21', '2025.06.28'],
      axisLine: {
        lineStyle: {
          color: '#E5E6EB',
        },
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: '#999',
        fontSize: 12,
      },
    },
    yAxis: {
      type: 'value',
      max: 100,
      min: 0,
      interval: 20,
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: '#999',
        fontSize: 12,
      },
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: '#E5E6EB',
        },
      },
    },
    series: [
      {
        name: '计划完成率',
        type: 'line',
        // smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        data: [100, 80, 60, 40, 20],
        itemStyle: {
          color: '#4080FF',
        },
        lineStyle: {
          color: '#4080FF',
          width: 2,
          shadowColor: 'rgba(16, 46, 117, 0.4)',
          shadowBlur: 8,
          shadowOffsetY: 4,
        },
      },
      {
        name: '实际完成率',
        type: 'line',
        // smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        data: [100, 86, 79, 60, 30],
        itemStyle: {
          color: '#FF9500',
        },
        lineStyle: {
          color: '#FF9500',
          width: 2,
          shadowColor: 'rgba(16, 46, 117, 0.4)',
          shadowBlur: 8,
          shadowOffsetY: 4,
        },
        areaStyle: {
          color: 'rgba(255, 149, 0, 0.3)',
        },
      },
    ],
  });
</script>

<template>
  <CardPane title="任务完成趋势分析">
    <template #extra>
      <DateSelect v-model="date" />
    </template>
    <div class="chart-wrapper">
      <VChart
        class="chart-container"
        :option="option"
        autoresize
      />
    </div>
  </CardPane>
</template>

<style scoped lang="less">
  .chart-wrapper {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .chart-container {
    width: 100%;
    height: 280px;
    flex: 1;
  }
</style>
