<script setup lang="ts">
  import { PlusOutlined } from '@ant-design/icons-vue';

  import api, { type QuickLinkItem } from '@/apis';
  import quickLinkRjgc from '@/assets/images/quicklink_rjgc.svg';
  import quickLinkYfy from '@/assets/images/quicklink_yfy.svg';
  import quickLinkZnt from '@/assets/images/quicklink_znt.svg';

  import AddForm from './AddForm.vue';

  defineOptions({
    name: 'QuickLinkNav',
  });

  interface QuickLink extends QuickLinkItem {}
  const list = ref<QuickLink[]>([
    {
      name: '研发云',
      icon: quickLinkYfy,
      url: 'https://www.srdcloud.cn/',
    },
    // {
    //   name: '智胜智能体平台',
    //   icon: quickLinkZnt,
    //   url: 'http://************:30080/',
    // },
    // {
    //   name: '研发云',
    //   icon: quickLinkYfy,
    //   url: 'https://www.srdcloud.cn/',
    // },
    {
      name: '软件工厂',
      icon: quickLinkRjgc,
      url: 'https://starfactory.teleagi.cn/',
    },
  ]);
  const loading = ref(false);
  const { createDialog } = useDialog();
  const handleAdd = () => {
    console.log('add');
    createDialog({
      title: '添加导航',
      width: 600,
      component: AddForm,
      onConfirm(data) {
        console.log(data);
      },
    });
  };
  const handleDelete = (item: QuickLink) => {
    console.log(item);
  };
</script>

<template>
  <CardPane title="便捷导航">
    <template #extra>
      <!-- <PlusOutlined
        class="cursor-pointer"
        @click="handleAdd"
      /> -->
    </template>
    <ASpin :spinning="loading">
      <div class="grid grid-cols-2 gap-7 p-4">
        <div
          v-for="(item, index) in list"
          :key="index"
          class="group relative flex items-center overflow-hidden border border-[var(--zion-border-color)] rounded-10px border-solid bg-[#fff] p-3 hover:shadow-[0px_0px_8px_0px_rgba(0,0,0,0.1)]"
        >
          <a
            target="_blank"
            :href="item.url"
            class="w-full flex items-center"
          >
            <img
              :src="item.icon"
              :alt="item.name"
              class="mr-2 h-10 w-10 rounded-2 object-contain"
            />
            <div class="ml-2 w-full truncate text-4 text-gray-700">
              {{ item.name }}
            </div>
          </a>
          <!-- <APopconfirm
            title="是否要删除当前导航?"
            ok-text="确定"
            cancel-text="取消"
            @confirm="handleDelete(item)"
          >
            <div
              class="absolute right-6px top-6px hidden cursor-pointer text-red group-hover:block"
            >
              <DeleteOutlined />
            </div>
          </APopconfirm> -->
        </div>
      </div>
    </ASpin>
  </CardPane>
</template>

<style scoped lang="less">
  /* 自定义样式，Tailwind无法覆盖的部分 */
  .active::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 16px;
    height: 16px;
    background-color: #4080ff;
    clip-path: polygon(0 0, 100% 0, 100% 100%);
  }
</style>
