<script setup lang="ts">
  import type { FormInstance } from 'ant-design-vue';
  import { ref, useTemplateRef } from 'vue';

  const formRef = useTemplateRef<FormInstance>('formRef');
  const form = ref({
    name: '',
    url: '',
    icon: '',
  });
  const rules = {
    name: [{ required: true, message: '请输入导航名称' }],
    url: [{ required: true, message: '请输入链接' }],
    icon: [{ required: true, message: '请上传图标' }],
  };
  const emits = defineEmits<{
    (e: 'loading', value: boolean): void;
  }>();
  defineExpose({
    async submit() {
      emits('loading', true);
      return formRef.value?.validate().then(async () => {
        emits('loading', false);
        return Promise.resolve(form);
      });
    },
  });
</script>

<template>
  <AForm
    ref="formRef"
    :model="form"
    :rules="rules"
    layout="vertical"
  >
    <AFormItem
      label="导航名称"
      name="name"
    >
      <AInput v-model:value="form.name" />
    </AFormItem>
    <AFormItem
      label="链接"
      name="url"
    >
      <AInput v-model:value="form.url" />
    </AFormItem>
    <AFormItem
      label="图标"
      name="icon"
    >
      <UploadImg v-model="form.icon" />
    </AFormItem>
  </AForm>
</template>

<style scoped></style>
