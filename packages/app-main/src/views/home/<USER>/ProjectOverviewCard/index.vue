<script setup lang="ts">
  import dayjs from 'dayjs';
  import type { EChartsOption } from 'echarts';
  import { PieChart } from 'echarts/charts';
  import { LegendComponent, TooltipComponent } from 'echarts/components';
  import { use } from 'echarts/core';
  import { CanvasRenderer } from 'echarts/renderers';
  import { ref, watch } from 'vue';
  import VChart from 'vue-echarts';

  import { getUserAiCountOfSM } from '@/apis/workbench';
  import { CardPane } from '@/components/Layout';

  import DateSelect from '../DateSelect/index.vue';

  // 注册必须的组件
  use([Canvas<PERSON>enderer, PieChart, TooltipComponent, LegendComponent]);

  const loading = ref(false);
  const isEmpty = ref(false);
  const itemColors = ['#027FFF', '#FFBF52', '#30C25B', '#FF00ED', '#8F7EFF', '#1B00D1'];
  const date = ref({
    startTime: '',
    endTime: '',
  });
  const data = ref<any>([
    {
      name: 'SM',
      value: 89,
      itemStyle: { color: itemColors[0] },
    },
    {
      name: '项目经理',
      value: 52,
      itemStyle: { color: itemColors[1] },
    },
    {
      name: '开发工程师',
      value: 673,
      itemStyle: { color: itemColors[2] },
    },
    {
      name: '测试工程师',
      value: 21,
      itemStyle: { color: itemColors[3] },
    },
    {
      name: '过程专员',
      value: 11,
      itemStyle: { color: itemColors[4] },
    },
    // {
    //   name: '在建',
    //   value: 313,
    //   itemStyle: { color: itemColors[0] },
    // },
    // {
    //   name: '初验',
    //   value: 219,
    //   itemStyle: { color: itemColors[1] },
    // },
    // {
    //   name: '终验',
    //   value: 632,
    //   itemStyle: { color: itemColors[2] },
    // },
    // {
    //   name: '已结项',
    //   value: 654,
    //   itemStyle: { color: itemColors[3] },
    // },
    // {
    //   name: '其他',
    //   value: 235,
    //   itemStyle: { color: itemColors[4] },
    // },
  ]);
  // const total = ref(0);
  const total = computed(() => {
    return data.value.reduce((acc, item) => {
      acc += item.value;
      return acc;
    }, 0);
  });
  // function getData() {
  //   loading.value = true;
  //   getUserAiCountOfSM(date.value)
  //     .then((res) => {
  //       console.log(res);
  //       const list = res.data?.data || [];
  //       if (list.length === 0) {
  //         isEmpty.value = true;
  //         return;
  //       }
  //       total.value = list.reduce((acc, item) => {
  //         acc += item.usageCount;
  //         return acc;
  //       }, 0);
  //       data.value = list.map((item, index) => {
  //         return {
  //           name: item.assistantName,
  //           value: item.usageCount,
  //           users: item.userCount,
  //           percentage: ((item.usageCount / total.value) * 100).toFixed(2),
  //           itemStyle: { color: itemColors[index] },
  //         };
  //       });
  //     })
  //     .finally(() => {
  //       loading.value = false;
  //     });
  // }
  const option = computed<EChartsOption>(() => ({
    tooltip: {
      trigger: 'item',
    },
    legend: {
      orient: 'vertical',
      right: '5%',
      top: 'middle',
      itemGap: 16,
      itemWidth: 10,
      itemHeight: 8,
      icon: 'circle',
      formatter: (name: string) => {
        const item = data.value.find((d) => d.name === name);
        return `{name|${name}}  {value|${((item?.value / total.value) * 100).toFixed(0)}%  ${item?.value}}`;
      },
      textStyle: {
        fontSize: 12,
        color: '#4B5071',
        rich: {
          name: {
            width: 70,
            color: '#4B5071',
          },
          value: {
            color: '#4B5071',
            fontWeight: 'bold',
          },
        },
      },
    },
    series: [
      {
        name: 'AI辅助使用情况',
        type: 'pie',
        radius: ['50%', '70%'],
        center: ['25%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          // borderRadius: 4,
          borderColor: '#fff',
          // borderWidth: 2,
        },
        label: {
          show: false,
        },
        labelLine: {
          show: false,
        },
        data: data.value,
      },
    ],
  }));
</script>

<template>
  <CardPane
    title="研发人员概览"
    :loading="loading"
    :empty="{ show: isEmpty }"
  >
    <!-- <template #extra>
      <DateSelect
        v-model="date"
        @change="getData"
      />
    </template> -->

    <div class="chart-wrapper">
      <div class="total-info">
        <div class="value">{{ total.toLocaleString() }}</div>
        <div class="label">总数</div>
      </div>
      <VChart
        class="chart-container"
        :option="option"
        autoresize
      />
    </div>
  </CardPane>
</template>

<style scoped lang="less">
  .chart-wrapper {
    height: 100%;

    .total-info {
      position: absolute;
      left: 25%;
      top: 50%;
      transform: translate(-50%, -50%);
      text-align: center;
      z-index: 1;

      .value {
        font-size: 24px;
        font-weight: bold;
        color: #333;
      }

      .label {
        font-size: 14px;
        color: #666;
        margin-top: 4px;
      }
    }
  }

  .chart-container {
    width: 100%;
    height: 100%;
  }

  :deep(.rounded-select) {
    .ant-select-selector {
      border-radius: 20px !important;
    }
  }
</style>
