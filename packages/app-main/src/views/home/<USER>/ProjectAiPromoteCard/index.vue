<script setup lang="tsx">
  import { CaretDownOutlined, CaretUpOutlined } from '@ant-design/icons-vue';
  import { Flex, Progress } from 'ant-design-vue';

  import DateSelect from '../DateSelect/index.vue';

  defineProps<{
    height?: string | number;
  }>();

  const timeRange = ref('30');

  const aiEfficiencyRank = [
    {
      dept: '数智事业部',
      score: 95,
      efficiencyPerson: 270,
      totalPerson: 299,
      progress: 92,
      progressType: 'up',
    },
    {
      dept: 'AI及大数据事业部',
      score: 92,
      efficiencyPerson: 220,
      totalPerson: 254,
      progress: 90,
      progressType: 'up',
    },
    {
      dept: '工业互联网',
      score: 89,
      efficiencyPerson: 45,
      totalPerson: 63,
      progress: 85,
      progressType: 'up',
    },
    {
      dept: '农村农业事业部',
      score: 86,
      efficiencyPerson: 110,
      totalPerson: 142,
      progress: 82,
      progressType: 'up',
    },
    {
      dept: '要客部',
      score: 83,
      efficiencyPerson: 31,
      totalPerson: 42,
      progress: 79,
      progressType: 'up',
    },
    {
      dept: '标品业务部',
      score: 80,
      efficiencyPerson: 10,
      totalPerson: 52,
      progress: 76,
      progressType: 'up',
    },
    {
      dept: '文商旅事业部',
      score: 77,
      efficiencyPerson: 20,
      totalPerson: 166,
      progress: 75,
      progressType: 'up',
    },
    {
      dept: '战新业务事业部',
      score: 80,
      efficiencyPerson: 18,
      totalPerson: 101,
      progress: 72,
      progressType: 'up',
    },
    {
      dept: '视频服务部',
      score: 74,
      efficiencyPerson: 20,
      totalPerson: 56,
      progress: 68,
      progressType: 'up',
    },
    {
      dept: '平台服务部',
      score: 74,
      efficiencyPerson: 20,
      totalPerson: 89,
      progress: 50,
      progressType: 'up',
    },
  ];
  const handleTimeRangeChange = (value: string) => {
    timeRange.value = value;
  };
  const getProgressColor = (value: number) => {
    if (value >= 80) return '#49CE20';
    if (value >= 60) return '#FF7102';
    return '#EE6060';
  };

  const aiEfficiencyColumns = [
    {
      title: '排名',
      key: 'rank',
      align: 'center' as const,
      width: 60,
      customRender: ({ index }: { index: number }) => {
        return index + 1;
      },
    },
    { title: '部门', dataIndex: 'dept', key: 'dept', width: 120 },
    {
      title: 'AI工具覆盖率',
      dataIndex: 'efficiencyPerson',
      key: 'efficiencyPerson',
      align: 'center' as const,
      width: 120,
      customRender: ({ record }: { record: any }) => {
        const progress = (record.efficiencyPerson / record.totalPerson) * 100;
        return (
          <Flex
            align="center"
            style="width: 100%;"
          >
            <Progress
              percent={progress}
              strokeColor={getProgressColor(progress)}
              showInfo={false}
              size="small"
              style="flex: 1;margin-right: 5px;margin-bottom: 0;"
            />
            <span style="flex-shrink: 0;color: #4B5071;font-size: 12px;">
              {record.efficiencyPerson}
              <span class="mx-1">/</span>
              {record.totalPerson}人
            </span>
          </Flex>
        );
      },
    },
    {
      title: 'AI 效能提升比',
      key: 'progress',
      align: 'center' as const,
      width: 120,
      customRender: ({ record }: { record: any }) => {
        const progressColor = record.progressType === 'up' ? '#49CE20' : '#EE6060';
        return (
          <span>
            <span style="display:inline-block;width: 100px;text-align:right;margin-right: 4px;">
              {record.progress}%
            </span>
            {record.progressType === 'up' ? (
              <CaretUpOutlined style={{ color: progressColor }} />
            ) : (
              <CaretDownOutlined style={{ color: progressColor }} />
            )}
          </span>
        );
      },
    },
  ];
</script>

<template>
  <CardPane
    title="部门AI提效排行"
    :height="height"
  >
    <template #extra>
      <!-- <ASelect
        v-model:value="timeRange"
        class="rounded-select w-[180px]"
        :options="[
          { label: '近30天', value: '30' },
          { label: '近60天', value: '60' },
          { label: '近90天', value: '90' },
        ]"
        @change="handleTimeRangeChange"
      ></ASelect> -->
      <DateSelect
        v-model="timeRange"
        @change="handleTimeRangeChange"
      />
    </template>
    <ATable
      class="stat-table stat-table-compact"
      :data-source="aiEfficiencyRank"
      :columns="aiEfficiencyColumns"
      :pagination="false"
      size="small"
      :bordered="false"
      row-key="dept"
    />
  </CardPane>
</template>

<style scoped lang="less">
  .stat-table {
    :deep(.ant-table-thead > tr > th) {
      background: #fafafa;
      font-weight: 500;
    }
  }
</style>
