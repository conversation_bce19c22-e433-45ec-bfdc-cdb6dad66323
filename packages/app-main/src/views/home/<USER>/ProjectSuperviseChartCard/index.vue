<script setup lang="tsx">
  import VChart from 'vue-echarts';

  defineProps<{
    height?: string | number;
  }>();

  const trendOption = {
    tooltip: {},
    xAxis: {
      type: 'category',
      data: [
        '甘肃机电职业技术学院OA项目维保',
        '甘肃省智慧博物馆项目“数字甘博”平台单纯购置项目',
        '陇南市教育局协同办公项目',
        '河北省12345政务服务便民热线升级改造项目',
      ],
      axisLabel: { fontSize: 10, margin: 10, overflow: 'truncate', width: 80, interval: 0 },
      axisLine: {
        lineStyle: {
          color: 'rgba(196,197,197,1)',
        },
      },
    },
    yAxis: {
      type: 'value',
      axisLabel: { fontSize: 12 },
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
          color: 'rgba(196,197,197,1)',
        },
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(196,197,197,1)',
        },
      },
    },
    series: [
      {
        data: [55, 90, 70, 60],
        type: 'bar',
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#A2E6FF' },
              { offset: 1, color: '#3173FF' },
            ],
          },
          borderRadius: [6, 6, 0, 0],
        },
        barWidth: 15,
      },
    ],
    grid: { left: 30, right: 20, top: 30, bottom: 30 },
  };
</script>

<template>
  <CardPane
    title="项目督办分布情况"
    :height="height"
    tip=""
  >
    <VChart
      :option="trendOption"
      autoresize
      style="height: 100%; width: 100%"
    />
  </CardPane>
</template>

<style scoped lang="less"></style>
