<script setup lang="tsx">
  import { Line<PERSON>hart } from 'echarts/charts';
  import {
    GridComponent,
    LegendComponent,
    MarkLineComponent,
    TooltipComponent,
  } from 'echarts/components';
  import { use } from 'echarts/core';
  import { CanvasRenderer } from 'echarts/renderers';
  import VChart from 'vue-echarts';

  // 注册必须的组件
  use([
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    Line<PERSON>hart,
    GridComponent,
    TooltipComponent,
    LegendComponent,
    MarkLineComponent,
  ]);
  defineProps<{
    height?: string | number;
  }>();

  const trendOption = {
    tooltip: {},
    xAxis: {
      type: 'category',
      data: [
        '渔业信息化管理服务平台（一期）',
        '宜昌市兴山智慧旅游平台项目',
        '天翼云图',
        '甘肃机电职业技术学院OA项目维保',
      ],
      axisLabel: {
        fontSize: 10,
        margin: 10,
        overflow: 'truncate',
        width: 80,
        interval: 0,
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(196,197,197,1)',
        },
      },
    },
    yAxis: {
      type: 'value',
      axisLabel: { fontSize: 12 },
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
          color: 'rgba(196,197,197,1)',
        },
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(196,197,197,1)',
        },
      },
    },
    series: [
      {
        data: [56, 70, 95, 80],
        type: 'bar',
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#A2E6FF' },
              { offset: 1, color: '#3173FF' },
            ],
          },
          borderRadius: [6, 6, 0, 0],
        },

        markLine: {
          symbol: 'none',
          data: [{ yAxis: 60, name: '及格线' }],
          lineStyle: {
            color: '#D54941',
          },
        },
        barWidth: 15,
      },
    ],
    grid: { left: 30, right: 20, top: 30, bottom: 30 },
  };
</script>

<template>
  <CardPane
    title="项目过程符合度"
    :height="height"
    tip=""
  >
    <VChart
      :option="trendOption"
      autoresize
      style="height: 100%; width: 100%"
    />
  </CardPane>
</template>

<style scoped lang="less"></style>
