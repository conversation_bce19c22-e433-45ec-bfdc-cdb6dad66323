<script setup lang="ts">
  import { debounce } from 'lodash-es';

  import { getAllProjectList, getProjectList, type Project } from '@/apis/project/project';

  defineProps<{ modelValue: string }>();
  defineEmits(['update:modelValue']);

  const project = defineModel<string | undefined>('modelValue', { default: '' });

  const projectList = ref([] as Project[]);
  const loading = ref(false);
  project.value = projectList.value[0]?.value || '';
  function getData(name?: string) {
    loading.value = true;
    getProjectList({
      pageIndex: 1,
      pageSize: 10000,
      programmeName: name,
    })
      .then((res) => {
        projectList.value = res.data?.data || [];
        projectList.value.length && (project.value = projectList.value[0].id);
      })
      .finally(() => {
        loading.value = false;
      });
  }
  getData();
  const handleSearch = debounce((value: string) => getData(value), 300);
  function getPopupContainer(triggerNode: HTMLElement) {
    return triggerNode.parentElement || document.body;
  }
</script>

<template>
  <ASelect
    ref="select"
    v-model:value="project"
    class="rounded-select w-180px"
    show-search
    :filter-option="false"
    :default-active-first-option="false"
    :not-found-content="loading ? undefined : null"
    :get-popup-container="getPopupContainer"
    @search="handleSearch"
  >
    <template
      v-if="loading"
      #notFoundContent
    >
      <ASpin size="small" />
    </template>
    <ASelectOption
      v-for="item in projectList"
      :key="item.id"
      :value="item.id"
    >
      {{ item.programmeName }}
    </ASelectOption>
  </ASelect>
</template>
