<script setup lang="tsx">
  import type { FormInstance, TableColumnsType } from 'ant-design-vue';
  import { reactive, ref } from 'vue';
  import type { ComponentExposed } from 'vue-component-type-helpers';

  import {
    APPROVE_STATUS,
    ApproveStatus,
    ApproveStatusDotColors,
    ApproveStatusList,
  } from '@/apis/code-generate/approval';
  import type { ListResponseItem } from '@/apis/code-generate/resources';
  import Api, { ResourceStatus, ResourceStatusColors } from '@/apis/code-generate/resources';
  import ServerPagination from '@/components/ServerPagination/index.vue';
  import TableList from '@/components/TableList/index.vue';

  import ConfigApplyAction from './components/ConfigApplyAction.vue';
  import ConfigApproveAction from './components/ConfigApproveAction.vue';
  import ConfigDetailAction from './components/ConfigDetailAction.vue';
  import ConfigReleaseAction from './components/ConfigReleaseAction.vue';

  const formRef = ref<FormInstance>();
  const formState = reactive<{
    pageIndex?: number;
    pageSize?: number;
    programmeName?: string; // 项目名称
    approvalState?: number; // 审批状态
    department?: any; // 归属部门
  }>({
    pageIndex: 1,
    pageSize: 10,
    programmeName: undefined,
    approvalState: undefined,
    department: undefined,
  });

  const serverPaginationRef = ref<ComponentExposed<typeof ServerPagination>>();
  const handleQuery = (reset?: boolean) => {
    if (reset) {
      formRef.value?.resetFields();
    }

    serverPaginationRef.value?.fetchDataButResetPage();
  };
  const handleRefresh = () => {
    serverPaginationRef.value?.fetchData();
  };

  const listLoading = ref(false);
  const list = ref<ListResponseItem[]>();
  const columns: TableColumnsType<ListResponseItem> = [
    {
      title: '项目名称',
      dataIndex: 'programmeName',
      key: 'programmeName',
      ellipsis: true,
    },
    {
      title: '项目标识',
      dataIndex: 'programmeTag',
      key: 'programmeTag',
      ellipsis: true,
    },
    {
      title: '归属部门',
      dataIndex: 'department',
      key: 'department',
      ellipsis: true,
      customRender: ({ text }: any) => {
        return <div class="text-left">{text || '--'}</div>;
      },
    },
    {
      title: '资源申请人',
      dataIndex: 'submitterName',
      key: 'submitterName',
      ellipsis: true,
    },
    {
      title: '申请时间',
      dataIndex: 'createTime',
      key: 'createTime',
      ellipsis: true,
    },
    {
      title: '资源状态',
      dataIndex: 'resourceState',
      key: 'resourceState',
      align: 'center',
    },
    {
      title: '审批状态',
      dataIndex: 'approvalState',
      key: 'approvalState',
      align: 'center',
      width: 180,
    },
    {
      title: '操作',
      key: 'actions',
      align: 'center',
      width: 140,
      // customRender: ({ record }) => (
      //   <div>
      //     <ConfigDetailAction record={record} />
      //     <ConfigApproveAction
      //       actionType="approve"
      //       record={record}
      //       onDataChange={handleRefresh}
      //     />
      //     <ConfigMoreAction />
      //   </div>
      // ),
    },
  ];
</script>

<template>
  <div class="content-box h-full">
    <TableList
      :filter-form-state="formState"
      :loading="listLoading"
      @query="serverPaginationRef?.fetchDataButResetPage"
    >
      <template #filterForm>
        <AFormItem
          label="归属部门"
          name="department"
        >
          <AInput
            v-model:value.trim="formState.department"
            placeholder="请输入名称"
            :disabled="listLoading"
          />
        </AFormItem>
        <AFormItem
          label="项目名称"
          name="programmeName"
        >
          <AInput
            v-model:value.trim="formState.programmeName"
            placeholder="请输入名称"
            :disabled="listLoading"
          />
        </AFormItem>

        <AFormItem
          label="审批状态"
          name="approvalState"
        >
          <ASelect
            v-model:value="formState.approvalState"
            placeholder="请选择状态"
            allow-clear
          >
            <ASelectOption
              v-for="item in ApproveStatusList"
              :key="item.value"
              :value="item.value"
            >
              {{ item.label }}
            </ASelectOption>
          </ASelect>
        </AFormItem>
      </template>
      <template #tableActions>
        <ConfigApplyAction @data-change="handleRefresh" />
      </template>
      <template #table>
        <ATable
          row-key="id"
          :data-source="list"
          :columns="columns"
          :pagination="false"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'resourceState'">
              <ATag
                v-if="record.resourceState"
                :color="ResourceStatusColors[record.resourceState] || ''"
                class="mr-0 cursor-pointer"
              >
                {{ ResourceStatus[record.resourceState] || '--' }}
              </ATag>
              <span v-else>--</span>
            </template>
            <template v-if="column.key === 'approvalState'">
              <div class="w-full flex items-center justify-start pl-40px">
                <span
                  class="dot mr-7px h-9px w-9px rounded-50%"
                  :style="{ backgroundColor: ApproveStatusDotColors[record.approvalState] }"
                ></span>
                <span class="">{{ ApproveStatus[record.approvalState] || '--' }}</span>
              </div>
            </template>
            <template v-else-if="column.key === 'actions'">
              <!-- <ADropdown :trigger="['hover']">
                <a
                  class="ant-dropdown-link"
                  @click.prevent
                >
                  <MoreOutlined />
                </a>
                <template #overlay>
                  <AMenu> -->
              <ConfigDetailAction :record="record" />
              <ConfigApproveAction
                v-if="record.hasPermission"
                action-type="approve"
                :record="record"
                :disabled="record.approvalState === APPROVE_STATUS.PROCESSING"
                @data-change="handleRefresh"
              />
              <ConfigReleaseAction
                :record="record"
                @data-change="handleRefresh"
              ></ConfigReleaseAction>
              <!-- </AMenu>
                </template>
              </ADropdown> -->
            </template>
          </template>
        </ATable>
      </template>
      <template #pagination>
        <ServerPagination
          ref="serverPaginationRef"
          :request="
            ({ pageIndex, pageSize }) =>
              Api.list({
                ...formState,
                pageIndex,
                pageSize,
              })
          "
          @loading-change="(val) => (listLoading = val)"
          @list-change="(val) => (list = val)"
        />
      </template>
    </TableList>
  </div>
</template>
