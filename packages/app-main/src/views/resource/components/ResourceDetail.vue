<script setup lang="tsx">
  import { watchImmediate } from '@vueuse/core';
  import { message } from 'ant-design-vue';
  import { to } from 'await-to-js';
  import * as R from 'remeda';
  import { ref } from 'vue';

  import type { EditParams, ListResponseItem } from '@/apis/code-generate/resources';
  import Api, { ResourceStatus, ResourceStatusColors } from '@/apis/code-generate/resources';

  const route = useRoute();

  const props = defineProps<{
    record?: ListResponseItem;
    loading?: boolean;
  }>();

  const emit = defineEmits<{
    (event: 'update:loading', data: any): void;
  }>();

  const defaultFormState: EditParams = {
    id: null, // 主键
    department: '', // 部门名称
    approvalState: '', // 审批状态
    programmeName: '', // 项目名称
    programmeTag: '', // 项目标识
    submitter: '', // 申请人
    submitterName: '', // 申请人
    createTime: '', // 申请时间
    server: null, // 资源列表
    middlewares: null, // 中间件列表
    resourceState: '', // 资源状态
  };
  const formState = ref(R.clone(defaultFormState));

  const queryDetailLoading = ref(true);
  const queryDetail = async () => {
    const record = props.record;
    const queryId = route.params?.id || record?.id;
    if (!queryId) {
      message.error('记录不存在，请联系管理员');
      return;
    }

    queryDetailLoading.value = true;
    const [err, res] = await to(Api.detail(queryId));
    queryDetailLoading.value = false;
    if (err || !res.data.data) {
      return;
    }

    const detial = res.data.data;
    Object.keys(formState.value).forEach((key) => {
      if (key === 'server') {
        formState.value[key] = detial[key]?.resourceExtInfo ? detial[key].resourceExtInfo : null;
      } else {
        formState.value[key] = detial[key] || undefined;
      }
    });
  };

  watchImmediate(queryDetailLoading, (val) => {
    emit('update:loading', val);
  });

  onMounted(() => {
    queryDetail();
  });
</script>

<template>
  <ADescriptions
    size="small"
    class="w-full"
    :class="{
      'opacity-0': queryDetailLoading,
    }"
    bordered
    :column="2"
    :label-style="{ width: '120px' }"
  >
    <ADescriptionsItem label="项目名称">{{ formState.programmeName }}</ADescriptionsItem>
    <ADescriptionsItem label="资源状态">
      <ATag
        :color="ResourceStatusColors[formState.resourceState] || ''"
        class="mr-0 cursor-pointer"
      >
        {{ ResourceStatus[formState.resourceState] || '--' }}
      </ATag>
    </ADescriptionsItem>
    <ADescriptionsItem label="项目标识">{{ formState.programmeTag || '--' }}</ADescriptionsItem>
    <ADescriptionsItem label="归属部门">{{ formState.department || '--' }}</ADescriptionsItem>
    <ADescriptionsItem label="资源申请人">{{ formState.submitterName || '--' }}</ADescriptionsItem>
    <ADescriptionsItem label="申请时间">{{ formState.createTime || '--' }}</ADescriptionsItem>
    <ADescriptionsItem
      v-if="formState.server"
      label="服务器资源"
      :span="2"
    >
      <ADescriptions
        size="small"
        bordered
        :column="2"
        :label-style="{ width: '120px' }"
      >
        <ADescriptionsItem label="资源名称">
          {{ formState.server.name || '--' }}
        </ADescriptionsItem>
        <ADescriptionsItem label="CPU(核)">
          {{ formState.server.cpuSize || '--' }}
        </ADescriptionsItem>
        <ADescriptionsItem label="内存(G)">
          {{ formState.server.memorySize || '--' }}
        </ADescriptionsItem>
        <ADescriptionsItem label="磁盘(G)">
          {{ formState.server.diskSize || '--' }}
        </ADescriptionsItem>
        <ADescriptionsItem
          label="端口"
          :span="2"
        >
          <ADescriptions
            v-if="formState.server?.port?.length"
            size="small"
            bordered
            :column="1"
            :label-style="{ width: '140px' }"
          >
            <ADescriptionsItem
              v-for="(port, idx) in formState.server?.port"
              :key="idx"
              :label="port.name"
            >
              {{ port.port }}
            </ADescriptionsItem>
          </ADescriptions>
          <div v-else>--</div>
        </ADescriptionsItem>
        <ADescriptionsItem
          label="使用时间"
          :span="2"
        >
          {{
            formState.server.validDateRange && formState.server.validDateRange[0]
              ? formState.server.validDateRange[0]
              : '--'
          }}
          至
          {{
            formState.server.validDateRange && formState.server.validDateRange[1]
              ? formState.server.validDateRange[1]
              : '--'
          }}
        </ADescriptionsItem>
      </ADescriptions>
    </ADescriptionsItem>
    <ADescriptionsItem
      v-if="formState.middlewares?.length"
      label="中间件资源"
      :span="2"
    >
      <ADescriptions
        size="small"
        bordered
        :column="1"
        :label-style="{ width: '120px' }"
      >
        <ADescriptionsItem
          v-for="(ware, idx) in formState.middlewares"
          :key="idx"
          :label="ware.code"
        >
          version：{{ ware.version }}
        </ADescriptionsItem>
      </ADescriptions>
    </ADescriptionsItem>
  </ADescriptions>
</template>

<style lang="less" scoped></style>
