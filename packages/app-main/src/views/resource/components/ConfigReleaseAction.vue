<script setup lang="tsx">
  import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
  import { message, Modal } from 'ant-design-vue';
  import { to } from 'await-to-js';
  import * as R from 'remeda';
  import { nextTick, ref } from 'vue';

  import { APPROVE_STATUS } from '@/apis/code-generate/approval';
  import type { EditParams, ListResponseItem } from '@/apis/code-generate/resources';
  import Api, { RESOURCE_STATUS } from '@/apis/code-generate/resources';

  const props = defineProps<{
    record: ListResponseItem;
    actionType?: 'open' | 'release'; // 开通、释放
  }>();
  const emit = defineEmits<{
    (event: 'dataChange'): void;
  }>();

  const isHandle = computed(
    () =>
      props.record?.approvalState === APPROVE_STATUS.PASS &&
      props.record?.resourceState !== RESOURCE_STATUS.RELEASE,
  );
  const isOpen = computed(() => props.actionType === 'open' || !props.record?.resourceState);
  const isRelease = computed(
    () => props.actionType === 'release' || props.record?.resourceState === RESOURCE_STATUS.OPEN,
  );

  const modalTitle = computed(() => {
    if (isOpen.value) return '开通';
    if (isRelease.value) return '释放';
    return '开通';
  });

  const openState = computed(() => {
    const { resourceState } = props.record;
    if (resourceState === RESOURCE_STATUS.OPEN) return RESOURCE_STATUS.RELEASE;
    if (resourceState === RESOURCE_STATUS.RELEASE) return RESOURCE_STATUS.OPEN;
    return RESOURCE_STATUS.OPEN;
  });

  const handleModalOk = () => {
    Modal.confirm({
      title: `${modalTitle.value}提示`,
      icon: <ExclamationCircleOutlined />,
      content: `确定 ‘${modalTitle.value}’ 此项目服务器资源？`,
      okText: '确定',
      cancelText: '取消',
      async onOk() {
        if (!props.record) {
          message.error('记录不存在，请联系管理员');
          return;
        }

        const { bizId } = props.record;
        const [err, res] = await to(
          Api.resourceStatusChange({ bizId, openState: openState.value }),
        );

        if (err || !res.data.data) {
          return;
        }

        message.success(`${modalTitle.value}成功`);
        emit('dataChange');
      },
    });
  };
</script>

<template>
  <AButton
    v-if="isHandle"
    key="release"
    type="link"
    size="small"
    @click="handleModalOk"
  >
    {{ modalTitle }}
  </AButton>
</template>

<style lang="less" scoped></style>
