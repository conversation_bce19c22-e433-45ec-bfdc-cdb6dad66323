<script setup lang="tsx">
  import { message } from 'ant-design-vue';
  import { to } from 'await-to-js';
  import * as R from 'remeda';
  import { nextTick, ref } from 'vue';

  import type { EditParams, ListResponseItem } from '@/apis/code-generate/resources';
  import Api from '@/apis/code-generate/resources';

  import ApproveList from './ApproveList.vue';
  import ResourceDetail from './ResourceDetail.vue';

  const props = defineProps<{
    record: ListResponseItem;
  }>();

  const modalKey = ref(0);
  const defaultFormState: EditParams = {
    id: null, // 主键
    department: '', // 部门名称
    approvalState: '', // 审批状态
    programmeName: '', // 项目名称
    programmeTag: '', // 项目标识
    submitter: '', // 申请人
    submitterName: '', // 申请人
    createTime: '', // 申请时间
    server: null, // 资源列表
    middlewares: null, // 中间件列表
  };
  const formState = ref(R.clone(defaultFormState));

  const queryDetailLoading = ref(true);
  const queryDetail = async () => {
    const record = props.record;
    if (!record) {
      message.error('记录不存在，请联系管理员');
      return;
    }

    queryDetailLoading.value = true;
    const [err, res] = await to(Api.detail(record.id));
    queryDetailLoading.value = false;
    if (err || !res.data.data) {
      return;
    }

    const detial = res.data.data;
    Object.keys(formState.value).forEach((key) => {
      if (key === 'server') {
        formState.value[key] = detial[key]?.resourceExtInfo ? detial[key].resourceExtInfo : null;
      } else {
        formState.value[key] = detial[key] || undefined;
      }
    });
  };

  const isModalOpen = ref(false);
  const openModal = async () => {
    formState.value = R.clone(defaultFormState);
    modalKey.value += 1;
    await nextTick();
    isModalOpen.value = true;
    queryDetail();
  };

  const cancelModal = () => {
    formState.value = R.clone(defaultFormState);
    tabActive.value = 1;
    isModalOpen.value = false;
    modalKey.value += 1; // 重新渲染
  };

  const tabActive = ref(1);
  const tabPaneData = [
    {
      key: 1,
      tab: '资源详情',
    },
    {
      key: 2,
      tab: '审批记录',
    },
  ];
</script>

<template>
  <AButton
    key="detial"
    type="link"
    size="small"
    @click="openModal"
  >
    详情
  </AButton>

  <AModal
    :key="modalKey"
    v-model:open="isModalOpen"
    title=""
    width="760px"
    :footer="null"
    @cancel="cancelModal"
  >
    <ASpin :spinning="queryDetailLoading">
      <ATabs
        v-model:active-key="tabActive"
        class="mt-[-10px]"
      >
        <ATabPane
          v-for="item in tabPaneData"
          :key="item.key"
          :tab="item.tab"
        ></ATabPane>
      </ATabs>

      <!-- 资源详情 -->
      <ResourceDetail
        v-show="tabActive === 1"
        v-model:loading="queryDetailLoading"
        :record="record"
      />

      <!-- 流程列表 -->
      <span v-show="tabActive === 2">
        <ApproveList
          :record="props.record"
          :loading="queryDetailLoading"
        />
      </span>

      <div class="text-right">
        <AButton
          class="mt-20px"
          @click="cancelModal"
        >
          关闭
        </AButton>
      </div>
    </ASpin>
  </AModal>
</template>

<style lang="less" scoped></style>
