<script setup lang="tsx">
import { EditOutlined, PlusOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import type { FormInstance } from 'ant-design-vue/es/form';
import { to } from 'await-to-js';
import { h, nextTick, ref } from 'vue';

import type { ListResponseItem } from '@/apis/code-generate/resources';
import Api, { RESOURCE_STATUS } from '@/apis/code-generate/resources';
import { defaultStep3Form } from '@/stores/startDevForm';

import ProjectSelect from '../../project/components/ProjectSelect.vue';
import ApplyResourceTrigger from '../../project/start-dev/components/ApplyResourceTrigger.vue';

const props = defineProps<{
  record?: ListResponseItem;
  actionType?: string;
  disabled?: boolean;
}>();
const emit = defineEmits<{
  (event: 'dataChange'): void;
}>();

const isApply = computed(
  () => props.actionType === 'apply' || props.record?.resourceState === RESOURCE_STATUS.OPEN,
);

const formState = ref({ programmeId: undefined, ...defaultStep3Form() });
const resourceList = ref<any>([]);
const validateResource = async (rule: any, value: any) => {
  if (!value?.name) {
    throw new Error('请选择服务器资源');
  }
};

const formParam = computed(() => {
  resourceList.value[0].resourceExtInfo = formState.value.resource
    ? JSON.stringify(formState.value.resource)
    : '';
  formState.value.middleware?.forEach((middlewareId: string) => {
    resourceList.value.push({
      id: middlewareId,
      resourceExtInfo: '',
    });
  });
  return {
    programmeId: formState.value.programmeId || '',
    resourceList: resourceList.value,
  };
});

const middlewareOptions = ref<any>([]);
const getMiddlewareOptions = async () => {
  const [err, res] = await to(Api.resourceList());
  if (err || !res.data.data) {
    return;
  }
  const middlewares = res.data?.data?.middleware || [];
  middlewareOptions.value = middlewares.map((item: any) => ({
    label: `${item.code}:(${item.version})`,
    value: item.id,
    description: item.description || '',
  }));

  const server = res.data?.data?.server || [];
  const serverInfo = server[0] ? server[0] : null;
  serverInfo &&
    resourceList.value.unshift({
      id: serverInfo.id,
      type: serverInfo.type,
      resourceExtInfo: '',
    });
};

const formRef = ref<FormInstance>();
const isModalOpen = ref(false);
const modalKey = ref(0);
const openModal = async () => {
  modalKey.value += 1;
  await nextTick();
  isModalOpen.value = true;
  getMiddlewareOptions();
};
const cancelModal = () => {
  formState.value = { programmeId: undefined, ...defaultStep3Form() };
  isModalOpen.value = false;
};

const submitLoading = ref(false);
const handleApprove = async () => {
  submitLoading.value = true;
  const [err, res] = await to(Api.resourceApply(formParam.value));
  submitLoading.value = false;

  if (err || !res.data.success) {
    return;
  }

  message.success('申请成功');
  isModalOpen.value = false;
  emit('dataChange');
};

// 处理模态框确认操作
const handleModalOk = async () => {
  if (!formRef.value) {
    message.error('表单实例不存在，请联系管理员');
    return;
  }

  const [validateErr] = await to(formRef.value?.validate());
  if (validateErr) {
    return;
  }

  handleApprove();
};
</script>

<template>
  <AButton type="dashed" :icon="h(PlusOutlined)" :disabled="props.disabled" @click="openModal">
    资源申请
  </AButton>
  <AModal :key="modalKey" v-model:open="isModalOpen" title="资源申请" width="700px" :confirm-loading="submitLoading"
    :footer="false" @cancel="cancelModal">
    <AForm ref="formRef" class="mx-auto mt-8 w-[80%]" :model="formState" autocomplete="off" label-align="left"
      label-width="180px">
      <AFormItem name="programmeId" label="所属项目" :rules="[{ required: true, message: '请选择' }]">
        <ProjectSelect v-model:value.trim="formState.programmeId" :query="{
          devStates: '0,1',
        }" />
      </AFormItem>
      <AFormItem
        name="resource"
        label="服务器资源"
        :rules="[{ required: true, validator: validateResource, trigger: 'change' }]"
      >
        <ApplyResourceTrigger v-model:value="formState.resource" />
      </AFormItem>
      <!-- 屏蔽中间件选择 -->
      <!-- <AFormItem
        label="中间件资源"
        name="middleware"
        :rules="[{ required: false, message: '请选择中间件资源' }]"
      >
        <ASelect
          v-model:value="formState.middleware"
          mode="multiple"
          show-search
          allow-clear
          option-filter-prop="label"
          placeholder="请选择"
          :options="middlewareOptions"
        />
      </AFormItem> -->
    </AForm>
    <div class="text-right">
      <AButton key="back" class="mr-12px" :loading="submitLoading" @click="cancelModal">
        取消
      </AButton>
      <AButton key="submit" type="primary" :disabled="submitLoading" :loading="submitLoading" @click="handleModalOk">
        确定
      </AButton>
    </div>
  </AModal>
</template>

<style lang="less" scoped></style>
