<script setup lang="tsx">
  import { SmileOutlined } from '@ant-design/icons-vue';
  import { message, type StepProps, Tag } from 'ant-design-vue';
  import { to } from 'await-to-js';
  import { h, onMounted, ref } from 'vue';

  import {
    APPROVE_STATUS,
    ApproveFlowStatus,
    ApproveModeColors,
    ApproveModes,
    ApproveStatus,
    ApproveStatusColors,
    ApproveStatusTag,
  } from '@/apis/code-generate/approval';
  import type { FlowItemDetail, ListResponseItem } from '@/apis/code-generate/resources';
  import Api from '@/apis/code-generate/resources';

  const route = useRoute();

  const props = defineProps<{
    record?: ListResponseItem;
    loading?: boolean;
  }>();

  const flowStatusList = ref<any[]>([]);
  const approvalMode = ref('');
  const queryDetailLoading = ref(true);
  const queryDetail = async () => {
    const record = props.record;
    const queryId = route.params?.id || record?.id;
    if (!queryId) {
      message.error('记录不存在，请联系管理员');
      return;
    }

    queryDetailLoading.value = true;
    const [err, res] = await to(Api.approveList(queryId));
    queryDetailLoading.value = false;
    if (err || !res.data.data) {
      return;
    }

    const { approvalMode: mode, approvalRecordList } = res.data?.data || {};
    approvalMode.value = mode || '';
    const flowList = approvalRecordList || [];
    handleFlowList(flowList);
  };

  // 处理流程列表
  const handleFlowList = (flowList: FlowItemDetail[]) => {
    let endFlow: any = null;
    flowStatusList.value = flowList?.map((item, index) => {
      if (index === flowList.length - 1 && item.approvalState === APPROVE_STATUS.PASS)
        endFlow = item;
      const { approverName, approvalTime, approvalState, approvalOpinion } = item;
      const approvalStateTagColor = approvalState ? ApproveStatusColors[approvalState] : 'default';
      const approvalStateTagName = approvalState ? ApproveStatusTag[approvalState] : '';
      const description = (() => {
        if (approvalOpinion) return approvalOpinion;
        if (approvalState === APPROVE_STATUS.PASS) return '同意';
        return '--';
      })();
      return {
        title: h('span', {}, [
          approverName,
          h(
            Tag,
            {
              style: { marginLeft: '8px' },
              color: approvalStateTagColor,
            },
            approvalStateTagName,
          ),
        ]),
        subTitle: approvalTime || '',
        description,
        status: approvalState ? ApproveFlowStatus[approvalState] : 'wait',
        approveStatusName: approvalState ? ApproveStatus[approvalState] : '--',
      };
    });

    // 若存在当前任务，则表示当前任务未完成，否则表示已完成
    if (endFlow) {
      const endFlow: StepProps = {
        title: '当前',
        subTitle: '',
        description: h(<span class="color-[rgba(0,0,0,.3)]">审批结束</span>),
        status: 'finish',
        icon: h(SmileOutlined, {
          style: { color: 'var(--ant-colorSuccess)', fontSize: '22px' },
        }),
      };
      flowStatusList.value.push(endFlow);
      return;
    }
  };

  const getModeBgColor = (mode: string) => {
    const modeColor = ApproveModeColors[mode] || '';
    if (modeColor) return { background: modeColor };
    return {};
  };
  onMounted(() => {
    queryDetail();
  });
</script>

<template>
  <div
    v-if="approvalMode"
    class="w-full"
  >
    <span
      v-if="1 || ApproveModes[approvalMode]"
      :style="getModeBgColor(approvalMode)"
      class="mb-20px ml-10px h-22px w-max flex items-center rounded-bl-lg rounded-tr-lg px-2.5 py-0.5 text-12px font-500 leading-18px tracking-wider text-white shadow-[0_2px_8px_rgba(255,180,0,0.08)]"
    >
      {{ ApproveModes[approvalMode] }}
    </span>
  </div>
  <ASteps
    v-if="flowStatusList.length"
    direction="vertical"
    size="small"
    :items="flowStatusList"
    class="px-4px"
  ></ASteps>
  <AEmpty
    v-if="!flowStatusList.length"
    :class="{
      'opacity-0': queryDetailLoading || loading,
    }"
    class="my-30px scale-80 text-16px color-#999"
  />
</template>

<style lang="less" scoped>
  :deep(.ant-descriptions-item-label) {
    width: 110px;
  }
</style>
