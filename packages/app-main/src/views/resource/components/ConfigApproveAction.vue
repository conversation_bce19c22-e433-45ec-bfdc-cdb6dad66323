<script setup lang="tsx">
  import type { FormInstance } from 'ant-design-vue';
  import { message } from 'ant-design-vue';
  import { to } from 'await-to-js';
  import * as R from 'remeda';
  import { computed, nextTick, ref } from 'vue';

  import {
    APPROVE_STATUS,
    ApproveStatus,
    ApproveStatusColors,
    APPROVE_STATUS_STRS,
  } from '@/apis/code-generate/approval';
  import type { EditParams, ListResponseItem } from '@/apis/code-generate/resources';
  import Api from '@/apis/code-generate/resources';

  const props = defineProps<{
    actionType: 'approve'; // 申请、释放、查看、审批
    record?: ListResponseItem;
    disabled?: boolean;
  }>();
  const emit = defineEmits<{
    (event: 'dataChange'): void;
  }>();

  const isApprove = computed(
    () =>
      props.actionType === 'approve' &&
      props.record?.approvalState !== APPROVE_STATUS.REJECT &&
      props.record?.approvalState !== APPROVE_STATUS.PASS,
  );

  const modalKey = ref(0);

  const formRef = ref<FormInstance>();
  const defaultFormState: EditParams = {
    id: null, // 主键
    department: '', // 部门名称
    approvalState: '', // 审批状态
    programmeName: '', // 项目名称
    programmeTag: '', // 项目标识
    submitterName: '', // 申请人
    createTime: '', // 申请时间
    server: null, // 资源列表
    middlewares: null, // 中间件列表
    approvalOpinion: '', // 审批意见
  };
  const formState = ref(R.clone(defaultFormState));

  const formParam = computed(() => {
    const params = {
      id: formState.value.id,
      approvalOpinion: formState.value.approvalOpinion,
      approvalState: formState.value.approvalState,
    };
    return params;
  });

  const queryDetailLoading = ref(false);
  const queryDetail = async () => {
    const record = props.record;
    if (!record) {
      message.error('记录不存在，请联系管理员');
      return;
    }

    queryDetailLoading.value = true;
    const [err, res] = await to(Api.detail(record.id));
    queryDetailLoading.value = false;
    if (err || !res.data.data) {
      return;
    }

    const detial = res.data.data;
    Object.keys(formState.value).forEach((key) => {
      if (key === 'server') {
        formState.value[key] = detial[key]?.resourceExtInfo ? detial[key].resourceExtInfo : null;
      } else {
        formState.value[key] = detial[key] || undefined;
      }
    });
  };

  const isModalOpen = ref(false);
  const openModal = async () => {
    formState.value = R.clone(defaultFormState);

    modalKey.value += 1;
    await nextTick();
    isModalOpen.value = true;

    queryDetail();
  };
  const cancelModal = () => {
    formState.value = R.clone(defaultFormState);
    isModalOpen.value = false;
  };

  const submitLoading = ref(false);
  const handleApprove = async (isAgree?: boolean) => {
    formState.value.approvalState = isAgree ? APPROVE_STATUS.PASS : APPROVE_STATUS.REJECT;

    submitLoading.value = true;
    const [err, res] = await to(Api.approve(formParam.value));
    submitLoading.value = false;

    if (err || !res.data.success) {
      return;
    }

    emit('dataChange');
    message.success(isAgree ? APPROVE_STATUS_STRS.PASS : APPROVE_STATUS_STRS.REJECT);
    isModalOpen.value = false;
  };

  // 处理模态框确认操作，可选参数 isAgree 表示是否同意
  const handleModalOk = async (isAgree?: any) => {
    if (!formRef.value) {
      message.error('表单实例不存在，请联系管理员');
      return;
    }

    const [validateErr] = await to(formRef.value?.validate());
    if (validateErr) {
      return;
    }

    // 如果是同意审批，则调用 handleApprove 方法
    handleApprove(!!isAgree);
  };
</script>

<template>
  <AButton
    v-if="isApprove"
    key="approve"
    type="link"
    size="small"
    :disabled="props.disabled"
    @click="openModal"
  >
    审批
  </AButton>

  <AModal
    :key="modalKey"
    v-model:open="isModalOpen"
    title="资源审批"
    width="680px"
    :confirm-loading="submitLoading"
    :footer="false"
    @ok="handleModalOk"
  >
    <ASpin :spinning="queryDetailLoading">
      <ADescriptions
        size="small"
        class="w-full"
        :class="{
          'opacity-0': queryDetailLoading,
        }"
        bordered
        :column="2"
        :label-style="{ width: '120px' }"
      >
        <ADescriptionsItem label="项目名称">{{ formState.programmeName }}</ADescriptionsItem>
        <ADescriptionsItem label="审批状态">
          <ATag
            :color="ApproveStatusColors[formState.approvalState] || ''"
            class="mr-0 cursor-pointer"
          >
            {{ ApproveStatus[formState.approvalState] || '--' }}
          </ATag>
        </ADescriptionsItem>
        <ADescriptionsItem label="项目标识">{{ formState.programmeTag }}</ADescriptionsItem>
        <ADescriptionsItem label="归属部门">{{ formState.department }}</ADescriptionsItem>
        <ADescriptionsItem label="资源申请人">{{ formState.submitterName }}</ADescriptionsItem>
        <ADescriptionsItem label="申请时间">{{ formState.createTime }}</ADescriptionsItem>
        <ADescriptionsItem
          v-if="formState.server"
          label="服务器资源"
          :span="2"
        >
          <ADescriptions
            size="small"
            bordered
            :column="2"
            :label-style="{ width: '120px' }"
          >
            <ADescriptionsItem label="资源名称">
              {{ formState.server.resourceName }}
            </ADescriptionsItem>
            <ADescriptionsItem label="CPU(核)">
              {{ formState.server.cpuCores }}
            </ADescriptionsItem>
            <ADescriptionsItem label="内存(G)">{{ formState.server.memory }}</ADescriptionsItem>
            <ADescriptionsItem label="磁盘(G)">{{ formState.server.disk }}</ADescriptionsItem>
            <ADescriptionsItem
              v-if="formState.server?.ports?.length"
              label="端口"
              :span="2"
            >
              <div
                v-for="(port, idx) in formState.server?.ports"
                :key="idx"
                style="margin-bottom: 4px"
              >
                {{ port.portName }}：{{ port.port }}
              </div>
            </ADescriptionsItem>
            <ADescriptionsItem
              label="使用时间"
              :span="2"
            >
              {{ formState.server.beginTime }} 至 {{ formState.server.endTime }}
            </ADescriptionsItem>
          </ADescriptions>
        </ADescriptionsItem>
        <ADescriptionsItem
          v-if="formState.middlewares?.length"
          label="中间件资源"
          :span="2"
        >
          <ADescriptions
            size="small"
            bordered
            :column="1"
            :label-style="{ width: '120px' }"
          >
            <ADescriptionsItem
              v-for="(ware, idx) in formState.middlewares"
              :key="idx"
              :label="ware.code"
            >
              version：{{ ware.version }}
            </ADescriptionsItem>
          </ADescriptions>
        </ADescriptionsItem>
      </ADescriptions>
      <AForm
        ref="formRef"
        :model="formState"
        autocomplete="off"
        label-align="left"
        :label-col="{ style: { width: '110px' } }"
        class="mt-10px p-x-20px"
      >
        <ARow>
          <ACol :span="24">
            <AFormItem
              label="  审批意见"
              name="approvalOpinion"
              :rules="[{ required: false }]"
            >
              <ATextarea
                v-model:value.trim="formState.approvalOpinion"
                placeholder="请输入描述"
                allow-clear
                show-count
                :maxlength="250"
              />
            </AFormItem>
          </ACol>
        </ARow>
      </AForm>
      <div class="text-right">
        <AButton
          key="back"
          class="mr-12px"
          :loading="submitLoading"
          @click="cancelModal"
        >
          取消
        </AButton>
        <AButton
          key="reject"
          class="mr-12px"
          danger
          :disabled="submitLoading"
          :loading="submitLoading"
          @click="(e) => handleModalOk(false)"
        >
          拒绝
        </AButton>
        <AButton
          key="submit"
          type="primary"
          :disabled="submitLoading"
          :loading="submitLoading"
          @click="handleModalOk"
        >
          同意
        </AButton>
      </div>
    </ASpin>
  </AModal>
</template>
