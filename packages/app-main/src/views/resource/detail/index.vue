<script setup lang="tsx">
  import { toRef } from '@vueuse/core';
  import { message } from 'ant-design-vue';
  import { to } from 'await-to-js';
  import * as R from 'remeda';
  import { ref } from 'vue';

  import { APPROVE_STATUS, APPROVE_TYPE } from '@/apis/code-generate/approval';
  import type { EditParams } from '@/apis/code-generate/resources';
  import Api from '@/apis/code-generate/resources';

  import ApproveList from '../components/ApproveList.vue';
  import ResourceDetail from '../components/ResourceDetail.vue';

  const DETAIL = 'detail';

  const route = useRoute();
  const router = useRouter();
  const id = toRef(route.params, 'id');
  const bizType = toRef(route.query, 'bizType');

  const isDetail = computed(() => {
    return bizType.value === DETAIL || !bizType.value;
  });

  const defaultFormState: EditParams = {
    id: id.value, // 主键
    approvalState: '', // 审批状态
    approvalOpinion: '', // 项目名称
  };
  const formState = ref(R.clone(defaultFormState));
  const formParam = computed(() => {
    const params = {
      id: formState.value.id,
      approvalOpinion: formState.value.approvalOpinion,
      approvalState: formState.value.approvalState,
    };
    return params;
  });

  const pageTitle = computed(() => {
    if (bizType.value === APPROVE_TYPE.RESOURCE) {
      return '项目研发资源审批';
    } else if (bizType.value === APPROVE_TYPE.PROCESS) {
      return '项目阶段推进审批';
    } else {
      return '资源详情';
    }
  });

  const back = () => {
    switch (bizType.value) {
      case APPROVE_TYPE.RESOURCE:
      case 'detail':
        router.push('/approval');
        break;
      default:
        router.push('/resource');
        break;
    }
  };

  const isApproveStatus = ref(false);
  const formRef = ref();
  const submitLoading = ref(false);
  // 处理模态框确认操作，可选参数 isAgree 表示是否同意
  const handleApproveOk = async (isAgree?: any) => {
    if (!formRef.value) {
      message.error('表单实例不存在，请联系管理员');
      return;
    }

    const [validateErr] = await to(formRef.value?.validate());
    if (validateErr) {
      return;
    }

    // 如果是同意审批，则调用 handleApprove 方法
    approve(!!isAgree);
  };
  // 审批
  const approve = async (isAgree?: boolean) => {
    formState.value.approvalState = isAgree ? APPROVE_STATUS.PASS : APPROVE_STATUS.REJECT;

    submitLoading.value = true;
    const [err, res] = await to(Api.approve(formParam.value));
    submitLoading.value = false;

    if (err || !res.data.success) {
      return;
    }
    isApproveStatus.value = true;
    message.success('审批成功');
  };

  const queryDetailLoading = ref(false);
  const okText = computed(() => {
    if (isApproveStatus.value) return '已审批';
    return '同意';
  });

  const tabActive = ref(1);
  const tabPaneData = [
    {
      key: 1,
      tab: '资源详情',
    },
    {
      key: 2,
      tab: '审批记录',
    },
  ];
</script>

<template>
  <div class="min-h-screen">
    <!-- <div
      class="bg mx-auto mb-6 max-w-1200 rounded-8px px-10 pb-6 pt-8 shadow-[0_2px_8px_0_rgba(24,144,255,0.04)]"
    >
      <div class="mb-3 text-[20px] font-600 text-#222">{{ pageTitle }}</div>
      <div class="text-[16px] leading-relaxed text-#666">
        研发一体化平台提供通用技术架构能力，组件一键复用能力，代码快速生成能力，提升高业务的开发效率，欢迎一起来解锁～
      </div>
    </div> -->
    <ACard
      class="mx-auto max-w-1200 min-h-75 rounded-8px shadow-[0_2px_8px_0_rgba(24,144,255,0.04)]"
      :bordered="false"
    >
      <AFlex justify="space-between">
        <div class="cursor-pointer mb-3 text-16px font-normal text-#565865" @click="back">
          <LeftCircleOutlined style="color: #d8d8d8; margin-right: 8px" />
          返回
        </div>
      </AFlex>

      <ATabs
        v-model:active-key="tabActive"
        class="mt-[-10px]"
      >
        <ATabPane
          v-for="item in tabPaneData"
          :key="item.key"
          :tab="item.tab"
        ></ATabPane>
      </ATabs>
      <ASpin :spinning="queryDetailLoading">
        <!-- 资源详情 -->
        <ResourceDetail
          v-show="tabActive === 1"
          v-model:loading="queryDetailLoading"
        />

        <!-- 流程列表 -->
        <span v-show="tabActive === 2">
          <ApproveList :loading="queryDetailLoading" />
        </span>

        <AForm
          v-if="!isDetail && tabActive === 1"
          ref="formRef"
          :model="formState"
          autocomplete="off"
          label-align="left"
          :label-col="{ style: { width: '110px' } }"
          class="mt-20px p-x-20px"
        >
          <ARow>
            <ACol :span="24">
              <AFormItem
                :class="{ 'opacity-0': queryDetailLoading }"
                label="审批意见"
                name="approvalOpinion"
                :rules="[{ required: false }]"
              >
                <ATextarea
                  v-model:value.trim="formState.approvalOpinion"
                  :disabled="isApproveStatus || submitLoading"
                  placeholder="请输入描述"
                  allow-clear
                  show-count
                  :maxlength="250"
                />
              </AFormItem>
            </ACol>
          </ARow>
        </AForm>
      </ASpin>

      <div class="mt-20px text-center">
        <AButton
          key="back"
          @click="back"
        >
          返回
        </AButton>
        <AButton
          v-if="!isApproveStatus && !isDetail && tabActive === 1"
          key="reject"
          class="ml-12px"
          danger
          :disabled="queryDetailLoading || submitLoading"
          :loading="submitLoading"
          @click="(e) => handleApproveOk(false)"
        >
          拒绝
        </AButton>
        <AButton
          v-if="!isDetail && tabActive === 1"
          key="submit"
          class="ml-12px"
          type="primary"
          :disabled="isApproveStatus || queryDetailLoading || submitLoading"
          :loading="submitLoading"
          @click="handleApproveOk"
        >
          {{ okText }}
        </AButton>
      </div>
    </ACard>
  </div>
</template>

<style lang="less" scoped>
  .bg {
    background: linear-gradient(
      348deg,
      rgba(217, 228, 253, 1) 0%,
      rgba(233, 240, 254, 1) 8%,
      rgba(252, 253, 255, 1) 37%,
      rgba(255, 255, 255, 1) 59%,
      rgba(227, 252, 255, 1) 100%
    );
  }
</style>
