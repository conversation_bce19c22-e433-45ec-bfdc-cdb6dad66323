server {
  server_name localhost;
  server_tokens off;
  listen 80;
  charset utf-8;
  client_max_body_size 1024M;

  add_header 'Access-Control-Allow-Origin' '*' always;
  add_header 'Access-Control-Allow-Methods' '*' always;
  add_header 'Access-Control-Allow-Headers' '*' always;
  add_header 'Access-Control-Expose-Headers' '*' always;
  if ($request_method = OPTIONS) {
    return 204;
  }

  location /web/v1/ {
    proxy_pass $api_server_url;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection 'Upgrade';
  }

  location / {
    root /usr/share/nginx/html;
    try_files $uri $uri/ /index.html;
    gzip on;
    gzip_types text/plain application/x-javascript application/javascript  application/json text/css application/xml text/javascript image/jpeg image/gif image/png;

    location ~* \.(html)$ {
      add_header Cache-Control 'no-store, no-cache, must-revalidate, post-check=0, pre-check=0';
      add_header Pragma 'no-cache';
      expires off;

      add_header 'Access-Control-Allow-Origin' '*' always;
      add_header 'Access-Control-Allow-Methods' '*' always;
      add_header 'Access-Control-Allow-Headers' '*' always;
      add_header 'Access-Control-Expose-Headers' '*' always;
      if ($request_method = OPTIONS) {
        return 204;
      }
    }
  }
}
