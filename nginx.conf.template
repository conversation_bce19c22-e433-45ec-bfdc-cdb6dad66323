server {
  server_name localhost;
  server_tokens off;
  listen 80;
  charset utf-8;
  client_max_body_size 1024M;

  location /api/auth/ {
    proxy_pass $alone_server_url;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection 'Upgrade';
  }
  location /api/platform/ {
    proxy_pass $alone_server_url;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection 'Upgrade';
  }
  location /api/attachment/ {
    proxy_pass $alone_server_url;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection 'Upgrade';
  }
  location /api/log/ {
    proxy_pass $alone_server_url;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection 'Upgrade';
  }
  location /api/message/ {
    proxy_pass $alone_server_url;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection 'Upgrade';
  }
  location /api/monitor/ {
    proxy_pass $alone_server_url;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection 'Upgrade';
  }
  location /api/flow/ {
    proxy_pass $alone_server_url;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection 'Upgrade';
  }
  location /api/online-dev/ {
    proxy_pass $alone_server_url;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection 'Upgrade';
  }

  location / {
    root /usr/share/nginx/html/app-main;
    try_files $uri $uri/ /index.html;
  }

  # 附件中心单体版本
  location /app-attachment-center {
    root /usr/share/nginx/html;
    try_files $uri $uri/ /app-attachment-center/index.html;
  }

  # 日志中心单体版本
  location /app-log-center {
    root /usr/share/nginx/html;
    try_files $uri $uri/ /app-log-center/index.html;
  }

  # 消息中心单体版本
  location /app-message-center {
    root /usr/share/nginx/html;
    try_files $uri $uri/ /app-message-center/index.html;
  }

  # 监控中心单体版本
  location /app-monitor-center {
    root /usr/share/nginx/html;
    try_files $uri $uri/ /app-monitor-center/index.html;
  }

  # 流程中心单体版本
  location /app-flow-center {
    root /usr/share/nginx/html;
    try_files $uri $uri/ /app-flow-center/index.html;
  }

  # 在线开发中心单体版本
  location /app-online-dev-center {
    root /usr/share/nginx/html;
    try_files $uri $uri/ /app-online-dev-center/index.html;
  }

  # 运维监控单体版本
  location /app-easy-monitor {
    root /usr/share/nginx/html;
    try_files $uri $uri/ /app-easy-monitor/index.html;
  }
}
