FROM nginx:1.27.0
COPY dist/ /usr/share/nginx/html/
COPY nginx.conf.template /etc/nginx/templates/default.conf.template
RUN groupadd -r nginx_group && useradd -r -g nginx_group nginx_user
RUN chown -R nginx_user:nginx_group /usr/share/nginx/html/ && \
    chown -R nginx_user:nginx_group /var/cache/nginx && \
    chown -R nginx_user:nginx_group /var/log/nginx && \
    chown -R nginx_user:nginx_group /etc/nginx/conf.d
RUN mkdir -p /var/run/nginx && \
chown nginx_user:nginx_group /var/run/nginx && \
chmod 755 /var/run/nginx
COPY nginx.conf /etc/nginx/nginx.conf
USER nginx_user
EXPOSE 80
