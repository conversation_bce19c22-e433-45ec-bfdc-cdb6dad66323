{"name": "dev-integration-ui", "private": true, "version": "1.0.0", "engines": {"node": "^20.11.0"}, "workspaces": {"packages": ["packages/*"]}, "scripts": {"clean": "rimraf .vscode .turbo node_modules **/**/node_modules", "clean:mac": "rm -rf .vscode .turbo node_modules **/**/node_modules", "serve": "turbo run serve --parallel --no-cache", "serve-alone": "turbo run serve-alone --parallel --no-cache", "serve:main": "turbo run serve --filter=app-main --parallel --no-cache", "build-development-alone": "turbo run build-only-development-alone  --concurrency=2 --no-cache && node scripts/copy-dist.js", "build-release-alone": "turbo run build-only-release-alone  --concurrency=2 --no-cache && node scripts/copy-dist.js", "build-release": "turbo run build-only-release  --concurrency=2 --no-cache", "copy-dist": "node scripts/copy-dist.js", "preinstall": "node scripts/preinstall.js", "type-check": "turbo run type-check --no-cache", "lint": "turbo run lint --no-cache"}, "devDependencies": {"cross-env": "^7.0.3", "minimist": "^1.2.8", "rimraf": "^6.0.1", "shelljs": "^0.10.0", "turbo": "^1.11.3"}}