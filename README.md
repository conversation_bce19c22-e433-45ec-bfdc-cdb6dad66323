# zion-ui

基于 Vue3 + TypeScript + 微前端(无界) 的前端快速开发框架，采用 Monorepo 架构设计，使用 Turborepo 进行项目管理，使用 npm 进行包管理。

## 技术栈

- Node.js: **^18.15.0(推荐)** || >= 18.15.0(支持)
- 包管理器: **npm** (workspace)
- 构建工具: **Vite**
- 基础框架: **Vue.js(@^3)**
- 微前端: **无界**
- 项目管理: **Turborepo**

## 项目结构

```
├── packages/
│   ├── app-main/                 # 主应用 - 研发框架前端核心（必须）
│   │   ├── public/               # 静态资源，不会被 vite 构建，将直接复制到部署根路径
│   │   ├── src/                  # 存放所有源码、资源
│   │   │   ├── apis/             # 后端请求相关二次封装
│   │   │   │   └── requests/     # 存放不同的后端服务对应的 axios 实例
│   │   │   ├── assets/           # 资源文件
│   │   │   ├── components/       # 公共组件
│   │   │   ├── layouts/          # 布局文件
│   │   │   ├── router/           # 路由文件
│   │   │   ├── share/            # 共享资源（主要共享给子应用）
│   │   │   ├── stores/           # Pinia 全局状态管理
│   │   │   ├── utils/            # 一些公共方法
│   │   │   └── views/            # 页面视图文件
│   │   └── types/                # 自定义类型声明（若需要）
│   ├── app-auth/                 # 认证中心（必须）
│   ├── app-common/               # 公共工具（必须）
│   ├── app-attachment-center/    # 附件组件（可选）
│   ├── app-easy-monitor/         # 运维监控组件（可选）
│   ├── app-flow-center/          # 工作流组件（可选）
│   ├── app-log-center/           # 日志组件（可选）
│   ├── app-message-center/       # 消息组件（可选）
│   ├── app-monitor-center/       # 监控组件（可选）
│   └── app-online-dev-center/    # 在线表单组件（可选）
├── scripts/
│   ├── build-alone.js            # 单体版打包脚本
│   └── preinstall.js             # Npm preinstall 钩子脚本
├── nginx.conf.template           # 单体版 Nginx 配置模板（微服务版的配置模板位于各子应用根目录中）
├── package-lock.json             # Npm 依赖版本锁定
├── package.json                  # 项目的各种元信息
└── turbo.json                    # turbo 配置文件
```

## 快速开始

### 基础开发环境

- Node.js >= 18.15.0
- npm >= 9.5.0

`为确保项目依赖管理的一致性和避免潜在冲突，本项目统一使用 npm 作为包管理工具。请勿使用其他包管理工具（如 yarn、pnpm 等）安装依赖，减少 package-lock.json 文件的冲突或其他未知问题。`

### 环境自检，通过命令检查 node 及 npm 版本是否符合要求

```bash
node -v
npm -v
```

### 安装依赖

```bash
npm install
```

### 开发模式（单体版）

```bash
# 一键启动全部应用(根目录)
npm run serve-alone

# 启动单个应用(e.g. app-main)
# 其他应用启动方式类似
cd packages/app-main
npm run serve-alone
```

### 开发模式（微服务版）

```bash
# 一键启动全部应用(根目录)
npm run serve

# 启动单个应用(e.g. app-main)
# 其他应用启动方式类似
cd packages/app-main
npm run serve
```

### 接口服务配置

默认情况下，统一研发框架前端模板默认后端服务地址为 `localhost`, 如果需要配置其他服务地址，请在对应的 ENV 文件中配置以 `_PROXY_BASE_URL` 为后缀的变量。如：

```bash
# 在 app-main 的 .env.development(单体版用 .env.development-alone) 文件中配置
VITE_API_PLATFORM_PROXY_BASE_URL = 'http://${ip}:${port}/'
VITE_API_AUTH_PROXY_BASE_URL = 'http://${ip}:${port}/'
VITE_API_GOVERNANCE_PROXY_BASE_URL = 'http://${ip}:${port}/'
# 其他应用配置方式类似
```

### 构建(单体版)

```bash
# 若需要部署到二级目录等，请修改各子应用中对应的 .env.release-alone 中 VITE_BASIC_PATH 的值

# 一键构建全部应用(根目录)
npm run build-release-alone

# 构建单个应用(e.g. app-main)
# 其他应用构建方式类似
cd packages/app-main
npm run build-only-release-alone
```

### 构建(微服务版)

```bash
# 若需要部署到二级目录等，请修改各子应用中对应的 .env.release 中 VITE_BASIC_PATH 的值

# 构建单个应用(e.g. app-main)
# 其他应用构建方式类似
cd packages/app-main
npm run build-only-release
```

## 其他配置和用法

详见 [https://docs.srdcloud.cn/docs/1lq7MwN7B2IO29Ae/](https://docs.srdcloud.cn/docs/1lq7MwN7B2IO29Ae/)
